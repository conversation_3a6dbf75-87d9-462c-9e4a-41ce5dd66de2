Running Section 4 for target: all users
2025-06-11 20:41:21,047 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-06-11 20:41:21,047 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-06-11 20:41:21,047 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-06-11 20:41:21,048 - INFO - refresh_cache_script - --- <PERSON><PERSON> Refresh Script Started: 2025-06-11T20:41:21.048108 ---
2025-06-11 20:41:21,048 - INFO - refresh_cache_script - Retrieving user credentials...
2025-06-11 20:41:21,233 - INFO - refresh_cache_script - Refreshing section 4 (cms_content) for 30 users.
2025-06-11 20:41:21,234 - INFO - refresh_cache_script - Starting processing for user: youssef.alghrory
2025-06-11 20:41:21,234 - INFO - refresh_cache_script - Processing user: youssef.alghrory for data types: ['cms_content']
2025-06-11 20:41:21,234 - INFO - refresh_cache_script - Processing CMS content section for user: youssef.alghrory
2025-06-11 20:41:21,235 - INFO - scraping.cms - Fetching CMS course list for youssef.alghrory from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,235 - INFO - refresh_cache_script - Starting processing for user: rafaiel.elbaiady
2025-06-11 20:41:21,236 - INFO - refresh_cache_script - Processing user: rafaiel.elbaiady for data types: ['cms_content']
2025-06-11 20:41:21,236 - INFO - refresh_cache_script - Processing CMS content section for user: rafaiel.elbaiady
2025-06-11 20:41:21,236 - INFO - scraping.cms - Fetching CMS course list for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,237 - INFO - refresh_cache_script - Starting processing for user: moustafa.mohamed
2025-06-11 20:41:21,237 - INFO - refresh_cache_script - Processing user: moustafa.mohamed for data types: ['cms_content']
2025-06-11 20:41:21,237 - INFO - refresh_cache_script - Processing CMS content section for user: moustafa.mohamed
2025-06-11 20:41:21,238 - INFO - scraping.cms - Fetching CMS course list for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,239 - INFO - refresh_cache_script - Starting processing for user: maleek.elhodaiby
2025-06-11 20:41:21,239 - INFO - refresh_cache_script - Processing user: maleek.elhodaiby for data types: ['cms_content']
2025-06-11 20:41:21,239 - INFO - refresh_cache_script - Processing CMS content section for user: maleek.elhodaiby
2025-06-11 20:41:21,239 - INFO - scraping.cms - Fetching CMS course list for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,240 - INFO - refresh_cache_script - Starting processing for user: eyad.abdelhafiz
2025-06-11 20:41:21,240 - INFO - refresh_cache_script - Processing user: eyad.abdelhafiz for data types: ['cms_content']
2025-06-11 20:41:21,240 - INFO - refresh_cache_script - Processing CMS content section for user: eyad.abdelhafiz
2025-06-11 20:41:21,241 - INFO - scraping.cms - Fetching CMS course list for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,241 - INFO - refresh_cache_script - Starting processing for user: malak.mohamedelkady
2025-06-11 20:41:21,242 - INFO - refresh_cache_script - Processing user: malak.mohamedelkady for data types: ['cms_content']
2025-06-11 20:41:21,242 - INFO - refresh_cache_script - Processing CMS content section for user: malak.mohamedelkady
2025-06-11 20:41:21,242 - INFO - scraping.cms - Fetching CMS course list for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,243 - INFO - refresh_cache_script - Starting processing for user: seif.elkady
2025-06-11 20:41:21,243 - INFO - refresh_cache_script - Processing user: seif.elkady for data types: ['cms_content']
2025-06-11 20:41:21,243 - INFO - refresh_cache_script - Processing CMS content section for user: seif.elkady
2025-06-11 20:41:21,243 - INFO - scraping.cms - Fetching CMS course list for seif.elkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,244 - INFO - refresh_cache_script - Starting processing for user: nour.tantawi
2025-06-11 20:41:21,244 - INFO - refresh_cache_script - Processing user: nour.tantawi for data types: ['cms_content']
2025-06-11 20:41:21,244 - INFO - refresh_cache_script - Processing CMS content section for user: nour.tantawi
2025-06-11 20:41:21,245 - INFO - scraping.cms - Fetching CMS course list for nour.tantawi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:21,245 - INFO - refresh_cache_script - Starting processing for user: amir.beshay
2025-06-11 20:41:21,246 - INFO - refresh_cache_script - Processing user: amir.beshay for data types: ['cms_content']
2025-06-11 20:41:21,246 - INFO - refresh_cache_script - Processing CMS content section for user: amir.beshay
2025-06-11 20:41:21,246 - INFO - refresh_cache_script - Starting processing for user: mohamed.abouzid
2025-06-11 20:41:21,246 - INFO - refresh_cache_script - Processing user: mohamed.abouzid for data types: ['cms_content']
2025-06-11 20:41:21,246 - INFO - refresh_cache_script - Processing CMS content section for user: mohamed.abouzid
2025-06-11 20:41:25,156 - INFO - scraping.cms - Successfully scraped 8 courses for moustafa.mohamed.
2025-06-11 20:41:25,156 - INFO - scraping.cms - Fetching CMS course list for amir.beshay from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:25,157 - INFO - refresh_cache_script - User moustafa.mohamed has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,157 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,157 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,157 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user moustafa.mohamed needs global refresh.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by moustafa.mohamed.
2025-06-11 20:41:25,158 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:25,158 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,160 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,161 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:25,161 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,162 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,163 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:25,163 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,165 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,166 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:25,166 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,166 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,167 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|EDPT201|) Production Technology (432) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65)
2025-06-11 20:41:25,169 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:25,170 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:25,188 - INFO - scraping.cms - Successfully scraped 8 courses for youssef.alghrory.
2025-06-11 20:41:25,188 - INFO - scraping.cms - Fetching CMS course list for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - User youssef.alghrory has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,189 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,190 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,190 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user youssef.alghrory needs global refresh.
2025-06-11 20:41:25,190 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by youssef.alghrory.
2025-06-11 20:41:25,190 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:25,190 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,192 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,193 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:25,193 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,194 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,195 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:25,195 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:25,197 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:25,197 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:25,198 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:25,199 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:25,200 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:25,201 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,202 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,235 - INFO - scraping.cms - Successfully scraped 7 courses for rafaiel.elbaiady.
2025-06-11 20:41:25,235 - INFO - refresh_cache_script - User rafaiel.elbaiady has 7 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,235 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,235 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Course '(|CIG201|) Civil Engineering Drawing (1555)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user rafaiel.elbaiady needs global refresh.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Awaiting 7 global CMS refresh tasks initiated by rafaiel.elbaiady.
2025-06-11 20:41:25,236 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:25,236 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,238 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,239 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:25,239 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:25,240 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:25,241 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:25,241 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,243 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,244 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:25,244 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,245 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,246 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:25,247 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,247 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,288 - INFO - scraping.cms - Successfully scraped 8 courses for maleek.elhodaiby.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - User maleek.elhodaiby has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,289 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,290 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user maleek.elhodaiby needs global refresh.
2025-06-11 20:41:25,290 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by maleek.elhodaiby.
2025-06-11 20:41:25,290 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:25,290 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,291 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,292 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:25,293 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,294 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,295 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:25,295 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,296 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,297 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:25,298 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:25,299 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:25,300 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|EDPT201|) Production Technology (432) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65)
2025-06-11 20:41:25,300 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:25,302 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:25,621 - INFO - scraping.cms - Successfully scraped 8 courses for eyad.abdelhafiz.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - User eyad.abdelhafiz has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,622 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user eyad.abdelhafiz needs global refresh.
2025-06-11 20:41:25,623 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by eyad.abdelhafiz.
2025-06-11 20:41:25,623 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:25,623 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,624 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,625 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:25,626 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,628 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:25,629 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:25,629 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,630 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,631 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:25,631 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,633 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,633 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|EDPT201|) Production Technology (432) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65)
2025-06-11 20:41:25,634 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:25,635 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:25,668 - INFO - scraping.cms - Successfully scraped 8 courses for seif.elkady.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - User seif.elkady has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user seif.elkady needs global refresh.
2025-06-11 20:41:25,669 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by seif.elkady.
2025-06-11 20:41:25,670 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:25,670 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,671 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:25,672 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:25,672 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:25,673 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:25,674 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:25,675 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,676 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:25,677 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-06-11 20:41:25,677 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:25,678 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:25,678 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:25,680 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,681 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:25,826 - INFO - scraping.cms - Successfully scraped 8 courses for nour.tantawi.
2025-06-11 20:41:25,826 - INFO - refresh_cache_script - User nour.tantawi has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:25,826 - INFO - refresh_cache_script - Course '(|RPW401|) Research Paper Writing (A2) (34)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,826 - INFO - refresh_cache_script - Course '(|BINF405|) Information and Communication Architecture II (486)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,826 - INFO - refresh_cache_script - Course '(|MATH404|) Math IV (1072)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,826 - INFO - refresh_cache_script - Course '(|DE404|) Basic German 4 (73)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,827 - INFO - refresh_cache_script - Course '(|CSEN401|) Computer Programming Lab (402)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,827 - INFO - refresh_cache_script - Course '(|HROB203|) Human Resources Management for BI (2488)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,827 - INFO - refresh_cache_script - Course '(|BINF406|) Digital Transformation (2708)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,827 - INFO - refresh_cache_script - Course '(|CSEN404|) Introduction to Networks (510)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65) for user nour.tantawi needs global refresh.
2025-06-11 20:41:25,827 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by nour.tantawi.
2025-06-11 20:41:25,827 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|RPW401|) Research Paper Writing (A2) (34) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65)
2025-06-11 20:41:25,828 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-06-11 20:41:25,829 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-06-11 20:41:25,830 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF405|) Information and Communication Architecture II (486) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65)
2025-06-11 20:41:25,830 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-06-11 20:41:25,831 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-06-11 20:41:25,832 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH404|) Math IV (1072) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65)
2025-06-11 20:41:25,833 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-06-11 20:41:25,834 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-06-11 20:41:25,835 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE404|) Basic German 4 (73) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65)
2025-06-11 20:41:25,836 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-06-11 20:41:25,838 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-06-11 20:41:25,838 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN401|) Computer Programming Lab (402) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65)
2025-06-11 20:41:25,839 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-06-11 20:41:25,841 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-06-11 20:41:26,098 - INFO - scraping.cms - Successfully scraped 8 courses for malak.mohamedelkady.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - User malak.mohamedelkady has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|CSEN401|) Computer Programming Lab (402)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|BINF405|) Information and Communication Architecture II (486)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|HROB203|) Human Resources Management for BI (2488)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|CSEN404|) Introduction to Networks (510)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|BINF406|) Digital Transformation (2708)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|RPW401|) Research Paper Writing (A2) (34)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|DE404|) Basic German 4 (73)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Course '(|MATH404|) Math IV (1072)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65) for user malak.mohamedelkady needs global refresh.
2025-06-11 20:41:26,099 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by malak.mohamedelkady.
2025-06-11 20:41:26,100 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN401|) Computer Programming Lab (402) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65)
2025-06-11 20:41:26,100 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-06-11 20:41:26,101 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-06-11 20:41:26,101 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF405|) Information and Communication Architecture II (486) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65)
2025-06-11 20:41:26,102 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-06-11 20:41:26,103 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-06-11 20:41:26,104 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|HROB203|) Human Resources Management for BI (2488) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65)
2025-06-11 20:41:26,105 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-06-11 20:41:26,106 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-06-11 20:41:26,106 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN404|) Introduction to Networks (510) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65)
2025-06-11 20:41:26,107 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-06-11 20:41:26,108 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-06-11 20:41:26,109 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF406|) Digital Transformation (2708) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65)
2025-06-11 20:41:26,109 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-06-11 20:41:26,110 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-06-11 20:41:28,155 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:28,155 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:28,156 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:28,156 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:28,156 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:28,156 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:28,156 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:28,156 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:28,157 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:28,335 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:28,364 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:28,522 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:28,523 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:28,770 - INFO - scraping.cms - VOD '3 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0e42cdd4-6e16-81d6-4a3c-93a58aeb26fa' for access URL.
2025-06-11 20:41:28,770 - INFO - scraping.cms - VOD '4 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ee3db23c-2cb9-8fd6-ff8e-2403b39267a9' for access URL.
2025-06-11 20:41:28,772 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 11 weeks.
2025-06-11 20:41:28,772 - WARNING - refresh_cache_script - Newly fetched CMS content for (|EDPT201|) Production Technology (432) is not substantial (empty/minimal content)
2025-06-11 20:41:28,842 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:28,842 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:28,842 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:28,843 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:28,893 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:28,893 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:28,893 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:28,893 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:28,893 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:28,893 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:28,894 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:28,894 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:28,894 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:28,956 - INFO - refresh_cache_script - No existing substantial cache found for (|EDPT201|) Production Technology (432), will cache new data even if minimal
2025-06-11 20:41:29,142 - INFO - utils.cache - Set PICKLE cache for key cms_content:3b7810d96cf07cd7afe4593d27ed4c3e with expiry 3600 seconds
2025-06-11 20:41:29,142 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|EDPT201|) Production Technology (432) (Key: cms_content:3b7810d96cf07cd7afe4593d27ed4c3e)
2025-06-11 20:41:29,142 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:29,142 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:29,144 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:29,162 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:29,162 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:29,214 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:29,214 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:29,288 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:29,345 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:29,407 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:29,520 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:29,521 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:29,521 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:29,521 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:29,529 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:29,529 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:29,529 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:29,530 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:29,531 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:29,682 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:29,683 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:29,867 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:30,050 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:30,051 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:30,051 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-06-11 20:41:30,051 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:30,052 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:30,296 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:30,313 - INFO - scraping.cms - Successfully scraped 7 courses for amir.beshay.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - User amir.beshay has 7 courses. Checking against globally refreshed list.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - Course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,313 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,314 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user amir.beshay needs global refresh.
2025-06-11 20:41:30,314 - INFO - refresh_cache_script - Awaiting 7 global CMS refresh tasks initiated by amir.beshay.
2025-06-11 20:41:30,314 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:30,314 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:30,315 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:30,316 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:30,316 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:30,319 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:30,319 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:30,320 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:30,321 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:30,322 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|EDPT201|) Production Technology (432) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65)
2025-06-11 20:41:30,322 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:30,324 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:30,324 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:30,325 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:30,326 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:30,473 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:30,782 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:30,964 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:31,049 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:31,091 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:41:31,091 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:31,262 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:31,402 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:31,578 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:31,579 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:31,589 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:31,759 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:31,919 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:32,059 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:32,215 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:32,222 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.abouzid.
2025-06-11 20:41:32,260 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:32,260 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:32,260 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:32,261 - INFO - scraping.cms - Fetching CMS course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:32,262 - INFO - scraping.cms - Fetching CMS course announcements for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - User mohamed.abouzid has 8 courses. Checking against globally refreshed list.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user mohamed.abouzid needs global refresh.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by mohamed.abouzid.
2025-06-11 20:41:32,263 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-06-11 20:41:32,264 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:32,265 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:32,266 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:32,266 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:32,267 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:32,268 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:32,269 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:32,270 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:32,270 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-06-11 20:41:32,271 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:32,272 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:32,273 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-06-11 20:41:32,273 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:32,273 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:32,392 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:32,674 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:32,675 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:32,703 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:32,858 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:32,997 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:33,018 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:33,041 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:33,041 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:33,317 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:33,546 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:33,658 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:33,691 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:33,711 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:33,855 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:33,998 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:33,999 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:34,033 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,045 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,055 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:34,194 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,340 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:34,359 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:41:34,360 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,363 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,439 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,539 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:41:34,623 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,674 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:41:34,674 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,682 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:34,682 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:34,682 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:34,682 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:34,683 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:34,684 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:41:34,749 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:41:34,821 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:34,851 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:34,871 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:41:34,975 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:35,014 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:35,056 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:41:35,056 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:41:35,056 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:35,176 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:35,264 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:35,453 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:35,453 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:35,453 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:35,454 - INFO - scraping.cms - Fetching CMS course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:35,455 - INFO - scraping.cms - Fetching CMS course announcements for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:35,481 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:35,645 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:35,757 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:35,758 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:41:35,863 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,017 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,224 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,287 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:41:36,399 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,427 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:36,430 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,488 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:36,488 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:36,489 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:36,489 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:36,489 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:36,489 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:36,489 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:36,489 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:36,490 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,540 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,577 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,600 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,647 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:36,647 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,716 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:36,780 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,789 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:41:36,789 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:41:36,809 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:36,810 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:36,810 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:36,811 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:36,811 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,860 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:36,881 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:36,977 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:41:36,978 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,064 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,102 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:37,128 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:37,129 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:37,167 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,179 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:37,194 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:37,195 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:37,195 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,261 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:41:37,288 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,312 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:37,339 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,431 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,441 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:37,496 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:37,496 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:37,496 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CIG201|) Civil Engineering Drawing (1555) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65)
2025-06-11 20:41:37,497 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65
2025-06-11 20:41:37,498 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65
2025-06-11 20:41:37,512 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,534 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:37,534 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,549 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:37,550 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:37,596 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:37,602 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,648 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,668 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:41:37,668 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,703 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:37,703 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:37,704 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:37,704 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,747 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:37,757 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:37,762 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:37,814 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:41:37,814 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,845 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:41:37,906 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:37,964 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:37,964 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:37,964 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:37,965 - INFO - scraping.cms - Fetching CMS course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:37,966 - INFO - scraping.cms - Fetching CMS course announcements for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:37,968 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:38,007 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:38,133 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,151 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:38,204 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,309 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,313 - INFO - scraping.cms - VOD '3 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0e42cdd4-6e16-81d6-4a3c-93a58aeb26fa' for access URL.
2025-06-11 20:41:38,313 - INFO - scraping.cms - VOD '4 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ee3db23c-2cb9-8fd6-ff8e-2403b39267a9' for access URL.
2025-06-11 20:41:38,314 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 11 weeks.
2025-06-11 20:41:38,315 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,334 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:38,334 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:38,334 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:41:38,356 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:38,416 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,494 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,583 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,648 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,695 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,724 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:41:38,742 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,815 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,855 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,884 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:38,915 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:38,987 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:41:38,987 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,001 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:39,026 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,077 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,150 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,182 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,189 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:39,226 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:41:39,226 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:41:39,226 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:39,234 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,239 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:39,240 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:39,241 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,252 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,346 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,401 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:39,402 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,420 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:39,431 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,434 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:39,462 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,466 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,480 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:39,506 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,560 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:39,618 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:39,619 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:39,619 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:41:39,623 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:39,639 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,721 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:39,721 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:39,721 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:39,722 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,744 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,756 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:39,802 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:41:39,804 - INFO - scraping.cms - VOD '3 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0e42cdd4-6e16-81d6-4a3c-93a58aeb26fa' for access URL.
2025-06-11 20:41:39,805 - INFO - scraping.cms - VOD '4 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ee3db23c-2cb9-8fd6-ff8e-2403b39267a9' for access URL.
2025-06-11 20:41:39,806 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 11 weeks.
2025-06-11 20:41:39,811 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,870 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:39,905 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,932 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:39,985 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:41:39,985 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:41:39,985 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:39,986 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:39,987 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:39,988 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:40,110 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,110 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,171 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:40,232 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:40,276 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:41:40,276 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,278 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,357 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:40,357 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:40,358 - WARNING - refresh_cache_script - Newly fetched CMS content for (|EDPT201|) Production Technology (432) is not substantial (empty/minimal content)
2025-06-11 20:41:40,383 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:40,432 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,438 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:41:40,540 - INFO - refresh_cache_script - No existing substantial cache found for (|EDPT201|) Production Technology (432), will cache new data even if minimal
2025-06-11 20:41:40,585 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,587 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,723 - INFO - utils.cache - Set PICKLE cache for key cms_content:3b7810d96cf07cd7afe4593d27ed4c3e with expiry 3600 seconds
2025-06-11 20:41:40,724 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|EDPT201|) Production Technology (432) (Key: cms_content:3b7810d96cf07cd7afe4593d27ed4c3e)
2025-06-11 20:41:40,724 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:40,744 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:41:40,744 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,754 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:40,907 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:40,921 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:41:41,060 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,090 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:41,090 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:41,091 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:41,227 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,276 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:41,396 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,460 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:41,460 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:41,461 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:41,524 - INFO - scraping.cms - VOD '2 - RPW-  Introduction of the literature review (VoD)' ID '150675_f_1056943' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,572 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,645 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:41,682 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-06-11 20:41:41,689 - INFO - scraping.cms - VOD '4 - RPW- Literature Review (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-51ed902d-c21f-51d3-1e23-de32b20bb82d' for access URL.
2025-06-11 20:41:41,689 - INFO - scraping.cms - VOD '6 - RPW- Methodology Section (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-607130fb-d676-62ac-7bfb-8e2cd817db36' for access URL.
2025-06-11 20:41:41,689 - INFO - scraping.cms - VOD '2 - RPW- Article Review (VoD)' ID '150675_f_1056934' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,725 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:41,829 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:41,829 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:41,829 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:41,830 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:41,831 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:41,832 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:41,832 - INFO - scraping.cms - Fetching CMS course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:41,834 - INFO - scraping.cms - Fetching CMS course announcements for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:41,834 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:41,835 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:41,837 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:41,838 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-06-11 20:41:41,838 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:41,839 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:41,840 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:41,856 - INFO - scraping.cms - VOD '2 - RPW Narrowing Down Steps (Scopus Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-bb767829-e552-91a0-eb6a-990d36e54ed2' for access URL.
2025-06-11 20:41:41,856 - INFO - scraping.cms - VOD '4 - RPW Narrowing Down Steps (EBSCOhost Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-906e2a09-9ac9-17b7-73d2-947aca7308ee' for access URL.
2025-06-11 20:41:41,857 - INFO - scraping.cms - VOD '4 - RPW- Research Terminology (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d8fa6938-9caa-7132-215a-425f77ef607e' for access URL.
2025-06-11 20:41:41,857 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65. Found 5 weeks.
2025-06-11 20:41:41,914 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:41:41,914 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:42,024 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:42,085 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:41:42,208 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:42,208 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:42,209 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:41:42,210 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:42,269 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:42,382 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:42,382 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:42,382 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:42,382 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:42,382 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:42,383 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:42,383 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:42,383 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:42,383 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:42,394 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:41:42,511 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:42,553 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:42,577 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:41:42,577 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:41:42,577 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:41:42,752 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:42,753 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:42,760 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:41:42,868 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:42,943 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:41:42,943 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:41:42,943 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:42,944 - INFO - scraping.cms - Fetching CMS course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:42,945 - INFO - scraping.cms - Fetching CMS course announcements for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:42,946 - WARNING - refresh_cache_script - Newly fetched CMS content for (|RPW401|) Research Paper Writing (A2) (34) is not substantial (empty/minimal content)
2025-06-11 20:41:43,062 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:43,063 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:43,063 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:43,064 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:43,130 - INFO - refresh_cache_script - No existing substantial cache found for (|RPW401|) Research Paper Writing (A2) (34), will cache new data even if minimal
2025-06-11 20:41:43,217 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:43,313 - INFO - utils.cache - Set PICKLE cache for key cms_content:d0dd125bb2295e44175ad44ff1ae6a81 with expiry 3600 seconds
2025-06-11 20:41:43,313 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|RPW401|) Research Paper Writing (A2) (34) (Key: cms_content:d0dd125bb2295e44175ad44ff1ae6a81)
2025-06-11 20:41:43,313 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:41:43,496 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:41:43,608 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-06-11 20:41:43,679 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:41:43,680 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:41:43,680 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:43,801 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:43,863 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:44,046 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:44,046 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:44,047 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:44,230 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:44,306 - INFO - scraping.cms - VOD '4 - Final Revision - Part 1 (VoD)' ID '150675_f_1067515' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:44,417 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:44,417 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:44,417 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|HROB203|) Human Resources Management for BI (2488) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65)
2025-06-11 20:41:44,417 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-06-11 20:41:44,418 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-06-11 20:41:44,419 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:44,420 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:44,420 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:44,421 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:44,423 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:44,424 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:44,425 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:44,485 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:44,608 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:44,616 - INFO - scraping.cms - VOD '5 - Final Revision: Part-2 (VoD)' ID '150675_f_1067508' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:44,701 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-06-11 20:41:44,788 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-06-11 20:41:44,791 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:44,791 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:44,791 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:44,792 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:44,793 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:44,797 - INFO - scraping.cms - VOD '6 - Final Revision: Part-3 (VoD)' ID '150675_f_1067512' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:44,924 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65. Found 12 weeks.
2025-06-11 20:41:45,047 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65. Found 14 weeks.
2025-06-11 20:41:45,127 - INFO - scraping.cms - VOD '3 - Tutorial 11 (ws 11) (VoD)' ID '150675_f_1069055' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:45,181 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 21 weeks.
2025-06-11 20:41:45,181 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ENGD301|) Engineering Drawing & Design (49) is not substantial (empty/minimal content)
2025-06-11 20:41:45,367 - INFO - refresh_cache_script - No existing substantial cache found for (|ENGD301|) Engineering Drawing & Design (49), will cache new data even if minimal
2025-06-11 20:41:45,390 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:45,453 - INFO - scraping.cms - VOD '3 - Tutorial 10 (ws 10) (VoD)' ID '150675_f_1069054' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:45,547 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:45,550 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-06-11 20:41:45,550 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-06-11 20:41:45,550 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:45,579 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-06-11 20:41:45,625 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65. Found 13 weeks.
2025-06-11 20:41:45,665 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:45,680 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:45,704 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:45,739 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:45,758 - INFO - scraping.cms - VOD '3 - Tutorial 9 (ws 9) (VoD)' ID '150675_f_1069048' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:45,789 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:45,854 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:45,855 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:45,856 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:45,862 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:45,923 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:45,924 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:45,924 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN401|) Computer Programming Lab (402) is not substantial (empty/minimal content)
2025-06-11 20:41:45,925 - INFO - scraping.cms - VOD '3 - Tutorial 8 (ws 8) (VoD)' ID '150675_f_1069045' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:46,107 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN401|) Computer Programming Lab (402), will cache new data even if minimal
2025-06-11 20:41:46,175 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:46,176 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:46,234 - INFO - scraping.cms - VOD '3 - Tutorial 7 (ws 7) (VoD)' ID '150675_f_1069042' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:46,295 - INFO - utils.cache - Set PICKLE cache for key cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa with expiry 3600 seconds
2025-06-11 20:41:46,295 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN401|) Computer Programming Lab (402) (Key: cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa)
2025-06-11 20:41:46,296 - WARNING - refresh_cache_script - Newly fetched CMS content for (|BINF405|) Information and Communication Architecture II (486) is not substantial (empty/minimal content)
2025-06-11 20:41:46,412 - INFO - scraping.cms - VOD '2 - Blockchain Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b3790955-857d-ac72-102a-6566cc43a321' for access URL.
2025-06-11 20:41:46,412 - INFO - scraping.cms - VOD '3 - Tutorial 5 VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-58216c6b-b89b-8fcd-d41f-d8c99c564067' for access URL.
2025-06-11 20:41:46,413 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65. Found 11 weeks.
2025-06-11 20:41:46,479 - INFO - refresh_cache_script - No existing substantial cache found for (|BINF405|) Information and Communication Architecture II (486), will cache new data even if minimal
2025-06-11 20:41:46,551 - INFO - scraping.cms - VOD '3 - Tutorial 6 (ws 6) (VoD)' ID '150675_f_1069039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:46,627 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:46,627 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:46,627 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:46,628 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:46,662 - INFO - utils.cache - Set PICKLE cache for key cms_content:2c5a7598a5a727af380c8053ec0cd47d with expiry 3600 seconds
2025-06-11 20:41:46,662 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|BINF405|) Information and Communication Architecture II (486) (Key: cms_content:2c5a7598a5a727af380c8053ec0cd47d)
2025-06-11 20:41:46,662 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:46,748 - INFO - scraping.cms - VOD '4 - Midterm Revision (VoD)' ID '150675_f_1071230' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:46,837 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-06-11 20:41:46,869 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:46,925 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:47,057 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:47,058 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:47,058 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF406|) Digital Transformation (2708) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65)
2025-06-11 20:41:47,058 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-06-11 20:41:47,059 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-06-11 20:41:47,061 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN404|) Introduction to Networks (510) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65)
2025-06-11 20:41:47,061 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-06-11 20:41:47,062 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-06-11 20:41:47,064 - WARNING - refresh_cache_script - Newly fetched CMS content for (|BINF406|) Digital Transformation (2708) is not substantial (empty/minimal content)
2025-06-11 20:41:47,065 - INFO - scraping.cms - VOD '3 - Tutorial 5 (ws 5) (VoD)' ID '150675_f_1071247' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:47,181 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:47,229 - INFO - scraping.cms - VOD '3 - Tutorial 4 (ws 4) (VoD)' ID '150675_f_1067374' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:47,249 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-06-11 20:41:47,253 - INFO - refresh_cache_script - No existing substantial cache found for (|BINF406|) Digital Transformation (2708), will cache new data even if minimal
2025-06-11 20:41:47,437 - INFO - utils.cache - Set PICKLE cache for key cms_content:e379899422f6737123ba21e692610eed with expiry 3600 seconds
2025-06-11 20:41:47,437 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|BINF406|) Digital Transformation (2708) (Key: cms_content:e379899422f6737123ba21e692610eed)
2025-06-11 20:41:47,437 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:47,471 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:47,550 - INFO - scraping.cms - VOD '3 - Tutorial 3 (ws 3) (VoD)' ID '150675_f_1066977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:47,622 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:47,640 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-06-11 20:41:47,654 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:47,779 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-06-11 20:41:47,806 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:47,806 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:47,806 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|RPW401|) Research Paper Writing (A2) (34) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65)
2025-06-11 20:41:47,806 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-06-11 20:41:47,808 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-06-11 20:41:47,809 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-06-11 20:41:47,809 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:47,810 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:47,812 - WARNING - refresh_cache_script - Newly fetched CMS content for (|EDPT201|) Production Technology (432) is not substantial (empty/minimal content)
2025-06-11 20:41:47,906 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65. Found 16 weeks.
2025-06-11 20:41:47,960 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:47,998 - INFO - refresh_cache_script - No existing substantial cache found for (|EDPT201|) Production Technology (432), will cache new data even if minimal
2025-06-11 20:41:48,104 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:48,181 - INFO - utils.cache - Set PICKLE cache for key cms_content:3b7810d96cf07cd7afe4593d27ed4c3e with expiry 3600 seconds
2025-06-11 20:41:48,181 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|EDPT201|) Production Technology (432) (Key: cms_content:3b7810d96cf07cd7afe4593d27ed4c3e)
2025-06-11 20:41:48,181 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN404|) Introduction to Networks (510) is not substantial (empty/minimal content)
2025-06-11 20:41:48,245 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65. Found 14 weeks.
2025-06-11 20:41:48,260 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:48,313 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65. Found 12 weeks.
2025-06-11 20:41:48,365 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN404|) Introduction to Networks (510), will cache new data even if minimal
2025-06-11 20:41:48,551 - INFO - utils.cache - Set PICKLE cache for key cms_content:a0c90cd039e29094137c8bf4387596bb with expiry 3600 seconds
2025-06-11 20:41:48,551 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN404|) Introduction to Networks (510) (Key: cms_content:a0c90cd039e29094137c8bf4387596bb)
2025-06-11 20:41:48,552 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:48,578 - INFO - scraping.cms - Finished parsing course content for moustafa.mohamed from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:41:48,679 - INFO - scraping.cms - VOD '1 - Revision Part I (VoD)' ID '150675_f_1123696' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:48,735 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:48,835 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:48,920 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:48,920 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:48,920 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE404|) Basic German 4 (73) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65)
2025-06-11 20:41:48,921 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-06-11 20:41:48,922 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-06-11 20:41:48,923 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:48,923 - INFO - scraping.cms - Fetching CMS course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:48,924 - INFO - scraping.cms - Fetching CMS course announcements for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:48,926 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH404|) Math IV (1072) is not substantial (empty/minimal content)
2025-06-11 20:41:48,950 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:48,980 - INFO - scraping.cms - VOD '1 - Lektion 24 Video 1 (VoD)' ID '150675_f_1073495' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:49,115 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH404|) Math IV (1072), will cache new data even if minimal
2025-06-11 20:41:49,131 - INFO - scraping.cms - VOD '2 - Lektion 24 Video 1 Komplett (VoD)' ID '150675_f_1073500' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:49,300 - INFO - utils.cache - Set PICKLE cache for key cms_content:24ac4b2ef071b508afcbda016918c8cc with expiry 3600 seconds
2025-06-11 20:41:49,300 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH404|) Math IV (1072) (Key: cms_content:24ac4b2ef071b508afcbda016918c8cc)
2025-06-11 20:41:49,300 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:49,309 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65. Found 13 weeks.
2025-06-11 20:41:49,435 - INFO - scraping.cms - VOD '3 - Lektion 24 Video 2 Komplett (VoD)' ID '150675_f_1073502' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:49,452 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:49,486 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:49,558 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:49,599 - INFO - scraping.cms - VOD '1 - Lektion 23 Video 1 (VoD)' ID '150675_f_1073450' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:49,669 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:41:49,669 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:41:49,669 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN401|) Computer Programming Lab (402) is not substantial (empty/minimal content)
2025-06-11 20:41:49,723 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-06-11 20:41:49,838 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 21 weeks.
2025-06-11 20:41:49,852 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN401|) Computer Programming Lab (402), will cache new data even if minimal
2025-06-11 20:41:49,911 - INFO - scraping.cms - VOD '2 - Lektion 23 Video 2 (VoD)' ID '150675_f_1073453' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:49,972 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:50,036 - INFO - utils.cache - Set PICKLE cache for key cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa with expiry 3600 seconds
2025-06-11 20:41:50,036 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN401|) Computer Programming Lab (402) (Key: cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa)
2025-06-11 20:41:50,037 - WARNING - refresh_cache_script - Newly fetched CMS content for (|HROB203|) Human Resources Management for BI (2488) is not substantial (empty/minimal content)
2025-06-11 20:41:50,071 - INFO - scraping.cms - VOD '3 - Lektion 23 Video 3 (VoD)' ID '150675_f_1073456' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:50,220 - INFO - refresh_cache_script - No existing substantial cache found for (|HROB203|) Human Resources Management for BI (2488), will cache new data even if minimal
2025-06-11 20:41:50,247 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-06-11 20:41:50,252 - INFO - scraping.cms - VOD '4 - Lektion 23 Video 1 Komplett (VoD)' ID '150675_f_1073474' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:50,260 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:50,271 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:50,358 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:50,404 - INFO - utils.cache - Set PICKLE cache for key cms_content:9a92aa6c600a4f2afd5779272fb47030 with expiry 3600 seconds
2025-06-11 20:41:50,404 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|HROB203|) Human Resources Management for BI (2488) (Key: cms_content:9a92aa6c600a4f2afd5779272fb47030)
2025-06-11 20:41:50,404 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:41:50,425 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:50,438 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:50,554 - INFO - scraping.cms - VOD '5 - Lektion 23 Video 2 Komplett (VoD)' ID '150675_f_1073481' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:50,689 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:50,705 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:50,772 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:41:50,828 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65
2025-06-11 20:41:50,880 - INFO - scraping.cms - VOD '6 - Lektion 23 Video 3 Komplett (VoD)' ID '150675_f_1073486' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,037 - INFO - scraping.cms - VOD '7 - Lektion 23 Video 4 Komplett (VoD)' ID '150675_f_1073485' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,095 - INFO - scraping.cms - Finished parsing course content for rafaiel.elbaiady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65. Found 4 weeks.
2025-06-11 20:41:51,271 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:41:51,271 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:41:51,271 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH404|) Math IV (1072) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65)
2025-06-11 20:41:51,272 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-06-11 20:41:51,273 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-06-11 20:41:51,274 - WARNING - refresh_cache_script - Newly fetched CMS content for (|BINF405|) Information and Communication Architecture II (486) is not substantial (empty/minimal content)
2025-06-11 20:41:51,289 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:51,292 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:51,339 - INFO - scraping.cms - VOD '1 - Lektion 22 Video 1 (VoD)' ID '150675_f_1073442' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,353 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-06-11 20:41:51,353 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-06-11 20:41:51,353 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-06-11 20:41:51,353 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-06-11 20:41:51,353 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-06-11 20:41:51,354 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-06-11 20:41:51,354 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-06-11 20:41:51,354 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:51,354 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,459 - INFO - refresh_cache_script - No existing substantial cache found for (|BINF405|) Information and Communication Architecture II (486), will cache new data even if minimal
2025-06-11 20:41:51,494 - INFO - scraping.cms - VOD '2 - Lektion 22 Video 2 (VoD)' ID '150675_f_1073444' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,507 - INFO - scraping.cms - Finished parsing course content for youssef.alghrory from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:51,518 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-06-11 20:41:51,519 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,643 - INFO - utils.cache - Set PICKLE cache for key cms_content:2c5a7598a5a727af380c8053ec0cd47d with expiry 3600 seconds
2025-06-11 20:41:51,643 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|BINF405|) Information and Communication Architecture II (486) (Key: cms_content:2c5a7598a5a727af380c8053ec0cd47d)
2025-06-11 20:41:51,643 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ENGD301|) Engineering Drawing & Design (49) is not substantial (empty/minimal content)
2025-06-11 20:41:51,656 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-06-11 20:41:51,671 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:51,675 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:51,805 - INFO - scraping.cms - VOD '3 - Lektion 22 complete video 1 (VoD)' ID '150675_f_1073448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,825 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-06-11 20:41:51,826 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-06-11 20:41:51,826 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-06-11 20:41:51,826 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:51,829 - INFO - refresh_cache_script - No existing substantial cache found for (|ENGD301|) Engineering Drawing & Design (49), will cache new data even if minimal
2025-06-11 20:41:51,948 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:51,984 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-06-11 20:41:52,016 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-06-11 20:41:52,016 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-06-11 20:41:52,016 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:52,048 - INFO - scraping.cms - VOD '3 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0e42cdd4-6e16-81d6-4a3c-93a58aeb26fa' for access URL.
2025-06-11 20:41:52,048 - INFO - scraping.cms - VOD '4 - Turning Tutorial Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ee3db23c-2cb9-8fd6-ff8e-2403b39267a9' for access URL.
2025-06-11 20:41:52,050 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 11 weeks.
2025-06-11 20:41:52,122 - INFO - scraping.cms - VOD '4 - Lektion 22 complete video 2 (VoD)' ID '150675_f_1101615' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:52,200 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:52,280 - INFO - scraping.cms - VOD '1 - Unit 21 Part 1 (VoD)' ID '150675_f_1063834' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:52,306 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-06-11 20:41:52,388 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:41:52,388 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:41:52,388 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:52,439 - INFO - scraping.cms - VOD '2 - Unit 21 Part 2 (VoD)' ID '150675_f_1067572' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:52,571 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:52,684 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:52,756 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:52,756 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:52,757 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:52,759 - INFO - scraping.cms - VOD '3 - Unit 21 (Complete Unit: Part 1) (VoD)' ID '150675_f_1094812' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:52,842 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:41:52,916 - INFO - scraping.cms - VOD '4 - Unit 21 (Complete Unit: Part 2) (VoD)' ID '150675_f_1094820' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:52,941 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:53,124 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:41:53,124 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:41:53,125 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:53,224 - INFO - scraping.cms - VOD '5 - Unit 21 (Complete Unit: Part 3) (VoD)' ID '150675_f_1094828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:53,308 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:53,431 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-06-11 20:41:53,493 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:53,493 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:53,493 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CIG201|) Civil Engineering Drawing (1555) is not substantial (empty/minimal content)
2025-06-11 20:41:53,525 - INFO - scraping.cms - VOD '1 - Unit 20 (VoD)' ID '150675_f_1063828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:53,560 - INFO - scraping.cms - VOD '6 - Sheet 12:   AC circuits  VOD (VoD)' ID '150675_f_1130729' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:53,676 - INFO - refresh_cache_script - No existing substantial cache found for (|CIG201|) Civil Engineering Drawing (1555), will cache new data even if minimal
2025-06-11 20:41:53,717 - INFO - scraping.cms - VOD '2 - Unit 20 (Complete Unit) (VoD)' ID '150675_f_1092097' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:53,750 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 14 weeks.
2025-06-11 20:41:53,859 - INFO - utils.cache - Set PICKLE cache for key cms_content:296a5a13268d777b94051c3a7a09ea7e with expiry 3600 seconds
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CIG201|) Civil Engineering Drawing (1555) (Key: cms_content:296a5a13268d777b94051c3a7a09ea7e)
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by moustafa.mohamed) successful.
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - CMS Content processing summary for user moustafa.mohamed: Processed for user moustafa.mohamed: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:41:53,859 - INFO - refresh_cache_script - Finished processing for user: moustafa.mohamed
2025-06-11 20:41:53,860 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-06-11 20:41:53,860 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:53,861 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:53,862 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-06-11 20:41:53,862 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:53,863 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:53,864 - INFO - refresh_cache_script - Starting processing for user: nouralhoda.alseufy
2025-06-11 20:41:53,864 - INFO - refresh_cache_script - Processing user: nouralhoda.alseufy for data types: ['cms_content']
2025-06-11 20:41:53,864 - INFO - refresh_cache_script - Processing CMS content section for user: nouralhoda.alseufy
2025-06-11 20:41:53,865 - INFO - scraping.cms - Fetching CMS course list for nouralhoda.alseufy from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:53,866 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:53,874 - INFO - scraping.cms - VOD '3 - K19 - V1 (VoD)' ID '150675_f_1062891' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:54,019 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:54,031 - INFO - scraping.cms - VOD '4 - Unit 19 Complete Unit (Part1) (VoD)' ID '150675_f_1066246' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:54,049 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:54,231 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:41:54,231 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:41:54,232 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:54,320 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:54,361 - INFO - scraping.cms - VOD '5 - Unit 19 Complete Unit (Part 2) (VoD)' ID '150675_f_1066249' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:54,416 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:54,603 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 21 weeks.
2025-06-11 20:41:54,603 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:54,603 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:54,603 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:54,685 - INFO - scraping.cms - VOD '1 - Revision 1 (VoD)' ID '150675_f_1124540' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:54,790 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:54,972 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:41:54,972 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:41:54,973 - WARNING - refresh_cache_script - Newly fetched CMS content for (|SM101|) Scientific Methods (A1) (16) is not substantial (empty/minimal content)
2025-06-11 20:41:55,022 - INFO - scraping.cms - VOD '2 - Revision 2 (VoD)' ID '150675_f_1124541' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,158 - INFO - refresh_cache_script - No existing substantial cache found for (|SM101|) Scientific Methods (A1) (16), will cache new data even if minimal
2025-06-11 20:41:55,257 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,322 - INFO - scraping.cms - VOD '4 - Model test DE3 AUDIO (VoD)' ID '150675_f_1124235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,341 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-06-11 20:41:55,341 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-06-11 20:41:55,342 - WARNING - refresh_cache_script - Newly fetched CMS content for (|EDPT201|) Production Technology (432) is not substantial (empty/minimal content)
2025-06-11 20:41:55,372 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:55,382 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:55,424 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:55,468 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:41:55,483 - INFO - scraping.cms - VOD '1 - U18-V1 Blended (VoD)' ID '150675_f_1090464' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,526 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,527 - INFO - refresh_cache_script - No existing substantial cache found for (|EDPT201|) Production Technology (432), will cache new data even if minimal
2025-06-11 20:41:55,568 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 14 weeks.
2025-06-11 20:41:55,593 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-06-11 20:41:55,683 - INFO - scraping.cms - VOD '2 - U18-V2 Blended (VoD)' ID '150675_f_1090082' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,712 - INFO - utils.cache - Set PICKLE cache for key cms_content:3b7810d96cf07cd7afe4593d27ed4c3e with expiry 3600 seconds
2025-06-11 20:41:55,712 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|EDPT201|) Production Technology (432) (Key: cms_content:3b7810d96cf07cd7afe4593d27ed4c3e)
2025-06-11 20:41:55,713 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:41:55,757 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:55,836 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:55,987 - INFO - scraping.cms - VOD '3 - U18-V1 Complete (VoD)' ID '150675_f_1090084' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,029 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65. Found 12 weeks.
2025-06-11 20:41:56,056 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:41:56,154 - INFO - scraping.cms - VOD '4 - U18-V2 Complete (VoD)' ID '150675_f_1090083' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,177 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,293 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:41:56,293 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:41:56,294 - WARNING - refresh_cache_script - Newly fetched CMS content for (|PHYS202|) Physics II (450) is not substantial (empty/minimal content)
2025-06-11 20:41:56,303 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,343 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:41:56,343 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,357 - INFO - scraping.cms - VOD '1 - U17- V1 Blended (VoD)' ID '150675_f_1090058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,501 - INFO - refresh_cache_script - No existing substantial cache found for (|PHYS202|) Physics II (450), will cache new data even if minimal
2025-06-11 20:41:56,518 - INFO - scraping.cms - VOD '2 - U17-V2 Blended (VoD)' ID '150675_f_1090062' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,607 - INFO - scraping.cms - Finished parsing course content for maleek.elhodaiby from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:41:56,657 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,684 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,684 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1555&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,685 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by rafaiel.elbaiady) successful.
2025-06-11 20:41:56,685 - INFO - refresh_cache_script - CMS Content processing summary for user rafaiel.elbaiady: Processed for user rafaiel.elbaiady: Globally Updated Now=7, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:41:56,685 - INFO - refresh_cache_script - Finished processing for user: rafaiel.elbaiady
2025-06-11 20:41:56,685 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-06-11 20:41:56,685 - INFO - scraping.cms - Fetching CMS course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:56,686 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:56,686 - INFO - refresh_cache_script - Starting processing for user: abdullah.salousa
2025-06-11 20:41:56,688 - INFO - refresh_cache_script - Processing user: abdullah.salousa for data types: ['cms_content']
2025-06-11 20:41:56,688 - INFO - refresh_cache_script - Processing CMS content section for user: abdullah.salousa
2025-06-11 20:41:56,688 - INFO - scraping.cms - Fetching CMS course list for abdullah.salousa from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:56,689 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:56,690 - INFO - scraping.cms - VOD '3 - U17-V3 Blended (VoD)' ID '150675_f_1090064' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,857 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:41:56,862 - INFO - scraping.cms - VOD '4 - U17 - V1 Complete (VoD)' ID '150675_f_1090063' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:56,880 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:56,911 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-06-11 20:41:56,952 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,027 - INFO - scraping.cms - VOD '5 - U17 - V2 Complete (VoD)' ID '150675_f_1090067' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,067 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by youssef.alghrory) successful.
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - CMS Content processing summary for user youssef.alghrory: Processed for user youssef.alghrory: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:41:57,067 - INFO - refresh_cache_script - Finished processing for user: youssef.alghrory
2025-06-11 20:41:57,068 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ENGD301|) Engineering Drawing & Design (49) is not substantial (empty/minimal content)
2025-06-11 20:41:57,192 - INFO - scraping.cms - VOD '6 - U17-V3 Complete (VoD)' ID '150675_f_1090071' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,254 - INFO - refresh_cache_script - No existing substantial cache found for (|ENGD301|) Engineering Drawing & Design (49), will cache new data even if minimal
2025-06-11 20:41:57,266 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,461 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-06-11 20:41:57,461 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-06-11 20:41:57,461 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:41:57,503 - INFO - scraping.cms - VOD '1 - U16- V1 Blended (VoD)' ID '150675_f_1090053' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,597 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,644 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:41:57,667 - INFO - scraping.cms - VOD '2 - U16- V1 complete (VoD)' ID '150675_f_1090057' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,716 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:41:57,796 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:57,828 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:41:57,828 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:41:57,828 - WARNING - refresh_cache_script - Newly fetched CMS content for (|ELCT201|) Digital Logic Design (79) is not substantial (empty/minimal content)
2025-06-11 20:41:57,833 - INFO - scraping.cms - VOD '3 - U16-V2 complete (VoD)' ID '150675_f_1090462' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,849 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:57,906 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:57,992 - INFO - scraping.cms - VOD '1 - U13-complete Video1 (VoD)' ID '150675_f_1065580' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,013 - INFO - refresh_cache_script - No existing substantial cache found for (|ELCT201|) Digital Logic Design (79), will cache new data even if minimal
2025-06-11 20:41:58,059 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,085 - INFO - scraping.cms - VOD '2 - Blockchain Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b3790955-857d-ac72-102a-6566cc43a321' for access URL.
2025-06-11 20:41:58,085 - INFO - scraping.cms - VOD '3 - Tutorial 5 VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-58216c6b-b89b-8fcd-d41f-d8c99c564067' for access URL.
2025-06-11 20:41:58,086 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65. Found 11 weeks.
2025-06-11 20:41:58,117 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-06-11 20:41:58,132 - INFO - scraping.cms - VOD '2 - RPW-  Introduction of the literature review (VoD)' ID '150675_f_1056943' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,177 - INFO - scraping.cms - VOD '2 - U13- complete Video2 (VoD)' ID '150675_f_1065582' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,197 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-06-11 20:41:58,197 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-06-11 20:41:58,197 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:41:58,231 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,287 - INFO - scraping.cms - VOD '4 - RPW- Literature Review (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-51ed902d-c21f-51d3-1e23-de32b20bb82d' for access URL.
2025-06-11 20:41:58,287 - INFO - scraping.cms - VOD '6 - RPW- Methodology Section (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-607130fb-d676-62ac-7bfb-8e2cd817db36' for access URL.
2025-06-11 20:41:58,288 - INFO - scraping.cms - VOD '2 - RPW- Article Review (VoD)' ID '150675_f_1056934' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,439 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:41:58,452 - INFO - scraping.cms - VOD '2 - RPW Narrowing Down Steps (Scopus Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-bb767829-e552-91a0-eb6a-990d36e54ed2' for access URL.
2025-06-11 20:41:58,453 - INFO - scraping.cms - VOD '4 - RPW Narrowing Down Steps (EBSCOhost Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-906e2a09-9ac9-17b7-73d2-947aca7308ee' for access URL.
2025-06-11 20:41:58,453 - INFO - scraping.cms - VOD '4 - RPW- Research Terminology (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d8fa6938-9caa-7132-215a-425f77ef607e' for access URL.
2025-06-11 20:41:58,454 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65. Found 5 weeks.
2025-06-11 20:41:58,483 - INFO - scraping.cms - VOD '1 - U15-V1 (VoD)' ID '150675_f_1065068' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,609 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,618 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:58,653 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:41:58,653 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:41:58,653 - INFO - refresh_cache_script - Starting processing for user: hana.bediar
2025-06-11 20:41:58,653 - INFO - refresh_cache_script - Processing user: hana.bediar for data types: ['cms_content']
2025-06-11 20:41:58,653 - INFO - refresh_cache_script - Processing CMS content section for user: hana.bediar
2025-06-11 20:41:58,654 - INFO - scraping.cms - Fetching CMS course list for hana.bediar from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:41:58,655 - WARNING - refresh_cache_script - Newly fetched CMS content for (|HROB203|) Human Resources Management for BI (2488) is not substantial (empty/minimal content)
2025-06-11 20:41:58,670 - INFO - scraping.cms - VOD '2 - U16-V1 (VoD)' ID '150675_f_1065069' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,780 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,831 - INFO - scraping.cms - VOD '3 - U15 V1 Complete (VoD)' ID '150675_f_1141997' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,839 - INFO - refresh_cache_script - No existing substantial cache found for (|HROB203|) Human Resources Management for BI (2488), will cache new data even if minimal
2025-06-11 20:41:58,870 - INFO - scraping.cms - Finished parsing course content for amir.beshay from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 13 weeks.
2025-06-11 20:41:58,949 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:58,952 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65. Found 12 weeks.
2025-06-11 20:41:59,001 - INFO - scraping.cms - VOD '1 - U14 - V1 (VoD)' ID '150675_f_1063454' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,029 - INFO - utils.cache - Set PICKLE cache for key cms_content:9a92aa6c600a4f2afd5779272fb47030 with expiry 3600 seconds
2025-06-11 20:41:59,029 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|HROB203|) Human Resources Management for BI (2488) (Key: cms_content:9a92aa6c600a4f2afd5779272fb47030)
2025-06-11 20:41:59,030 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:59,165 - INFO - scraping.cms - VOD '2 - U14- V2 (VoD)' ID '150675_f_1065039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,212 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:59,268 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,384 - INFO - scraping.cms - VOD '3 - U14-V3 (VoD)' ID '150675_f_1065060' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,395 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:41:59,395 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:41:59,395 - WARNING - refresh_cache_script - Newly fetched CMS content for (|RPW401|) Research Paper Writing (A2) (34) is not substantial (empty/minimal content)
2025-06-11 20:41:59,433 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,557 - INFO - scraping.cms - VOD '4 - U14-V4 (VoD)' ID '150675_f_1065058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,577 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,579 - INFO - refresh_cache_script - No existing substantial cache found for (|RPW401|) Research Paper Writing (A2) (34), will cache new data even if minimal
2025-06-11 20:41:59,609 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-06-11 20:41:59,630 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-06-11 20:41:59,651 - INFO - scraping.cms - Successfully scraped 8 courses for nouralhoda.alseufy.
2025-06-11 20:41:59,660 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-06-11 20:41:59,716 - INFO - scraping.cms - VOD '5 - U14 Complete Video (VoD)' ID '150675_f_1141802' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,757 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,762 - INFO - utils.cache - Set PICKLE cache for key cms_content:d0dd125bb2295e44175ad44ff1ae6a81 with expiry 3600 seconds
2025-06-11 20:41:59,762 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|RPW401|) Research Paper Writing (A2) (34) (Key: cms_content:d0dd125bb2295e44175ad44ff1ae6a81)
2025-06-11 20:41:59,762 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:41:59,903 - INFO - scraping.cms - Finished parsing course content for eyad.abdelhafiz from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:41:59,926 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:41:59,926 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:41:59,947 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:41:59,952 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:00,021 - INFO - scraping.cms - VOD '6 - U14 Complete V2 (VoD)' ID '150675_f_1141799' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:00,024 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-06-11 20:42:00,130 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:42:00,132 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by maleek.elhodaiby) successful.
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - CMS Content processing summary for user maleek.elhodaiby: Processed for user maleek.elhodaiby: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Finished processing for user: maleek.elhodaiby
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Starting processing for user: seif.abdelkader
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Processing user: seif.abdelkader for data types: ['cms_content']
2025-06-11 20:42:00,132 - INFO - refresh_cache_script - Processing CMS content section for user: seif.abdelkader
2025-06-11 20:42:00,133 - INFO - scraping.cms - Fetching CMS course list for seif.abdelkader from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:00,134 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN202|) Introduction to Computer Programming (19) is not substantial (empty/minimal content)
2025-06-11 20:42:00,146 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:42:00,189 - INFO - scraping.cms - VOD '1 - K13- V1 (VoD)' ID '150675_f_1062824' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:00,233 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-06-11 20:42:00,276 - INFO - scraping.cms - Successfully scraped 8 courses for abdullah.salousa.
2025-06-11 20:42:00,318 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN202|) Introduction to Computer Programming (19), will cache new data even if minimal
2025-06-11 20:42:00,367 - INFO - scraping.cms - VOD '2 - K13 - V2 (VoD)' ID '150675_f_1062876' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:00,503 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-06-11 20:42:00,503 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-06-11 20:42:00,503 - WARNING - refresh_cache_script - Newly fetched CMS content for Engineering - 2nd Orientation course is not substantial (empty/minimal content)
2025-06-11 20:42:00,675 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:00,686 - INFO - refresh_cache_script - No existing substantial cache found for Engineering - 2nd Orientation course, will cache new data even if minimal
2025-06-11 20:42:00,855 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:00,868 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-06-11 20:42:00,868 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-06-11 20:42:00,869 - WARNING - refresh_cache_script - Newly fetched CMS content for (|BINF406|) Digital Transformation (2708) is not substantial (empty/minimal content)
2025-06-11 20:42:01,028 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,051 - INFO - refresh_cache_script - No existing substantial cache found for (|BINF406|) Digital Transformation (2708), will cache new data even if minimal
2025-06-11 20:42:01,187 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:42:01,187 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,235 - INFO - utils.cache - Set PICKLE cache for key cms_content:e379899422f6737123ba21e692610eed with expiry 3600 seconds
2025-06-11 20:42:01,235 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|BINF406|) Digital Transformation (2708) (Key: cms_content:e379899422f6737123ba21e692610eed)
2025-06-11 20:42:01,235 - INFO - refresh_cache_script - User nouralhoda.alseufy has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:01,235 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,235 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,235 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,235 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,236 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,236 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,236 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,236 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user nouralhoda.alseufy.
2025-06-11 20:42:01,236 - INFO - refresh_cache_script - CMS Content processing summary for user nouralhoda.alseufy: Processed for user nouralhoda.alseufy: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:01,236 - INFO - refresh_cache_script - Finished processing for user: nouralhoda.alseufy
2025-06-11 20:42:01,236 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:42:01,268 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,429 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,469 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:42:01,491 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,631 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,679 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:42:01,680 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:42:01,680 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:42:01,689 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-06-11 20:42:01,802 - INFO - scraping.cms - Successfully scraped 8 courses for hana.bediar.
2025-06-11 20:42:01,813 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:42:01,813 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,815 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:01,922 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:42:02,001 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,121 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,139 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:42:02,139 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:42:02,139 - INFO - refresh_cache_script - Starting processing for user: jana.hamed
2025-06-11 20:42:02,139 - INFO - refresh_cache_script - Processing user: jana.hamed for data types: ['cms_content']
2025-06-11 20:42:02,139 - INFO - refresh_cache_script - Processing CMS content section for user: jana.hamed
2025-06-11 20:42:02,140 - INFO - scraping.cms - Fetching CMS course list for jana.hamed from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:02,141 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:42:02,299 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,325 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:42:02,339 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,502 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,513 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:42:02,513 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:42:02,513 - WARNING - refresh_cache_script - Newly fetched CMS content for (|CSEN404|) Introduction to Networks (510) is not substantial (empty/minimal content)
2025-06-11 20:42:02,566 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,617 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,666 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-06-11 20:42:02,696 - INFO - refresh_cache_script - No existing substantial cache found for (|CSEN404|) Introduction to Networks (510), will cache new data even if minimal
2025-06-11 20:42:02,732 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-06-11 20:42:02,795 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,833 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:02,879 - INFO - utils.cache - Set PICKLE cache for key cms_content:a0c90cd039e29094137c8bf4387596bb with expiry 3600 seconds
2025-06-11 20:42:02,879 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|CSEN404|) Introduction to Networks (510) (Key: cms_content:a0c90cd039e29094137c8bf4387596bb)
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - User abdullah.salousa has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user abdullah.salousa.
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - CMS Content processing summary for user abdullah.salousa: Processed for user abdullah.salousa: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - Finished processing for user: abdullah.salousa
2025-06-11 20:42:02,880 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by amir.beshay) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - CMS Content processing summary for user amir.beshay: Processed for user amir.beshay: Globally Updated Now=7, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Finished processing for user: amir.beshay
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by eyad.abdelhafiz) successful.
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - CMS Content processing summary for user eyad.abdelhafiz: Processed for user eyad.abdelhafiz: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - Finished processing for user: eyad.abdelhafiz
2025-06-11 20:42:02,881 - INFO - refresh_cache_script - User hana.bediar has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user hana.bediar.
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - CMS Content processing summary for user hana.bediar: Processed for user hana.bediar: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Finished processing for user: hana.bediar
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Starting processing for user: emadeldin.senousy
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Processing user: emadeldin.senousy for data types: ['cms_content']
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Processing CMS content section for user: emadeldin.senousy
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Starting processing for user: mariam.hosam
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Processing user: mariam.hosam for data types: ['cms_content']
2025-06-11 20:42:02,882 - INFO - refresh_cache_script - Processing CMS content section for user: mariam.hosam
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Starting processing for user: ali.araffa
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Processing user: ali.araffa for data types: ['cms_content']
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Processing CMS content section for user: ali.araffa
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Starting processing for user: gelan.hassan
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Processing user: gelan.hassan for data types: ['cms_content']
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Processing CMS content section for user: gelan.hassan
2025-06-11 20:42:02,883 - INFO - scraping.cms - Fetching CMS course list for emadeldin.senousy from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:02,883 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,884 - INFO - scraping.cms - Fetching CMS course list for mariam.hosam from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:02,885 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,885 - INFO - scraping.cms - Fetching CMS course list for ali.araffa from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:02,886 - INFO - scraping.cms - Fetching CMS course list for gelan.hassan from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:02,886 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,888 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,888 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,888 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,888 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,889 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by seif.elkady) successful.
2025-06-11 20:42:02,889 - INFO - refresh_cache_script - CMS Content processing summary for user seif.elkady: Processed for user seif.elkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:02,889 - INFO - refresh_cache_script - Finished processing for user: seif.elkady
2025-06-11 20:42:02,889 - INFO - refresh_cache_script - Starting processing for user: khadija.mohamed
2025-06-11 20:42:02,889 - INFO - refresh_cache_script - Processing user: khadija.mohamed for data types: ['cms_content']
2025-06-11 20:42:02,889 - INFO - refresh_cache_script - Processing CMS content section for user: khadija.mohamed
2025-06-11 20:42:02,889 - INFO - scraping.cms - Fetching CMS course list for khadija.mohamed from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:02,895 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 14 weeks.
2025-06-11 20:42:02,896 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH203|) Mathematics I (17) is not substantial (empty/minimal content)
2025-06-11 20:42:02,969 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,008 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,129 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,129 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH203|) Mathematics I (17), will cache new data even if minimal
2025-06-11 20:42:03,150 - INFO - scraping.cms - VOD '4 - Final Revision - Part 1 (VoD)' ID '150675_f_1067515' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,180 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,227 - INFO - scraping.cms - Successfully scraped 8 courses for seif.abdelkader.
2025-06-11 20:42:03,280 - INFO - scraping.cms - VOD '1 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,342 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,343 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-06-11 20:42:03,343 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - User seif.abdelkader has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user seif.abdelkader.
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - CMS Content processing summary for user seif.abdelkader: Processed for user seif.abdelkader: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - Finished processing for user: seif.abdelkader
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - Starting processing for user: Khaled.abdelwaged
2025-06-11 20:42:03,344 - INFO - refresh_cache_script - Processing user: Khaled.abdelwaged for data types: ['cms_content']
2025-06-11 20:42:03,345 - INFO - refresh_cache_script - Processing CMS content section for user: Khaled.abdelwaged
2025-06-11 20:42:03,345 - INFO - scraping.cms - Fetching CMS course list for Khaled.abdelwaged from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:03,456 - INFO - scraping.cms - VOD '5 - Final Revision: Part-2 (VoD)' ID '150675_f_1067508' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,578 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,716 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,774 - INFO - scraping.cms - VOD '6 - Final Revision: Part-3 (VoD)' ID '150675_f_1067512' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,882 - INFO - scraping.cms - VOD '3 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,894 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:03,956 - INFO - scraping.cms - VOD '3 - Tutorial 11 (ws 11) (VoD)' ID '150675_f_1069055' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,083 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,181 - INFO - scraping.cms - VOD '3 - Tutorial 10 (ws 10) (VoD)' ID '150675_f_1069054' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,201 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,254 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:42:04,254 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,345 - INFO - scraping.cms - VOD '3 - Tutorial 9 (ws 9) (VoD)' ID '150675_f_1069048' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,412 - INFO - scraping.cms - VOD '1 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,419 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,503 - INFO - scraping.cms - VOD '3 - Tutorial 8 (ws 8) (VoD)' ID '150675_f_1069045' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,577 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,578 - INFO - scraping.cms - VOD '1 - kapitel 5 video 12 (VoD)' ID '150675_f_997901' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,667 - INFO - scraping.cms - VOD '3 - Tutorial 7 (ws 7) (VoD)' ID '150675_f_1069042' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,753 - INFO - scraping.cms - VOD '1 - Unit 6 Video 13 (VoD)' ID '150675_f_1004678' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,843 - INFO - scraping.cms - VOD '3 - Tutorial 6 (ws 6) (VoD)' ID '150675_f_1069039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,899 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:04,919 - INFO - scraping.cms - VOD '1 - Video 14 (VoD)' ID '150675_f_1010657' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,102 - INFO - scraping.cms - VOD '2 - Video 15 (VoD)' ID '150675_f_1010661' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,114 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:42:05,114 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,188 - INFO - scraping.cms - Successfully scraped 8 courses for jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - User jana.hamed has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user jana.hamed.
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - CMS Content processing summary for user jana.hamed: Processed for user jana.hamed: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - Finished processing for user: jana.hamed
2025-06-11 20:42:05,189 - INFO - refresh_cache_script - Starting processing for user: jasmine.abdelhady
2025-06-11 20:42:05,190 - INFO - refresh_cache_script - Processing user: jasmine.abdelhady for data types: ['cms_content']
2025-06-11 20:42:05,190 - INFO - refresh_cache_script - Processing CMS content section for user: jasmine.abdelhady
2025-06-11 20:42:05,190 - INFO - scraping.cms - Fetching CMS course list for jasmine.abdelhady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:05,204 - INFO - scraping.cms - VOD '4 - Midterm Revision (VoD)' ID '150675_f_1071230' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,265 - INFO - scraping.cms - VOD '1 - Kapitel 5 Video 11 (VoD)' ID '150675_f_997894' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,280 - INFO - scraping.cms - Finished parsing course content for mohamed.abouzid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-06-11 20:42:05,280 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE202|) Basic German 2 (33) is not substantial (empty/minimal content)
2025-06-11 20:42:05,368 - INFO - scraping.cms - VOD '3 - Tutorial 5 (ws 5) (VoD)' ID '150675_f_1071247' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,430 - INFO - scraping.cms - VOD '1 - Unit 4 Video 9 (VoD)' ID '150675_f_983448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,464 - INFO - refresh_cache_script - No existing substantial cache found for (|DE202|) Basic German 2 (33), will cache new data even if minimal
2025-06-11 20:42:05,517 - INFO - scraping.cms - VOD '3 - Tutorial 4 (ws 4) (VoD)' ID '150675_f_1067374' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,585 - INFO - scraping.cms - VOD '2 - Unit 5 Video 10 (VoD)' ID '150675_f_983452' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,648 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-06-11 20:42:05,648 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by mohamed.abouzid) successful.
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - CMS Content processing summary for user mohamed.abouzid: Processed for user mohamed.abouzid: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Finished processing for user: mohamed.abouzid
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Starting processing for user: saifaldeen.abdelaziz
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Processing user: saifaldeen.abdelaziz for data types: ['cms_content']
2025-06-11 20:42:05,649 - INFO - refresh_cache_script - Processing CMS content section for user: saifaldeen.abdelaziz
2025-06-11 20:42:05,650 - INFO - scraping.cms - Fetching CMS course list for saifaldeen.abdelaziz from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:05,676 - INFO - scraping.cms - VOD '3 - Tutorial 3 (ws 3) (VoD)' ID '150675_f_1066977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,751 - INFO - scraping.cms - VOD '1 - Unit 4 Video 8 (VoD)' ID '150675_f_983472' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,956 - INFO - scraping.cms - VOD '1 - Unit 2 Video 5 (VoD)' ID '150675_f_967860' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:05,998 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65. Found 16 weeks.
2025-06-11 20:42:05,998 - WARNING - refresh_cache_script - Newly fetched CMS content for (|MATH404|) Math IV (1072) is not substantial (empty/minimal content)
2025-06-11 20:42:06,260 - INFO - scraping.cms - VOD '2 - Unit 2 Video 6 (VoD)' ID '150675_f_967861' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:06,340 - INFO - refresh_cache_script - No existing substantial cache found for (|MATH404|) Math IV (1072), will cache new data even if minimal
2025-06-11 20:42:06,342 - INFO - scraping.cms - Successfully scraped 8 courses for mariam.hosam.
2025-06-11 20:42:06,419 - INFO - scraping.cms - VOD '3 - Unit 2 Video 7 (VoD)' ID '150675_f_967862' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:06,516 - INFO - scraping.cms - Successfully scraped 8 courses for Khaled.abdelwaged.
2025-06-11 20:42:06,582 - INFO - scraping.cms - VOD '1 - Unit 1 Video 4 (VoD)' ID '150675_f_967843' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:06,681 - INFO - utils.cache - Set PICKLE cache for key cms_content:24ac4b2ef071b508afcbda016918c8cc with expiry 3600 seconds
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|MATH404|) Math IV (1072) (Key: cms_content:24ac4b2ef071b508afcbda016918c8cc)
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - User mariam.hosam has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,681 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user mariam.hosam.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content processing summary for user mariam.hosam: Processed for user mariam.hosam: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - Finished processing for user: mariam.hosam
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - User Khaled.abdelwaged has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - Course '(|AS102|) English for Academic Purposes (A1) (14)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65) for user Khaled.abdelwaged needs global refresh.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - Awaiting 1 global CMS refresh tasks initiated by Khaled.abdelwaged.
2025-06-11 20:42:06,682 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-06-11 20:42:06,683 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_content']
2025-06-11 20:42:06,683 - INFO - refresh_cache_script - Processing CMS content section for user: mohamed.elsaadi
2025-06-11 20:42:06,683 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|AS102|) English for Academic Purposes (A1) (14) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65)
2025-06-11 20:42:06,683 - INFO - scraping.cms - Fetching CMS course content for Khaled.abdelwaged from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:06,684 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:06,685 - INFO - scraping.cms - Fetching CMS course announcements for Khaled.abdelwaged from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:06,741 - INFO - scraping.cms - Successfully scraped 8 courses for gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - User gelan.hassan has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - Course '(|AS102|) English for Academic Purposes (A1) (14)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65) for user gelan.hassan needs global refresh.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user gelan.hassan.
2025-06-11 20:42:06,742 - INFO - refresh_cache_script - Awaiting 1 global CMS refresh tasks initiated by gelan.hassan.
2025-06-11 20:42:06,743 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|AS102|) English for Academic Purposes (A1) (14) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65)
2025-06-11 20:42:06,743 - INFO - scraping.cms - Fetching CMS course content for gelan.hassan from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:06,744 - INFO - scraping.cms - Fetching CMS course announcements for gelan.hassan from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:06,746 - INFO - scraping.cms - VOD '1 - Video 2 (VoD)' ID '150675_f_962606' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:06,805 - INFO - scraping.cms - Successfully scraped 7 courses for ali.araffa.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - User ali.araffa has 7 courses. Checking against globally refreshed list.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,806 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,807 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user ali.araffa.
2025-06-11 20:42:06,807 - INFO - refresh_cache_script - CMS Content processing summary for user ali.araffa: Processed for user ali.araffa: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:06,807 - INFO - refresh_cache_script - Finished processing for user: ali.araffa
2025-06-11 20:42:06,807 - INFO - refresh_cache_script - Starting processing for user: abdelrhman.nasr
2025-06-11 20:42:06,807 - INFO - refresh_cache_script - Processing user: abdelrhman.nasr for data types: ['cms_content']
2025-06-11 20:42:06,807 - INFO - refresh_cache_script - Processing CMS content section for user: abdelrhman.nasr
2025-06-11 20:42:06,807 - INFO - scraping.cms - Fetching CMS course list for abdelrhman.nasr from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:06,816 - INFO - scraping.cms - Successfully scraped 8 courses for emadeldin.senousy.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - User emadeldin.senousy has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - Course '(|AS102|) English for Academic Purposes (A1) (14)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65) for user emadeldin.senousy needs global refresh.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,816 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,817 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,817 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user emadeldin.senousy.
2025-06-11 20:42:06,817 - INFO - refresh_cache_script - Awaiting 1 global CMS refresh tasks initiated by emadeldin.senousy.
2025-06-11 20:42:06,817 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|AS102|) English for Academic Purposes (A1) (14) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65)
2025-06-11 20:42:06,817 - INFO - scraping.cms - Fetching CMS course content for emadeldin.senousy from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:06,818 - INFO - scraping.cms - Fetching CMS course announcements for emadeldin.senousy from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:06,913 - INFO - scraping.cms - VOD '2 - Video 3 (VoD)' ID '150675_f_962713' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:07,079 - INFO - scraping.cms - Successfully scraped 7 courses for khadija.mohamed.
2025-06-11 20:42:07,079 - INFO - refresh_cache_script - User khadija.mohamed has 7 courses. Checking against globally refreshed list.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user khadija.mohamed.
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - CMS Content processing summary for user khadija.mohamed: Processed for user khadija.mohamed: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - Finished processing for user: khadija.mohamed
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - Starting processing for user: mamdouh.aiesh
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - Processing user: mamdouh.aiesh for data types: ['cms_content']
2025-06-11 20:42:07,080 - INFO - refresh_cache_script - Processing CMS content section for user: mamdouh.aiesh
2025-06-11 20:42:07,080 - INFO - scraping.cms - Fetching CMS course list for mamdouh.aiesh from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:07,091 - INFO - scraping.cms - VOD '1 - Video 1 (VoD)' ID '150675_f_962715' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:07,278 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65. Found 36 weeks.
2025-06-11 20:42:07,279 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE404|) Basic German 4 (73) is not substantial (empty/minimal content)
2025-06-11 20:42:07,295 - INFO - scraping.cms - VOD '1 - Revision Part I (VoD)' ID '150675_f_1123696' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:07,437 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-06-11 20:42:07,458 - INFO - scraping.cms - VOD '1 - Lektion 24 Video 1 (VoD)' ID '150675_f_1073495' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:07,621 - INFO - refresh_cache_script - No existing substantial cache found for (|DE404|) Basic German 4 (73), will cache new data even if minimal
2025-06-11 20:42:07,636 - INFO - scraping.cms - VOD '2 - Lektion 24 Video 1 Komplett (VoD)' ID '150675_f_1073500' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:07,947 - INFO - scraping.cms - VOD '3 - Lektion 24 Video 2 Komplett (VoD)' ID '150675_f_1073502' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:07,963 - INFO - utils.cache - Set PICKLE cache for key cms_content:34c291bbefc813231601da76882a6b39 with expiry 3600 seconds
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE404|) Basic German 4 (73) (Key: cms_content:34c291bbefc813231601da76882a6b39)
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65 (initiated by nour.tantawi) successful.
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - CMS Content processing summary for user nour.tantawi: Processed for user nour.tantawi: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:07,964 - INFO - refresh_cache_script - Finished processing for user: nour.tantawi
2025-06-11 20:42:07,965 - INFO - refresh_cache_script - Starting processing for user: magy.henna
2025-06-11 20:42:07,965 - INFO - refresh_cache_script - Processing user: magy.henna for data types: ['cms_content']
2025-06-11 20:42:07,965 - INFO - refresh_cache_script - Processing CMS content section for user: magy.henna
2025-06-11 20:42:07,965 - INFO - scraping.cms - Fetching CMS course list for magy.henna from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:08,121 - INFO - scraping.cms - VOD '1 - Lektion 23 Video 1 (VoD)' ID '150675_f_1073450' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:08,217 - INFO - scraping.cms - Successfully scraped 8 courses for jasmine.abdelhady.
2025-06-11 20:42:08,217 - INFO - refresh_cache_script - User jasmine.abdelhady has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:08,217 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,217 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,217 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user jasmine.abdelhady.
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - CMS Content processing summary for user jasmine.abdelhady: Processed for user jasmine.abdelhady: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - Finished processing for user: jasmine.abdelhady
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - Starting processing for user: malak.amrallah
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - Processing user: malak.amrallah for data types: ['cms_content']
2025-06-11 20:42:08,218 - INFO - refresh_cache_script - Processing CMS content section for user: malak.amrallah
2025-06-11 20:42:08,218 - INFO - scraping.cms - Fetching CMS course list for malak.amrallah from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:08,275 - INFO - scraping.cms - VOD '2 - Lektion 23 Video 2 (VoD)' ID '150675_f_1073453' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:08,430 - INFO - scraping.cms - VOD '3 - Lektion 23 Video 3 (VoD)' ID '150675_f_1073456' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:08,610 - INFO - scraping.cms - VOD '4 - Lektion 23 Video 1 Komplett (VoD)' ID '150675_f_1073474' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:08,701 - INFO - scraping.cms - Successfully scraped 8 courses for saifaldeen.abdelaziz.
2025-06-11 20:42:08,701 - INFO - refresh_cache_script - User saifaldeen.abdelaziz has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user saifaldeen.abdelaziz.
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - CMS Content processing summary for user saifaldeen.abdelaziz: Processed for user saifaldeen.abdelaziz: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - Finished processing for user: saifaldeen.abdelaziz
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - Starting processing for user: malek.amer
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - Processing user: malek.amer for data types: ['cms_content']
2025-06-11 20:42:08,702 - INFO - refresh_cache_script - Processing CMS content section for user: malek.amer
2025-06-11 20:42:08,703 - INFO - scraping.cms - Fetching CMS course list for malek.amer from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:08,792 - INFO - scraping.cms - VOD '5 - Lektion 23 Video 2 Komplett (VoD)' ID '150675_f_1073481' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:09,116 - INFO - scraping.cms - VOD '6 - Lektion 23 Video 3 Komplett (VoD)' ID '150675_f_1073486' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:09,299 - INFO - scraping.cms - VOD '7 - Lektion 23 Video 4 Komplett (VoD)' ID '150675_f_1073485' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:09,619 - INFO - scraping.cms - VOD '1 - Lektion 22 Video 1 (VoD)' ID '150675_f_1073442' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:09,678 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - User mohamed.elsaadi has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user mohamed.elsaadi.
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - CMS Content processing summary for user mohamed.elsaadi: Processed for user mohamed.elsaadi: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - Starting processing for user: baraa.khedr
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - Processing user: baraa.khedr for data types: ['cms_content']
2025-06-11 20:42:09,679 - INFO - refresh_cache_script - Processing CMS content section for user: baraa.khedr
2025-06-11 20:42:09,680 - INFO - scraping.cms - Fetching CMS course list for baraa.khedr from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-06-11 20:42:09,801 - INFO - scraping.cms - VOD '2 - Lektion 22 Video 2 (VoD)' ID '150675_f_1073444' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:10,107 - INFO - scraping.cms - VOD '3 - Lektion 22 complete video 1 (VoD)' ID '150675_f_1073448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:10,278 - INFO - scraping.cms - VOD '4 - Lektion 22 complete video 2 (VoD)' ID '150675_f_1101615' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:10,436 - INFO - scraping.cms - VOD '1 - Unit 21 Part 1 (VoD)' ID '150675_f_1063834' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:10,587 - INFO - scraping.cms - VOD '2 - Unit 21 Part 2 (VoD)' ID '150675_f_1067572' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:10,746 - INFO - scraping.cms - VOD '3 - Unit 21 (Complete Unit: Part 1) (VoD)' ID '150675_f_1094812' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:10,795 - INFO - scraping.cms - Successfully scraped 8 courses for mamdouh.aiesh.
2025-06-11 20:42:10,795 - INFO - refresh_cache_script - User mamdouh.aiesh has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:10,795 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,795 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,795 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user mamdouh.aiesh.
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - CMS Content processing summary for user mamdouh.aiesh: Processed for user mamdouh.aiesh: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:10,796 - INFO - refresh_cache_script - Finished processing for user: mamdouh.aiesh
2025-06-11 20:42:11,065 - INFO - scraping.cms - VOD '4 - Unit 21 (Complete Unit: Part 2) (VoD)' ID '150675_f_1094820' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:11,234 - INFO - scraping.cms - VOD '5 - Unit 21 (Complete Unit: Part 3) (VoD)' ID '150675_f_1094828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:11,534 - INFO - scraping.cms - VOD '1 - Unit 20 (VoD)' ID '150675_f_1063828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:11,660 - INFO - scraping.cms - Successfully scraped 7 courses for malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - User malak.amrallah has 7 courses. Checking against globally refreshed list.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user malak.amrallah.
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - CMS Content processing summary for user malak.amrallah: Processed for user malak.amrallah: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:11,661 - INFO - refresh_cache_script - Finished processing for user: malak.amrallah
2025-06-11 20:42:11,863 - INFO - scraping.cms - VOD '2 - Unit 20 (Complete Unit) (VoD)' ID '150675_f_1092097' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:12,085 - INFO - scraping.cms - Successfully scraped 7 courses for malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - User malek.amer has 7 courses. Checking against globally refreshed list.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user malek.amer.
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - CMS Content processing summary for user malek.amer: Processed for user malek.amer: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:12,086 - INFO - refresh_cache_script - Finished processing for user: malek.amer
2025-06-11 20:42:12,169 - INFO - scraping.cms - Successfully scraped 8 courses for magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - User magy.henna has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user magy.henna.
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - CMS Content processing summary for user magy.henna: Processed for user magy.henna: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:12,170 - INFO - refresh_cache_script - Finished processing for user: magy.henna
2025-06-11 20:42:12,176 - INFO - scraping.cms - Successfully scraped 7 courses for abdelrhman.nasr.
2025-06-11 20:42:12,176 - INFO - refresh_cache_script - User abdelrhman.nasr has 7 courses. Checking against globally refreshed list.
2025-06-11 20:42:12,176 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,176 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,176 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,176 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,177 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,177 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,177 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user abdelrhman.nasr.
2025-06-11 20:42:12,177 - INFO - refresh_cache_script - CMS Content processing summary for user abdelrhman.nasr: Processed for user abdelrhman.nasr: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:12,177 - INFO - refresh_cache_script - Finished processing for user: abdelrhman.nasr
2025-06-11 20:42:12,203 - INFO - scraping.cms - VOD '3 - K19 - V1 (VoD)' ID '150675_f_1062891' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:12,367 - INFO - scraping.cms - VOD '4 - Unit 19 Complete Unit (Part1) (VoD)' ID '150675_f_1066246' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:12,522 - INFO - scraping.cms - Successfully scraped 8 courses for baraa.khedr.
2025-06-11 20:42:12,522 - INFO - refresh_cache_script - User baraa.khedr has 8 courses. Checking against globally refreshed list.
2025-06-11 20:42:12,522 - INFO - refresh_cache_script - CMS Content for course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,522 - INFO - refresh_cache_script - CMS Content for course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,522 - INFO - refresh_cache_script - CMS Content for course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,522 - INFO - refresh_cache_script - CMS Content for course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,523 - INFO - refresh_cache_script - CMS Content for course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,523 - INFO - refresh_cache_script - CMS Content for course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,523 - INFO - refresh_cache_script - CMS Content for course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,523 - INFO - refresh_cache_script - CMS Content for course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) already refreshed this run. Skipping for user baraa.khedr.
2025-06-11 20:42:12,523 - INFO - refresh_cache_script - CMS Content processing summary for user baraa.khedr: Processed for user baraa.khedr: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:12,523 - INFO - refresh_cache_script - Finished processing for user: baraa.khedr
2025-06-11 20:42:12,685 - INFO - scraping.cms - VOD '5 - Unit 19 Complete Unit (Part 2) (VoD)' ID '150675_f_1066249' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:12,843 - INFO - scraping.cms - VOD '1 - Revision 1 (VoD)' ID '150675_f_1124540' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,002 - INFO - scraping.cms - VOD '2 - Revision 2 (VoD)' ID '150675_f_1124541' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,158 - INFO - scraping.cms - VOD '4 - Model test DE3 AUDIO (VoD)' ID '150675_f_1124235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,312 - INFO - scraping.cms - VOD '1 - U18-V1 Blended (VoD)' ID '150675_f_1090464' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,360 - INFO - scraping.cms - VOD '3 - Argument Analysis I (Supports) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-de3ef567-63e3-3374-53f2-9984bf219543' for access URL.
2025-06-11 20:42:13,360 - INFO - scraping.cms - VOD '4 - Argument Analaysis II (Flaws) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d7d375d9-a258-dcfd-bcc2-dc09f4c6038b' for access URL.
2025-06-11 20:42:13,360 - INFO - scraping.cms - VOD '1 - Synthesis (VoD)' ID '150675_f_1057707' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,418 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:13,470 - INFO - scraping.cms - VOD '2 - U18-V2 Blended (VoD)' ID '150675_f_1090082' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,526 - INFO - scraping.cms - VOD '1 - Essay 3 Orientation VOD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-dc8ae1a3-ac4f-5878-523e-2a43e58a4300' for access URL.
2025-06-11 20:42:13,527 - INFO - scraping.cms - VOD '2 - Patterns of Organization (VoD)' ID '150675_f_963806' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,546 - INFO - scraping.cms - VOD '3 - Argument Analysis I (Supports) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-de3ef567-63e3-3374-53f2-9984bf219543' for access URL.
2025-06-11 20:42:13,546 - INFO - scraping.cms - VOD '4 - Argument Analaysis II (Flaws) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d7d375d9-a258-dcfd-bcc2-dc09f4c6038b' for access URL.
2025-06-11 20:42:13,546 - INFO - scraping.cms - VOD '1 - Synthesis (VoD)' ID '150675_f_1057707' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,765 - INFO - scraping.cms - VOD '3 - Argument Analysis I (Supports) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-de3ef567-63e3-3374-53f2-9984bf219543' for access URL.
2025-06-11 20:42:13,765 - INFO - scraping.cms - VOD '4 - Argument Analaysis II (Flaws) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d7d375d9-a258-dcfd-bcc2-dc09f4c6038b' for access URL.
2025-06-11 20:42:13,766 - INFO - scraping.cms - VOD '1 - Synthesis (VoD)' ID '150675_f_1057707' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,769 - INFO - scraping.cms - VOD '3 - U18-V1 Complete (VoD)' ID '150675_f_1090084' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,775 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:13,782 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65
2025-06-11 20:42:13,833 - INFO - scraping.cms - VOD '1 - VoD	Inferences (VoD)' ID '150675_f_1165727' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,852 - INFO - scraping.cms - VOD '1 - Essay 3 Orientation VOD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-dc8ae1a3-ac4f-5878-523e-2a43e58a4300' for access URL.
2025-06-11 20:42:13,853 - INFO - scraping.cms - VOD '2 - Patterns of Organization (VoD)' ID '150675_f_963806' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,931 - INFO - scraping.cms - VOD '1 - Essay 3 Orientation VOD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-dc8ae1a3-ac4f-5878-523e-2a43e58a4300' for access URL.
2025-06-11 20:42:13,932 - INFO - scraping.cms - VOD '2 - Patterns of Organization (VoD)' ID '150675_f_963806' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,933 - INFO - scraping.cms - VOD '4 - U18-V2 Complete (VoD)' ID '150675_f_1090083' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:13,990 - INFO - scraping.cms - VOD '2 - Patterns of Organization (VoD)' ID '150675_f_963806' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,103 - INFO - scraping.cms - VOD '1 - U17- V1 Blended (VoD)' ID '150675_f_1090058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,105 - INFO - scraping.cms - VOD '1 - VoD	Inferences (VoD)' ID '150675_f_1165727' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,160 - INFO - scraping.cms - VOD '1 - Paraphrase (VoD)' ID '150675_f_1057698' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,164 - INFO - scraping.cms - VOD '1 - VoD	Inferences (VoD)' ID '150675_f_1165727' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,325 - INFO - scraping.cms - VOD '2 - Patterns of Organization (VoD)' ID '150675_f_963806' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,411 - INFO - scraping.cms - VOD '2 - U17-V2 Blended (VoD)' ID '150675_f_1090062' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,415 - INFO - scraping.cms - VOD '2 - Patterns of Organization (VoD)' ID '150675_f_963806' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,468 - INFO - scraping.cms - VOD '1 - Skimming, Scanning and Context Clues (VoD)' ID '150675_f_1162639' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,658 - INFO - scraping.cms - VOD '1 - Paraphrase (VoD)' ID '150675_f_1057698' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,718 - INFO - scraping.cms - VOD '3 - U17-V3 Blended (VoD)' ID '150675_f_1090064' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,759 - INFO - scraping.cms - VOD '1 - Paraphrase (VoD)' ID '150675_f_1057698' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,782 - INFO - scraping.cms - VOD '1 - Argumentative Essay (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0585d768-9039-ed8d-dec8-a06f2437833a' for access URL.
2025-06-11 20:42:14,783 - INFO - scraping.cms - VOD '16 - Essay Critique (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-27d0c965-9dfb-5794-f428-3275b5bf7653' for access URL.
2025-06-11 20:42:14,783 - INFO - scraping.cms - Finished parsing course content for Khaled.abdelwaged from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65. Found 15 weeks.
2025-06-11 20:42:14,783 - WARNING - refresh_cache_script - Newly fetched CMS content for (|AS102|) English for Academic Purposes (A1) (14) is not substantial (empty/minimal content)
2025-06-11 20:42:14,938 - INFO - scraping.cms - VOD '1 - Skimming, Scanning and Context Clues (VoD)' ID '150675_f_1162639' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:14,968 - INFO - refresh_cache_script - No existing substantial cache found for (|AS102|) English for Academic Purposes (A1) (14), will cache new data even if minimal
2025-06-11 20:42:15,001 - INFO - scraping.cms - VOD '1 - Skimming, Scanning and Context Clues (VoD)' ID '150675_f_1162639' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:15,022 - INFO - scraping.cms - VOD '4 - U17 - V1 Complete (VoD)' ID '150675_f_1090063' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:15,153 - INFO - utils.cache - Set PICKLE cache for key cms_content:dfacf6c43529b806f02f96b488812065 with expiry 3600 seconds
2025-06-11 20:42:15,153 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|AS102|) English for Academic Purposes (A1) (14) (Key: cms_content:dfacf6c43529b806f02f96b488812065)
2025-06-11 20:42:15,153 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65 (initiated by Khaled.abdelwaged) successful.
2025-06-11 20:42:15,153 - INFO - refresh_cache_script - CMS Content processing summary for user Khaled.abdelwaged: Processed for user Khaled.abdelwaged: Globally Updated Now=1, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:15,153 - INFO - refresh_cache_script - Finished processing for user: Khaled.abdelwaged
2025-06-11 20:42:15,247 - INFO - scraping.cms - VOD '1 - Argumentative Essay (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0585d768-9039-ed8d-dec8-a06f2437833a' for access URL.
2025-06-11 20:42:15,248 - INFO - scraping.cms - VOD '16 - Essay Critique (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-27d0c965-9dfb-5794-f428-3275b5bf7653' for access URL.
2025-06-11 20:42:15,248 - INFO - scraping.cms - Finished parsing course content for emadeldin.senousy from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65. Found 15 weeks.
2025-06-11 20:42:15,249 - WARNING - refresh_cache_script - Newly fetched CMS content for (|AS102|) English for Academic Purposes (A1) (14) is not substantial (empty/minimal content)
2025-06-11 20:42:15,313 - INFO - scraping.cms - VOD '1 - Argumentative Essay (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0585d768-9039-ed8d-dec8-a06f2437833a' for access URL.
2025-06-11 20:42:15,314 - INFO - scraping.cms - VOD '16 - Essay Critique (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-27d0c965-9dfb-5794-f428-3275b5bf7653' for access URL.
2025-06-11 20:42:15,314 - INFO - scraping.cms - Finished parsing course content for gelan.hassan from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65. Found 15 weeks.
2025-06-11 20:42:15,347 - INFO - scraping.cms - VOD '5 - U17 - V2 Complete (VoD)' ID '150675_f_1090067' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:15,436 - INFO - refresh_cache_script - No existing substantial cache found for (|AS102|) English for Academic Purposes (A1) (14), will cache new data even if minimal
2025-06-11 20:42:15,620 - INFO - utils.cache - Set PICKLE cache for key cms_content:dfacf6c43529b806f02f96b488812065 with expiry 3600 seconds
2025-06-11 20:42:15,620 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|AS102|) English for Academic Purposes (A1) (14) (Key: cms_content:dfacf6c43529b806f02f96b488812065)
2025-06-11 20:42:15,620 - WARNING - refresh_cache_script - Newly fetched CMS content for (|AS102|) English for Academic Purposes (A1) (14) is not substantial (empty/minimal content)
2025-06-11 20:42:15,649 - INFO - scraping.cms - VOD '6 - U17-V3 Complete (VoD)' ID '150675_f_1090071' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:15,808 - INFO - refresh_cache_script - No existing substantial cache found for (|AS102|) English for Academic Purposes (A1) (14), will cache new data even if minimal
2025-06-11 20:42:15,823 - INFO - scraping.cms - VOD '1 - U16- V1 Blended (VoD)' ID '150675_f_1090053' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:16,000 - INFO - scraping.cms - VOD '2 - U16- V1 complete (VoD)' ID '150675_f_1090057' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:16,235 - INFO - utils.cache - Set PICKLE cache for key cms_content:dfacf6c43529b806f02f96b488812065 with expiry 3600 seconds
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|AS102|) English for Academic Purposes (A1) (14) (Key: cms_content:dfacf6c43529b806f02f96b488812065)
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65 (initiated by emadeldin.senousy) successful.
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - CMS Content processing summary for user emadeldin.senousy: Processed for user emadeldin.senousy: Globally Updated Now=1, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - Finished processing for user: emadeldin.senousy
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=14&sid=65 (initiated by gelan.hassan) successful.
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - CMS Content processing summary for user gelan.hassan: Processed for user gelan.hassan: Globally Updated Now=1, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:16,235 - INFO - refresh_cache_script - Finished processing for user: gelan.hassan
2025-06-11 20:42:16,309 - INFO - scraping.cms - VOD '3 - U16-V2 complete (VoD)' ID '150675_f_1090462' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:16,472 - INFO - scraping.cms - VOD '1 - U13-complete Video1 (VoD)' ID '150675_f_1065580' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:16,788 - INFO - scraping.cms - VOD '2 - U13- complete Video2 (VoD)' ID '150675_f_1065582' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:16,953 - INFO - scraping.cms - VOD '1 - U15-V1 (VoD)' ID '150675_f_1065068' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:17,131 - INFO - scraping.cms - VOD '2 - U16-V1 (VoD)' ID '150675_f_1065069' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:17,440 - INFO - scraping.cms - VOD '3 - U15 V1 Complete (VoD)' ID '150675_f_1141997' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:17,601 - INFO - scraping.cms - VOD '1 - U14 - V1 (VoD)' ID '150675_f_1063454' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:17,932 - INFO - scraping.cms - VOD '2 - U14- V2 (VoD)' ID '150675_f_1065039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:18,101 - INFO - scraping.cms - VOD '3 - U14-V3 (VoD)' ID '150675_f_1065060' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:18,418 - INFO - scraping.cms - VOD '4 - U14-V4 (VoD)' ID '150675_f_1065058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:18,596 - INFO - scraping.cms - VOD '5 - U14 Complete Video (VoD)' ID '150675_f_1141802' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:18,916 - INFO - scraping.cms - VOD '6 - U14 Complete V2 (VoD)' ID '150675_f_1141799' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:19,080 - INFO - scraping.cms - VOD '1 - K13- V1 (VoD)' ID '150675_f_1062824' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:19,444 - INFO - scraping.cms - VOD '2 - K13 - V2 (VoD)' ID '150675_f_1062876' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:19,617 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:19,803 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:19,970 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:20,297 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-06-11 20:42:20,297 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:20,595 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:20,766 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:20,921 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:21,222 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:21,547 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:21,875 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:22,056 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:22,233 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:22,406 - INFO - scraping.cms - VOD '1 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:22,572 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:22,736 - INFO - scraping.cms - VOD '3 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:23,057 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:23,368 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-06-11 20:42:23,369 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:23,534 - INFO - scraping.cms - VOD '1 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:23,713 - INFO - scraping.cms - VOD '1 - kapitel 5 video 12 (VoD)' ID '150675_f_997901' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:23,876 - INFO - scraping.cms - VOD '1 - Unit 6 Video 13 (VoD)' ID '150675_f_1004678' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:24,218 - INFO - scraping.cms - VOD '1 - Video 14 (VoD)' ID '150675_f_1010657' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:24,384 - INFO - scraping.cms - VOD '2 - Video 15 (VoD)' ID '150675_f_1010661' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:24,551 - INFO - scraping.cms - VOD '1 - Kapitel 5 Video 11 (VoD)' ID '150675_f_997894' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:24,709 - INFO - scraping.cms - VOD '1 - Unit 4 Video 9 (VoD)' ID '150675_f_983448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:25,047 - INFO - scraping.cms - VOD '2 - Unit 5 Video 10 (VoD)' ID '150675_f_983452' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:25,210 - INFO - scraping.cms - VOD '1 - Unit 4 Video 8 (VoD)' ID '150675_f_983472' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:25,519 - INFO - scraping.cms - VOD '1 - Unit 2 Video 5 (VoD)' ID '150675_f_967860' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:25,673 - INFO - scraping.cms - VOD '2 - Unit 2 Video 6 (VoD)' ID '150675_f_967861' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:25,979 - INFO - scraping.cms - VOD '3 - Unit 2 Video 7 (VoD)' ID '150675_f_967862' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:26,126 - INFO - scraping.cms - VOD '1 - Unit 1 Video 4 (VoD)' ID '150675_f_967843' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:26,278 - INFO - scraping.cms - VOD '1 - Video 2 (VoD)' ID '150675_f_962606' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:26,604 - INFO - scraping.cms - VOD '2 - Video 3 (VoD)' ID '150675_f_962713' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:26,937 - INFO - scraping.cms - VOD '1 - Video 1 (VoD)' ID '150675_f_962715' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-06-11 20:42:27,110 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65. Found 36 weeks.
2025-06-11 20:42:27,111 - WARNING - refresh_cache_script - Newly fetched CMS content for (|DE404|) Basic German 4 (73) is not substantial (empty/minimal content)
2025-06-11 20:42:27,452 - INFO - refresh_cache_script - No existing substantial cache found for (|DE404|) Basic German 4 (73), will cache new data even if minimal
2025-06-11 20:42:27,794 - INFO - utils.cache - Set PICKLE cache for key cms_content:34c291bbefc813231601da76882a6b39 with expiry 3600 seconds
2025-06-11 20:42:27,794 - INFO - refresh_cache_script - Successfully cached minimal GLOBAL CMS content cache for (|DE404|) Basic German 4 (73) (Key: cms_content:34c291bbefc813231601da76882a6b39)
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Global minimal refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65 (initiated by malak.mohamedelkady) successful.
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - CMS Content processing summary for user malak.mohamedelkady: Processed for user malak.mohamedelkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - Finished processing for user: malak.mohamedelkady
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - User: youssef.alghrory -> cms_content: Processed for user youssef.alghrory: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,795 - INFO - refresh_cache_script - User: rafaiel.elbaiady -> cms_content: Processed for user rafaiel.elbaiady: Globally Updated Now=7, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: moustafa.mohamed -> cms_content: Processed for user moustafa.mohamed: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: maleek.elhodaiby -> cms_content: Processed for user maleek.elhodaiby: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: eyad.abdelhafiz -> cms_content: Processed for user eyad.abdelhafiz: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: malak.mohamedelkady -> cms_content: Processed for user malak.mohamedelkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: seif.elkady -> cms_content: Processed for user seif.elkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: nour.tantawi -> cms_content: Processed for user nour.tantawi: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: amir.beshay -> cms_content: Processed for user amir.beshay: Globally Updated Now=7, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: mohamed.abouzid -> cms_content: Processed for user mohamed.abouzid: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: nouralhoda.alseufy -> cms_content: Processed for user nouralhoda.alseufy: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: abdullah.salousa -> cms_content: Processed for user abdullah.salousa: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: hana.bediar -> cms_content: Processed for user hana.bediar: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: seif.abdelkader -> cms_content: Processed for user seif.abdelkader: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: jana.hamed -> cms_content: Processed for user jana.hamed: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: emadeldin.senousy -> cms_content: Processed for user emadeldin.senousy: Globally Updated Now=1, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: mariam.hosam -> cms_content: Processed for user mariam.hosam: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: ali.araffa -> cms_content: Processed for user ali.araffa: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: gelan.hassan -> cms_content: Processed for user gelan.hassan: Globally Updated Now=1, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: khadija.mohamed -> cms_content: Processed for user khadija.mohamed: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: Khaled.abdelwaged -> cms_content: Processed for user Khaled.abdelwaged: Globally Updated Now=1, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: jasmine.abdelhady -> cms_content: Processed for user jasmine.abdelhady: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: saifaldeen.abdelaziz -> cms_content: Processed for user saifaldeen.abdelaziz: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,796 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_content: Processed for user mohamed.elsaadi: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - User: abdelrhman.nasr -> cms_content: Processed for user abdelrhman.nasr: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - User: mamdouh.aiesh -> cms_content: Processed for user mamdouh.aiesh: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - User: magy.henna -> cms_content: Processed for user magy.henna: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - User: malak.amrallah -> cms_content: Processed for user malak.amrallah: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - User: malek.amer -> cms_content: Processed for user malek.amer: Globally Updated Now=0, Skipped (already updated this run)=7, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - User: baraa.khedr -> cms_content: Processed for user baraa.khedr: Globally Updated Now=0, Skipped (already updated this run)=8, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-06-11T20:42:27.797281 (Duration: 0:01:06.749173) ---
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - CMS Content Global Summary: Total unique courses successfully refreshed and cached this run = 19
2025-06-11 20:42:27,797 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=0, Skipped=0, Failed=0