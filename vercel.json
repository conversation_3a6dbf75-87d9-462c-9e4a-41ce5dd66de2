{"version": 2, "builds": [{"src": "app.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb"}}], "routes": [{"src": "/(.*)", "methods": ["OPTIONS"], "dest": "app.py", "status": 204, "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Accept, Origin, Admin-Secret", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Credentials": "true", "Access-Control-Max-Age": "86400"}}, {"src": "/api/(.*)", "dest": "app.py", "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Accept, Origin, Admin-Secret", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Credentials": "true"}}, {"src": "/(.*)", "dest": "app.py", "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Accept, Origin, Admin-Secret", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Credentials": "true"}}], "env": {"FLASK_APP": "app.py", "PYTHONPATH": "$PYTHONPATH:.", "REQUESTS_CA_BUNDLE": "/etc/ssl/certs/ca-certificates.crt"}, "github": {"silent": true}}