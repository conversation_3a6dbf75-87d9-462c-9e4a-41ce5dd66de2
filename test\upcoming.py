"""
API endpoint for upcoming events using Google's Gemini AI model.
"""

import logging
import json
import time
import threading
import concurrent.futures
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from flask import Blueprint, request, jsonify, g
from google import genai
from google.genai import types

from config import config
from utils.auth import validate_credentials_flow, AuthError
from utils.cache import get_from_cache, set_in_cache, generate_cache_key
from scraping.guc_data import scrape_guc_data_fast
from scraping.cms import (
    scrape_cms_courses,
    scrape_course_content,
    scrape_course_announcements,
)
from utils.helpers import normalize_course_url

logger = logging.getLogger(__name__)
upcoming_bp = Blueprint("upcoming_bp", __name__)

# Initialize the Gemini AI client with the API key
API_KEY = "AIzaSyAzSzm1L2ECUy_5Dm5hkMnvB-hozyMw5RI"

# Maximum retries for API calls
MAX_RETRIES = 2  # Reduced from 3 to make it faster
# Backoff time between retries (in seconds)
BACKOFF_TIME = 1  # Reduced from 2 to make it faster
# Timeout for API calls (in seconds)
API_TIMEOUT = 30
# Cache prefix for upcoming events
UPCOMING_CACHE_PREFIX = "upcoming"


def initialize_gemini_client():
    """Initialize the Gemini AI client."""
    try:
        client = genai.Client(api_key=API_KEY)
        return client
    except Exception as e:
        logger.error(f"Failed to initialize Gemini AI client: {e}", exc_info=True)
        return None


@upcoming_bp.route("/upcoming", methods=["GET"])
def api_upcoming():
    """
    Endpoint to fetch upcoming events using Gemini AI.
    Uses cache first, then fetches fresh data if needed.

    Query parameters:
    - username: GUC username
    - password: GUC password
    - days: Number of days ahead to consider (default: 30)
    - force_refresh: Whether to bypass the cache (default: false)
    - use_ai: Whether to use AI-powered extraction (default: true)
    """
    # --- Bot Health Check ---
    if request.args.get("bot", "").lower() == "true":
        logger.info("Received bot health check request for Upcoming Events API.")
        g.log_outcome = "bot_check_success"
        return (
            jsonify(
                {
                    "status": "Success",
                    "message": "Upcoming Events API route is up!",
                    "data": None,
                }
            ),
            200,
        )

    # --- Parameter Extraction & Validation ---
    username = request.args.get("username")
    password = request.args.get("password")
    days_ahead = int(request.args.get("days", "30"))
    force_refresh = request.args.get("force_refresh", "false").lower() == "true"
    use_ai = request.args.get("use_ai", "true").lower() == "true"
    g.username = username

    if not username or not password:
        g.log_outcome = "validation_error"
        g.log_error_message = "Missing username or password"
        return (
            jsonify(
                {
                    "status": "error",
                    "message": "Missing required parameters: username and password",
                }
            ),
            400,
        )

    try:
        # --- Validate Credentials ---
        password_to_use = validate_credentials_flow(username, password)

        # --- Check Cache ---
        cache_key = generate_cache_key(
            f"{UPCOMING_CACHE_PREFIX}_{days_ahead}_{use_ai}", username
        )
        if not force_refresh:
            cached_data = get_from_cache(cache_key)
            if cached_data:
                logger.info(f"Cache hit for upcoming events for {username}")
                g.log_outcome = "cache_hit"
                return jsonify(cached_data), 200

        # --- Cache Miss -> Fetch Data ---
        logger.info(f"Cache miss for upcoming events. Fetching data for {username}")
        g.log_outcome = "fetch_attempt"

        # Fetch GUC data
        guc_data = scrape_guc_data_fast(username, password_to_use)
        if not guc_data:
            g.log_outcome = "guc_data_fetch_error"
            g.log_error_message = "Failed to fetch GUC data"
            return (
                jsonify({"status": "error", "message": "Failed to fetch GUC data"}),
                502,
            )

        # Fetch CMS courses
        cms_courses = scrape_cms_courses(username, password_to_use)
        if cms_courses is None:
            g.log_outcome = "cms_courses_fetch_error"
            g.log_error_message = "Failed to fetch CMS courses"
            return (
                jsonify({"status": "error", "message": "Failed to fetch CMS courses"}),
                502,
            )

        # Extract upcoming events
        if use_ai:
            events = extract_upcoming_events_ai(
                username, password_to_use, guc_data, cms_courses, days_ahead
            )
        else:
            # Fallback to traditional extraction if AI is disabled
            events = extract_upcoming_events_traditional(
                guc_data, cms_courses, days_ahead
            )

        # Cache the results
        set_in_cache(cache_key, events, timeout=config.CACHE_DEFAULT_TIMEOUT)
        logger.info(f"Cached upcoming events for {username}")

        # Return the results
        g.log_outcome = "fetch_success"
        return jsonify(events), 200

    except AuthError as e:
        g.log_outcome = "auth_error"
        g.log_error_message = str(e)
        return jsonify({"status": "error", "message": str(e)}), 401
    except Exception as e:
        g.log_outcome = "error"
        g.log_error_message = str(e)
        logger.error(f"Error in upcoming events API: {e}", exc_info=True)
        return (
            jsonify({"status": "error", "message": "Failed to fetch upcoming events"}),
            500,
        )


def extract_upcoming_events_ai(
    username: str,
    password: str,
    guc_data: Dict[str, Any],
    cms_courses: List[Dict[str, Any]],
    days_ahead: int = 30,
) -> List[Dict[str, Any]]:
    """
    Extract upcoming events from GUC data and CMS content using Gemini AI.
    Processes all courses in parallel for better performance.

    Args:
        username: The username for authentication
        password: The password for authentication
        guc_data: The GUC data containing notifications
        cms_courses: List of CMS courses
        days_ahead: Number of days ahead to consider for upcoming events

    Returns:
        List of upcoming events in the required format
    """
    try:
        client = initialize_gemini_client()
        if not client:
            logger.error("Failed to initialize Gemini AI client")
            return []

        # Prepare the current date information
        current_date = datetime.now()
        cutoff_date = current_date + timedelta(days=days_ahead)

        # Prepare GUC notifications (only from the past month)
        guc_notifications = _prepare_guc_notifications(guc_data)

        # Gather course data in parallel using ThreadPoolExecutor for better performance
        all_course_data = []
        course_data_lock = threading.Lock()

        def fetch_course_data(course):
            course_name = course.get("course_name", "Unknown")
            course_url = course.get("course_url", "")

            if not course_url:
                logger.warning(f"Missing course URL for {course_name}")
                return None

            try:
                normalized_url = normalize_course_url(course_url)
                if not normalized_url:
                    logger.warning(f"Invalid course URL format: {course_url}")
                    return None

                # Use ThreadPoolExecutor to fetch content and announcements in parallel
                content_result = None
                announcements_result = None

                with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                    content_future = executor.submit(
                        scrape_course_content, username, password, normalized_url
                    )
                    announcements_future = executor.submit(
                        scrape_course_announcements, username, password, normalized_url
                    )

                    # Get results from futures
                    content_result = content_future.result()
                    announcements_result = announcements_future.result()

                # Process content data
                processed_content = []
                if content_result:
                    for week in content_result:
                        # Each week has: week_name, announcement, description, contents
                        week_name = week.get("week_name", "")
                        week_description = week.get("description", "")
                        week_announcement = week.get("announcement", "")
                        week_contents = week.get("contents", [])

                        # Add week announcement if it exists
                        if week_announcement:
                            processed_content.append(
                                {
                                    "type": "announcement",
                                    "title": f"Week Announcement: {week_name}",
                                    "content": week_announcement,
                                    "week": week_name,
                                    "description": week_description,
                                }
                            )

                        # Add week contents
                        for content_item in week_contents:
                            processed_content.append(
                                {
                                    "type": "content",
                                    "title": content_item.get("title", ""),
                                    "week": week_name,
                                    "description": week_description,
                                }
                            )

                # Process announcements data
                processed_announcements = []
                if announcements_result and isinstance(announcements_result, dict):
                    # Extract announcements HTML or any other format
                    announcements_html = announcements_result.get(
                        "announcements_html", ""
                    )
                    if announcements_html:
                        processed_announcements.append(
                            {
                                "type": "course_announcement",
                                "content": announcements_html,
                                "title": f"Course Announcement: {course_name}",
                            }
                        )

                if processed_content or processed_announcements:
                    course_data = {
                        "course_name": course_name,
                        "course_url": normalized_url,
                        "content": processed_content,
                        "announcements": processed_announcements,
                    }

                    return course_data
                else:
                    logger.warning(
                        f"No content or announcements found for course: {course_name}"
                    )
                    return None
            except Exception as e:
                logger.error(
                    f"Error fetching data for course {course_name}: {e}", exc_info=True
                )
                return None

        # Use ThreadPoolExecutor to process all courses in parallel with a reasonable number of workers
        max_workers = min(10, len(cms_courses))  # Limit to 10 workers maximum
        logger.info(
            f"Processing {len(cms_courses)} courses with {max_workers} parallel workers"
        )

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_course = {
                executor.submit(fetch_course_data, course): course
                for course in cms_courses
            }

            # Process results as they complete
            for future in concurrent.futures.as_completed(future_to_course):
                course = future_to_course[future]
                course_name = course.get("course_name", "Unknown")
                try:
                    course_data = future.result()
                    if course_data:
                        with course_data_lock:
                            all_course_data.append(course_data)
                        logger.info(
                            f"Successfully processed data for course: {course_name}"
                        )
                except Exception as e:
                    logger.error(
                        f"Error processing course {course_name}: {e}", exc_info=True
                    )

        logger.info(f"Fetched data for {len(all_course_data)} courses")

        # Prepare the input for the AI model - optimize to reduce data size
        # Only include essential data to make processing faster
        optimized_guc_notifications = []
        for notification in guc_notifications:
            # Only include notifications that might contain event information
            lower_body = notification.get("body", "").lower()
            lower_subject = notification.get("subject", "").lower()
            lower_title = notification.get("title", "").lower()

            # Check if this notification might contain event information
            keywords = [
                "quiz",
                "quizzes",
                "quizes",
                "exam",
                "exams",
                "examination",
                "midterm",
                "final",
                "assignment",
                "assignments",
                "hw",
                "homework",
                "project",
                "projects",
                "presentation",
                "demo",
                "compensation",
                "compensate",
                "make-up",
                "makeup",
                "make up",
                "deadline",
                "deadlines",
                "due",
                "dues",
                "submit",
                "submission",
                "turn in",
                "hand in",
                "test",
                "tests",
                "assessment",
                "assessments",
                "report",
                "reports",
                "lab",
                "labs",
                "practical",
                "task",
                "tasks",
                "exercise",
                "exercises",
                "grade",
                "grades",
                "graded",
                "marks",
                "score",
                "evaluation",
                "evaluate",
                "assessed",
            ]
            if any(
                keyword in lower_body
                or keyword in lower_subject
                or keyword in lower_title
                for keyword in keywords
            ):
                # Only include essential fields
                optimized_guc_notifications.append(
                    {
                        "title": notification.get("title", ""),
                        "subject": notification.get("subject", ""),
                        "body": notification.get("body", ""),
                        "date": notification.get("date", ""),
                    }
                )

        # Optimize course data
        optimized_course_data = []
        for course in all_course_data:
            course_content = []
            course_name = course.get("course_name", "")

            # Log what we're processing
            logger.info(f"Processing course data for: {course_name}")
            logger.info(
                f"Content items: {len(course.get('content', []))}, Announcements: {len(course.get('announcements', []))}"
            )

            # Filter course content to only include items that might contain event information
            for content_item in course.get("content", []):
                if content_item and isinstance(content_item, dict):
                    # Check if this content item might contain event information
                    item_title = content_item.get("title", "").lower()
                    item_description = (
                        content_item.get("description", "").lower()
                        if isinstance(content_item.get("description"), str)
                        else ""
                    )
                    item_content = (
                        content_item.get("content", "").lower()
                        if isinstance(content_item.get("content"), str)
                        else ""
                    )
                    item_week = content_item.get("week", "").lower()

                    # Combine all text fields for keyword search
                    combined_text = (
                        f"{item_title} {item_description} {item_content} {item_week}"
                    )

                    keywords = [
                        "quiz",
                        "quizzes",
                        "quizes",
                        "exam",
                        "exams",
                        "examination",
                        "midterm",
                        "final",
                        "assignment",
                        "assignments",
                        "hw",
                        "homework",
                        "project",
                        "projects",
                        "presentation",
                        "demo",
                        "compensation",
                        "compensate",
                        "make-up",
                        "makeup",
                        "make up",
                        "deadline",
                        "deadlines",
                        "due",
                        "dues",
                        "submit",
                        "submission",
                        "turn in",
                        "hand in",
                        "test",
                        "tests",
                        "assessment",
                        "assessments",
                        "report",
                        "reports",
                        "lab",
                        "labs",
                        "practical",
                        "task",
                        "tasks",
                        "exercise",
                        "exercises",
                        "grade",
                        "grades",
                        "graded",
                        "marks",
                        "score",
                        "evaluation",
                        "evaluate",
                        "assessed",
                    ]

                    if any(keyword in combined_text for keyword in keywords):
                        # Only include essential fields
                        optimized_item = {
                            "title": content_item.get("title", ""),
                            "course": course_name,
                        }

                        # Add optional fields if they exist
                        if content_item.get("description"):
                            optimized_item["description"] = content_item.get(
                                "description"
                            )
                        if content_item.get("content"):
                            optimized_item["content"] = content_item.get("content")
                        if content_item.get("week"):
                            optimized_item["week"] = content_item.get("week")
                        if content_item.get("type"):
                            optimized_item["type"] = content_item.get("type")

                        course_content.append(optimized_item)

            # Filter announcements
            course_announcements = []
            for announcement in course.get("announcements", []):
                if announcement and isinstance(announcement, dict):
                    # Check if this announcement might contain event information
                    announcement_title = announcement.get("title", "").lower()
                    announcement_content = (
                        announcement.get("content", "").lower()
                        if isinstance(announcement.get("content"), str)
                        else ""
                    )

                    # Combine all text fields for keyword search
                    combined_text = f"{announcement_title} {announcement_content}"

                    keywords = [
                        "quiz",
                        "quizzes",
                        "quizes",
                        "exam",
                        "exams",
                        "examination",
                        "midterm",
                        "final",
                        "assignment",
                        "assignments",
                        "hw",
                        "homework",
                        "project",
                        "projects",
                        "presentation",
                        "demo",
                        "compensation",
                        "compensate",
                        "make-up",
                        "makeup",
                        "make up",
                        "deadline",
                        "deadlines",
                        "due",
                        "dues",
                        "submit",
                        "submission",
                        "turn in",
                        "hand in",
                        "test",
                        "tests",
                        "assessment",
                        "assessments",
                        "report",
                        "reports",
                        "lab",
                        "labs",
                        "practical",
                        "task",
                        "tasks",
                        "exercise",
                        "exercises",
                        "grade",
                        "grades",
                        "graded",
                        "marks",
                        "score",
                        "evaluation",
                        "evaluate",
                        "assessed",
                    ]

                    if any(keyword in combined_text for keyword in keywords):
                        # Only include essential fields
                        optimized_announcement = {
                            "title": announcement.get("title", ""),
                            "content": announcement.get("content", ""),
                            "course": course_name,
                        }

                        course_announcements.append(optimized_announcement)

            # Only add course if it has relevant content
            if course_content or course_announcements:
                optimized_course_data.append(
                    {
                        "course_name": course_name,
                        "content": course_content,
                        "announcements": course_announcements,
                    }
                )
                logger.info(
                    f"Added optimized data for course: {course_name} with {len(course_content)} content items and {len(course_announcements)} announcements"
                )

        # Log optimization metrics before AI processing
        logger.info(f"Preparing to process data with AI")

        # Log optimization metrics
        original_notifications_count = len(guc_notifications)
        optimized_notifications_count = len(optimized_guc_notifications)
        original_courses_count = len(all_course_data)
        optimized_courses_count = len(optimized_course_data)

        logger.info(
            f"Data optimization: Notifications reduced from {original_notifications_count} to {optimized_notifications_count}"
        )
        logger.info(
            f"Data optimization: Courses reduced from {original_courses_count} to {optimized_courses_count}"
        )

        # Estimate data size reduction
        original_data_size = len(
            json.dumps(
                {"guc_notifications": guc_notifications, "courses": all_course_data}
            )
        )
        optimized_data_size = len(
            json.dumps(
                {
                    "guc_notifications": optimized_guc_notifications,
                    "courses": optimized_course_data,
                }
            )
        )
        size_reduction_percent = (
            ((original_data_size - optimized_data_size) / original_data_size) * 100
            if original_data_size > 0
            else 0
        )

        logger.info(
            f"Data optimization: Size reduced from {original_data_size} to {optimized_data_size} bytes ({size_reduction_percent:.1f}% reduction)"
        )

        # Create a more detailed prompt for the AI model to improve accuracy while maintaining speed
        prompt_text = f"""
        You are an AI specialized in extracting academic events from university notifications and course content.

        TASK: Extract ALL upcoming events between {current_date.strftime('%Y-%m-%d')} and {cutoff_date.strftime('%Y-%m-%d')}.

        SOURCES:
        1. GUC notifications (official university announcements)
        2. CMS content (course materials, announcements, etc.)

        EVENT TYPES TO EXTRACT (be thorough and inclusive):
        - Quizzes (including pop quizzes, online quizzes, in-class quizzes)
        - Exams (midterms, finals, make-up exams)
        - Assignments (homework, problem sets, reports)
        - Projects (submissions, presentations, demos)
        - Compensation lectures/tutorials (make-up sessions)
        - Deadlines (any submission deadlines)
        - Tests (any form of assessment)

        OUTPUT FORMAT:
        Return a JSON array of events. Each event must have these fields:
        - upcoming_title: Clear descriptive title
        - upcoming_date: Date in readable format (e.g., "Monday, April 15, 2025" or "15/04/2025")
        - upcoming_type: One of [Quiz, Exam, Assignment, Project, Compensation, Deadline, Test, Other]
        - upcoming_source: Course name or department
        - upcoming_description: Brief description of the event

        EXTRACTION RULES:
        1. Be thorough - extract ALL possible academic events
        2. Extract dates from message content, not just metadata
        3. Include only events with specific dates mentioned
        4. Filter out past events (before {current_date.strftime('%Y-%m-%d')})
        5. Exclude regular lectures/tutorials (unless they are compensation/make-up sessions)
        6. Remove duplicates (same event mentioned in multiple places)
        7. If a date format is ambiguous, prefer the format that makes the event in the future
        8. Look for keywords like: quiz, exam, test, midterm, final, assignment, project, deadline, due, submit, presentation

        Return ONLY the JSON array with no additional text.
        """

        # Generate content using the Gemini AI model with retries
        for attempt in range(MAX_RETRIES):
            try:
                logger.info(f"Attempt {attempt + 1} to call Gemini AI")

                # Send the data to the AI model
                # Optimize the API call for maximum speed and reliability
                api_start_time = time.time()

                try:
                    # Prepare a more optimized input for the AI
                    # Only include the most relevant data to reduce processing time
                    minimal_input = {
                        "date": current_date.strftime("%Y-%m-%d"),
                        "cutoff": cutoff_date.strftime("%Y-%m-%d"),
                        "notifications": [
                            {
                                "title": n.get("title", ""),
                                "body": n.get("body", ""),
                                "subject": n.get("subject", ""),
                            }
                            for n in optimized_guc_notifications[
                                :100
                            ]  # Include more notifications for better coverage
                        ],
                        "courses": [
                            {
                                "name": c.get("course_name", ""),
                                "items": [
                                    {
                                        "title": i.get("title", ""),
                                        "description": i.get("description", ""),
                                        "content": (
                                            i.get("content", "")[:500]
                                            if i.get("content")
                                            else ""
                                        ),  # Limit content length
                                    }
                                    for i in (
                                        c.get("content", [])
                                        + c.get("announcements", [])
                                    )[
                                        :30
                                    ]  # Include more items per course
                                ],
                            }
                            for c in optimized_course_data[:15]  # Include more courses
                        ],
                    }

                    # Call the API with optimized settings
                    response = client.models.generate_content(
                        model="gemini-2.0-flash-lite",  # Use the fastest model
                        contents=[prompt_text, json.dumps(minimal_input, default=str)],
                        config=types.GenerateContentConfig(
                            response_mime_type="application/json",
                            temperature=0.1,  # Low temperature for more deterministic results
                            max_output_tokens=8192,  # Allow larger output for more events
                            top_p=0.95,  # Slightly more creative to catch edge cases
                        ),
                    )

                    api_time = time.time() - api_start_time
                    logger.info(f"API call completed in {api_time:.2f} seconds")

                except Exception as e:
                    logger.error(f"API call failed: {e}")
                    # If we're on the last attempt, return empty list
                    if attempt == MAX_RETRIES - 1:
                        return []
                    # Otherwise, continue to the next attempt
                    time.sleep(BACKOFF_TIME)
                    continue

                # Parse the response
                if hasattr(response, "text"):
                    try:
                        events = json.loads(response.text)
                        logger.info(
                            f"Successfully extracted {len(events)} upcoming events using Gemini AI"
                        )
                        return events
                    except json.JSONDecodeError as e:
                        logger.error(
                            f"Failed to parse Gemini AI response as JSON: {e}",
                            exc_info=True,
                        )

                        # The response might be truncated but still contain valid JSON objects
                        # Let's try to fix it by adding the missing closing bracket if needed
                        response_text = response.text

                        # Check if it's a truncated JSON array
                        if response_text.startswith("[") and not response_text.endswith(
                            "]"
                        ):
                            try:
                                # Try to fix the JSON by adding the missing closing bracket
                                fixed_json = response_text + "]"
                                events = json.loads(fixed_json)
                                logger.info(
                                    f"Successfully extracted {len(events)} upcoming events from fixed JSON"
                                )
                                return events
                            except json.JSONDecodeError:
                                # If that didn't work, try more advanced parsing
                                pass

                        # Try to extract valid JSON objects from the response
                        try:
                            import re

                            # Extract all JSON objects
                            objects = []
                            pattern = r"{[^{}]*(?:{[^{}]*}[^{}]*)*}"
                            matches = re.finditer(pattern, response_text)

                            for match in matches:
                                try:
                                    obj = json.loads(match.group(0))
                                    # Check if the object has the required fields
                                    required_fields = [
                                        "upcoming_title",
                                        "upcoming_date",
                                        "upcoming_type",
                                        "upcoming_source",
                                    ]

                                    # If all required fields are present, add it directly
                                    if all(key in obj for key in required_fields):
                                        # Make sure it has a description field
                                        if "upcoming_description" not in obj:
                                            obj["upcoming_description"] = (
                                                "No description available"
                                            )
                                        objects.append(obj)
                                    # If it's missing only the description, add it with a default description
                                    elif (
                                        all(key in obj for key in required_fields)
                                        and "upcoming_description" not in obj
                                    ):
                                        obj["upcoming_description"] = (
                                            "No description available"
                                        )
                                        objects.append(obj)
                                except json.JSONDecodeError:
                                    continue

                            if objects:
                                logger.info(
                                    f"Successfully extracted {len(objects)} upcoming events from partial JSON"
                                )
                                return objects
                        except Exception as ex:
                            logger.error(
                                f"Error extracting JSON objects: {ex}", exc_info=True
                            )

                        # If we're on the last attempt, try one more approach - extract what we can
                        if attempt == MAX_RETRIES - 1:
                            try:
                                # Look for complete event objects in the response
                                import re

                                # More flexible pattern to match event objects with any order of fields
                                pattern = r'{\s*"upcoming_[^}]+":[^}]+,\s*"upcoming_[^}]+":[^}]+,\s*"upcoming_[^}]+":[^}]+,\s*"upcoming_[^}]+":[^}]+,\s*"upcoming_[^}]+":[^}]+\s*}'
                                matches = re.finditer(pattern, response_text)

                                events = []
                                for match in matches:
                                    try:
                                        event = json.loads(match.group(0))
                                        events.append(event)
                                    except:
                                        pass

                                if events:
                                    logger.info(
                                        f"Successfully extracted {len(events)} upcoming events from regex parsing"
                                    )
                                    return events
                                return []
                            except Exception:
                                return []
                else:
                    logger.error("Gemini AI response does not contain text")
                    # If we're on the last attempt, return empty list
                    if attempt == MAX_RETRIES - 1:
                        return []

                # If we get here, we need to retry
                time.sleep(BACKOFF_TIME * (attempt + 1))  # Exponential backoff

            except Exception as e:
                logger.error(
                    f"Error calling Gemini AI (attempt {attempt + 1}): {e}",
                    exc_info=True,
                )
                # If we're on the last attempt, return empty list
                if attempt == MAX_RETRIES - 1:
                    return []

                # If we get here, we need to retry
                time.sleep(BACKOFF_TIME * (attempt + 1))  # Exponential backoff

    except Exception as e:
        logger.error(f"Error in extract_upcoming_events_ai: {e}", exc_info=True)
        return []


def extract_upcoming_events_traditional(
    guc_data: Dict[str, Any],
    cms_courses: List[Dict[str, Any]],
    days_ahead: int = 30,
) -> List[Dict[str, Any]]:
    """
    Extract upcoming events using traditional rule-based methods.
    This is a fallback when AI extraction is disabled.

    Args:
        guc_data: The GUC data containing notifications
        cms_courses: List of CMS courses
        days_ahead: Number of days ahead to consider for upcoming events

    Returns:
        List of upcoming events in the required format
    """
    logger.info("Using traditional rule-based extraction as fallback")
    events = []
    current_date = datetime.now()
    cutoff_date = current_date + timedelta(days=days_ahead)

    # Process GUC notifications
    if guc_data and "notifications" in guc_data:
        for notification in guc_data.get("notifications", []):
            # Check if this notification might contain event information
            title = notification.get("title", "").lower()
            subject = notification.get("subject", "").lower()
            body = notification.get("body", "").lower()

            # Skip if no content
            if not (title or subject or body):
                continue

            # Check for event keywords
            event_keywords = [
                "quiz",
                "exam",
                "assignment",
                "project",
                "compensation",
                "deadline",
                "due",
                "submit",
            ]
            if not any(
                keyword in title or keyword in subject or keyword in body
                for keyword in event_keywords
            ):
                continue

            # Try to extract a date from the content
            import re

            date_patterns = [
                r"(\d{1,2})[/-](\d{1,2})[/-](\d{2,4})",  # DD/MM/YYYY or DD-MM-YYYY
                r"(\d{1,2})\.(\d{1,2})\.(\d{2,4})",  # DD.MM.YYYY
                r"(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(\d{2,4})",  # DD Month YYYY
                r"(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\s+(\d{1,2})[/-](\d{1,2})",  # Weekday DD/MM
            ]

            found_date = None
            for pattern in date_patterns:
                matches = re.findall(pattern, body, re.IGNORECASE)
                if matches:
                    # Use the first match as the date
                    found_date = matches[0]
                    break

            if not found_date:
                continue

            # Determine event type
            event_type = "Other"
            if "quiz" in body or "quiz" in title:
                event_type = "Quiz"
            elif (
                "exam" in body
                or "exam" in title
                or "midterm" in body
                or "final" in body
            ):
                event_type = "Exam"
            elif "assignment" in body or "assignment" in title or "homework" in body:
                event_type = "Assignment"
            elif "project" in body or "project" in title:
                event_type = "Project"
            elif "compensation" in body or "compensation" in title or "make-up" in body:
                event_type = "Compensation"

            # Create event object
            event = {
                "upcoming_title": notification.get("title", "Unknown Event"),
                "upcoming_date": str(found_date),  # Simple string representation
                "upcoming_type": event_type,
                "upcoming_source": notification.get("department", "GUC"),
                "upcoming_description": notification.get("body", "")[
                    :200
                ],  # Truncate long descriptions
            }

            events.append(event)

    logger.info(f"Traditional extraction found {len(events)} events")
    return events


def _prepare_guc_notifications(guc_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Prepare GUC notifications for the AI model.
    Only includes notifications from the past month.

    Args:
        guc_data: The GUC data containing notifications

    Returns:
        List of prepared notifications
    """
    notifications = []

    # Check if guc_data contains notifications
    if not guc_data or "notifications" not in guc_data:
        return notifications

    # Get notifications from the last month
    one_month_ago = datetime.now() - timedelta(days=30)

    for notification in guc_data.get("notifications", []):
        # Try to parse the notification date
        notification_date = None
        date_str = notification.get("date", "")
        if date_str:
            try:
                notification_date = datetime.strptime(date_str, "%m/%d/%Y")
            except ValueError:
                pass

        # Skip notifications older than one month
        if notification_date and notification_date < one_month_ago:
            continue

        # Add the notification to the list
        notifications.append(
            {
                "title": notification.get("title", ""),
                "subject": notification.get("subject", ""),
                "body": notification.get("body", ""),
                "date": date_str,
                "staff": notification.get("staff", ""),
                "department": notification.get("department", ""),
            }
        )

    return notifications
