<html><head><style>body {transition: opacity ease-in 0.2s; } 
    body[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } 
    </style><title>
        Student Portal SIS
    </title><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta http-equiv="Content-Language" content="en"><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no"><meta name="description" content="This is an example dashboard created using build-in elements and components."><meta name="msapplication-tap-highlight" content="no"><link href="/CSS/architectui-html-pro-1.4.0/main.87c0748b313a1dda75f5.css" rel="stylesheet">
       
    
        <style>
            .app-header__logo .logo-src {
                background: url(/CSS/architectui-html-pro-1.4.0/assets/images/guc_logo_og.png);
                background-repeat: no-repeat;
                width: 118px;
                height: 50px;
            }
    
            .app-sidebar {
                background-image: linear-gradient(to right,    	 #00665c,  	 #00665c ) !important;
            }
    
            .hamburger-inner, .hamburger-inner::before, .hamburger-inner::after{
                background-color: #00665c !important;
            }
    
            .main-menu {
                color: rgba(255,255,255,0.7) !important;
            }
    
                .main-menu:hover {
                    background: rgba(255,255,255,0.15) !important;
                    color: #fff !important;
                }
    
            .vertical-nav-menu ul > li > a.mm-active {
                color: #fff;
                background: rgba(255,255,255,0.15);
                font-weight: normal;
            }
    
            .vertical-nav-menu ul > li > a:hover {
                background: rgba(255,255,255,0.15) !important;
                color: #fff !important;
            }
        </style>
        <style>
            .datepicker-container.datepicker-dropdown.datepicker-top-left {
                z-index: 1500 !important;
            }
        </style>
        <style>
            .modalx {
                display: none;
                position: fixed;
                z-index: 1000;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                background: rgba( 255, 255, 255, .7 )
                /*url('http://i.stack.imgur.com/FhHRx.gif')*/
                50% 50% no-repeat;
            }
    
            /* When the body has the loading class, we turn
                   the scrollbar off with overflow:hidden */
            body.loading .modalx {
                overflow: hidden;
            }
    
            /* Anytime the body has the loading class, our
                   modal element will be visible */
            body.loading .modalx {
                display: block;
            }
        </style>
        <style>
            .app-header__content {
                height: 90px !important;
            }
              .vertical-nav-menu ul > li > a {
                    color: white !important;
                }
        </style>
        
        
        
    
        <style>
            .icon_application-form {
                    background-image: url('/CSS/Icons/icons8-list-30.png');
                    height: 30px !important;
                    width: 30px !important;
                    display: block;
                        background-repeat: no-repeat;
                    /* Other styles here */
                }
             .icon_double-down {
                    background: url('/CSS/Icons/icons8-collapse-arrow-35.png');
                    /*height: 25px !important;
                    width: 25px !important;*/
                    display: block;
                    background-repeat: no-repeat;
                    opacity:0.7;
                    /* Other styles here */
                }
    
              .icon_terms-and-conditions {
                    background: url('/CSS/Icons/terms-and-conditions.png');
                    height: 40px;
                    width: 40px;
                    display: block;
                    background-repeat: no-repeat;
                    opacity:0.7;
                    /* Other styles here */
                }
    
              .vertical-nav-menu i.metismenu-state-icon{
                      height: 50%;
              }
    
        </style>
    <style>@-webkit-keyframes swal2-show{0%{-webkit-transform:scale(.7);transform:scale(.7)}45%{-webkit-transform:scale(1.05);transform:scale(1.05)}80%{-webkit-transform:scale(.95);transform:scale(.95)}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes swal2-show{0%{-webkit-transform:scale(.7);transform:scale(.7)}45%{-webkit-transform:scale(1.05);transform:scale(1.05)}80%{-webkit-transform:scale(.95);transform:scale(.95)}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes swal2-hide{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}100%{-webkit-transform:scale(.5);transform:scale(.5);opacity:0}}@keyframes swal2-hide{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}100%{-webkit-transform:scale(.5);transform:scale(.5);opacity:0}}@-webkit-keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.875em;width:1.5625em}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.875em;width:1.5625em}}@-webkit-keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@-webkit-keyframes swal2-rotate-success-circular-line{0%{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}5%{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}12%{-webkit-transform:rotate(-405deg);transform:rotate(-405deg)}100%{-webkit-transform:rotate(-405deg);transform:rotate(-405deg)}}@keyframes swal2-rotate-success-circular-line{0%{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}5%{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}12%{-webkit-transform:rotate(-405deg);transform:rotate(-405deg)}100%{-webkit-transform:rotate(-405deg);transform:rotate(-405deg)}}@-webkit-keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;-webkit-transform:scale(.4);transform:scale(.4);opacity:0}50%{margin-top:1.625em;-webkit-transform:scale(.4);transform:scale(.4);opacity:0}80%{margin-top:-.375em;-webkit-transform:scale(1.15);transform:scale(1.15)}100%{margin-top:0;-webkit-transform:scale(1);transform:scale(1);opacity:1}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;-webkit-transform:scale(.4);transform:scale(.4);opacity:0}50%{margin-top:1.625em;-webkit-transform:scale(.4);transform:scale(.4);opacity:0}80%{margin-top:-.375em;-webkit-transform:scale(1.15);transform:scale(1.15)}100%{margin-top:0;-webkit-transform:scale(1);transform:scale(1);opacity:1}}@-webkit-keyframes swal2-animate-error-icon{0%{-webkit-transform:rotateX(100deg);transform:rotateX(100deg);opacity:0}100%{-webkit-transform:rotateX(0);transform:rotateX(0);opacity:1}}@keyframes swal2-animate-error-icon{0%{-webkit-transform:rotateX(100deg);transform:rotateX(100deg);opacity:0}100%{-webkit-transform:rotateX(0);transform:rotateX(0);opacity:1}}body.swal2-toast-shown .swal2-container{background-color:transparent}body.swal2-toast-shown .swal2-container.swal2-shown{background-color:transparent}body.swal2-toast-shown .swal2-container.swal2-top{top:0;right:auto;bottom:auto;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{top:0;right:0;bottom:auto;left:auto}body.swal2-toast-shown .swal2-container.swal2-top-left,body.swal2-toast-shown .swal2-container.swal2-top-start{top:0;right:auto;bottom:auto;left:0}body.swal2-toast-shown .swal2-container.swal2-center-left,body.swal2-toast-shown .swal2-container.swal2-center-start{top:50%;right:auto;bottom:auto;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{top:50%;right:auto;bottom:auto;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{top:50%;right:0;bottom:auto;left:auto;-webkit-transform:translateY(-50%);transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-left,body.swal2-toast-shown .swal2-container.swal2-bottom-start{top:auto;right:auto;bottom:0;left:0}body.swal2-toast-shown .swal2-container.swal2-bottom{top:auto;right:auto;bottom:0;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{top:auto;right:0;bottom:0;left:auto}body.swal2-toast-column .swal2-toast{flex-direction:column;align-items:stretch}body.swal2-toast-column .swal2-toast .swal2-actions{flex:1;align-self:stretch;height:2.2em;margin-top:.3125em}body.swal2-toast-column .swal2-toast .swal2-loading{justify-content:center}body.swal2-toast-column .swal2-toast .swal2-input{height:2em;margin:.3125em auto;font-size:1em}body.swal2-toast-column .swal2-toast .swal2-validation-message{font-size:1em}.swal2-popup.swal2-toast{flex-direction:row;align-items:center;width:auto;padding:.625em;box-shadow:0 0 .625em #d9d9d9;overflow-y:hidden}.swal2-popup.swal2-toast .swal2-header{flex-direction:row}.swal2-popup.swal2-toast .swal2-title{flex-grow:1;justify-content:flex-start;margin:0 .6em;font-size:1em}.swal2-popup.swal2-toast .swal2-footer{margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-popup.swal2-toast .swal2-close{position:initial;width:.8em;height:.8em;line-height:.8}.swal2-popup.swal2-toast .swal2-content{justify-content:flex-start;font-size:1em}.swal2-popup.swal2-toast .swal2-icon{width:2em;min-width:2em;height:2em;margin:0}.swal2-popup.swal2-toast .swal2-icon-text{font-size:2em;font-weight:700;line-height:1em}.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-popup.swal2-toast .swal2-actions{height:auto;margin:0 .3125em}.swal2-popup.swal2-toast .swal2-styled{margin:0 .3125em;padding:.3125em .625em;font-size:1em}.swal2-popup.swal2-toast .swal2-styled:focus{box-shadow:0 0 0 .0625em #fff,0 0 0 .125em rgba(50,100,150,.4)}.swal2-popup.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:2em;height:2.8125em;-webkit-transform:rotate(45deg);transform:rotate(45deg);border-radius:50%}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.25em;left:-.9375em;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transform-origin:2em 2em;transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.25em;left:.9375em;-webkit-transform-origin:0 2em;transform-origin:0 2em;border-radius:0 4em 4em 0}.swal2-popup.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-popup.swal2-toast.swal2-show{-webkit-animation:showSweetToast .5s;animation:showSweetToast .5s}.swal2-popup.swal2-toast.swal2-hide{-webkit-animation:hideSweetToast .2s forwards;animation:hideSweetToast .2s forwards}.swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-tip{-webkit-animation:animate-toast-success-tip .75s;animation:animate-toast-success-tip .75s}.swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-long{-webkit-animation:animate-toast-success-long .75s;animation:animate-toast-success-long .75s}@-webkit-keyframes showSweetToast{0%{-webkit-transform:translateY(-.625em) rotateZ(2deg);transform:translateY(-.625em) rotateZ(2deg);opacity:0}33%{-webkit-transform:translateY(0) rotateZ(-2deg);transform:translateY(0) rotateZ(-2deg);opacity:.5}66%{-webkit-transform:translateY(.3125em) rotateZ(2deg);transform:translateY(.3125em) rotateZ(2deg);opacity:.7}100%{-webkit-transform:translateY(0) rotateZ(0);transform:translateY(0) rotateZ(0);opacity:1}}@keyframes showSweetToast{0%{-webkit-transform:translateY(-.625em) rotateZ(2deg);transform:translateY(-.625em) rotateZ(2deg);opacity:0}33%{-webkit-transform:translateY(0) rotateZ(-2deg);transform:translateY(0) rotateZ(-2deg);opacity:.5}66%{-webkit-transform:translateY(.3125em) rotateZ(2deg);transform:translateY(.3125em) rotateZ(2deg);opacity:.7}100%{-webkit-transform:translateY(0) rotateZ(0);transform:translateY(0) rotateZ(0);opacity:1}}@-webkit-keyframes hideSweetToast{0%{opacity:1}33%{opacity:.5}100%{-webkit-transform:rotateZ(1deg);transform:rotateZ(1deg);opacity:0}}@keyframes hideSweetToast{0%{opacity:1}33%{opacity:.5}100%{-webkit-transform:rotateZ(1deg);transform:rotateZ(1deg);opacity:0}}@-webkit-keyframes animate-toast-success-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes animate-toast-success-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@-webkit-keyframes animate-toast-success-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@keyframes animate-toast-success-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto!important}body.swal2-no-backdrop .swal2-shown{top:auto;right:auto;bottom:auto;left:auto;background-color:transparent}body.swal2-no-backdrop .swal2-shown>.swal2-modal{box-shadow:0 0 10px rgba(0,0,0,.4)}body.swal2-no-backdrop .swal2-shown.swal2-top{top:0;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-top-left,body.swal2-no-backdrop .swal2-shown.swal2-top-start{top:0;left:0}body.swal2-no-backdrop .swal2-shown.swal2-top-end,body.swal2-no-backdrop .swal2-shown.swal2-top-right{top:0;right:0}body.swal2-no-backdrop .swal2-shown.swal2-center{top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}body.swal2-no-backdrop .swal2-shown.swal2-center-left,body.swal2-no-backdrop .swal2-shown.swal2-center-start{top:50%;left:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-center-end,body.swal2-no-backdrop .swal2-shown.swal2-center-right{top:50%;right:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-bottom{bottom:0;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-bottom-left,body.swal2-no-backdrop .swal2-shown.swal2-bottom-start{bottom:0;left:0}body.swal2-no-backdrop .swal2-shown.swal2-bottom-end,body.swal2-no-backdrop .swal2-shown.swal2-bottom-right{right:0;bottom:0}.swal2-container{display:flex;position:fixed;top:0;right:0;bottom:0;left:0;flex-direction:row;align-items:center;justify-content:center;padding:10px;background-color:transparent;z-index:1060;overflow-x:hidden;-webkit-overflow-scrolling:touch}.swal2-container.swal2-top{align-items:flex-start}.swal2-container.swal2-top-left,.swal2-container.swal2-top-start{align-items:flex-start;justify-content:flex-start}.swal2-container.swal2-top-end,.swal2-container.swal2-top-right{align-items:flex-start;justify-content:flex-end}.swal2-container.swal2-center{align-items:center}.swal2-container.swal2-center-left,.swal2-container.swal2-center-start{align-items:center;justify-content:flex-start}.swal2-container.swal2-center-end,.swal2-container.swal2-center-right{align-items:center;justify-content:flex-end}.swal2-container.swal2-bottom{align-items:flex-end}.swal2-container.swal2-bottom-left,.swal2-container.swal2-bottom-start{align-items:flex-end;justify-content:flex-start}.swal2-container.swal2-bottom-end,.swal2-container.swal2-bottom-right{align-items:flex-end;justify-content:flex-end}.swal2-container.swal2-grow-fullscreen>.swal2-modal{display:flex!important;flex:1;align-self:stretch;justify-content:center}.swal2-container.swal2-grow-row>.swal2-modal{display:flex!important;flex:1;align-content:center;justify-content:center}.swal2-container.swal2-grow-column{flex:1;flex-direction:column}.swal2-container.swal2-grow-column.swal2-bottom,.swal2-container.swal2-grow-column.swal2-center,.swal2-container.swal2-grow-column.swal2-top{align-items:center}.swal2-container.swal2-grow-column.swal2-bottom-left,.swal2-container.swal2-grow-column.swal2-bottom-start,.swal2-container.swal2-grow-column.swal2-center-left,.swal2-container.swal2-grow-column.swal2-center-start,.swal2-container.swal2-grow-column.swal2-top-left,.swal2-container.swal2-grow-column.swal2-top-start{align-items:flex-start}.swal2-container.swal2-grow-column.swal2-bottom-end,.swal2-container.swal2-grow-column.swal2-bottom-right,.swal2-container.swal2-grow-column.swal2-center-end,.swal2-container.swal2-grow-column.swal2-center-right,.swal2-container.swal2-grow-column.swal2-top-end,.swal2-container.swal2-grow-column.swal2-top-right{align-items:flex-end}.swal2-container.swal2-grow-column>.swal2-modal{display:flex!important;flex:1;align-content:center;justify-content:center}.swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen)>.swal2-modal{margin:auto}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-container .swal2-modal{margin:0!important}}.swal2-container.swal2-fade{transition:background-color .1s}.swal2-container.swal2-shown{background-color:rgba(0,0,0,.4)}.swal2-popup{display:none;position:relative;flex-direction:column;justify-content:center;width:32em;max-width:100%;padding:1.25em;border-radius:.3125em;background:#fff;font-family:inherit;font-size:1rem;box-sizing:border-box}.swal2-popup:focus{outline:0}.swal2-popup.swal2-loading{overflow-y:hidden}.swal2-popup .swal2-header{display:flex;flex-direction:column;align-items:center}.swal2-popup .swal2-title{display:block;position:relative;max-width:100%;margin:0 0 .4em;padding:0;color:#595959;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word}.swal2-popup .swal2-actions{flex-wrap:wrap;align-items:center;justify-content:center;margin:1.25em auto 0;z-index:1}.swal2-popup .swal2-actions:not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}.swal2-popup .swal2-actions:not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0,0,0,.1),rgba(0,0,0,.1))}.swal2-popup .swal2-actions:not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0,0,0,.2),rgba(0,0,0,.2))}.swal2-popup .swal2-actions.swal2-loading .swal2-styled.swal2-confirm{width:2.5em;height:2.5em;margin:.46875em;padding:0;border:.25em solid transparent;border-radius:100%;border-color:transparent;background-color:transparent!important;color:transparent;cursor:default;box-sizing:border-box;-webkit-animation:swal2-rotate-loading 1.5s linear 0s infinite normal;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.swal2-popup .swal2-actions.swal2-loading .swal2-styled.swal2-cancel{margin-right:30px;margin-left:30px}.swal2-popup .swal2-actions.swal2-loading :not(.swal2-styled).swal2-confirm::after{display:inline-block;width:15px;height:15px;margin-left:5px;border:3px solid #999;border-radius:50%;border-right-color:transparent;box-shadow:1px 1px 1px #fff;content:'';-webkit-animation:swal2-rotate-loading 1.5s linear 0s infinite normal;animation:swal2-rotate-loading 1.5s linear 0s infinite normal}.swal2-popup .swal2-styled{margin:.3125em;padding:.625em 2em;font-weight:500;box-shadow:none}.swal2-popup .swal2-styled:not([disabled]){cursor:pointer}.swal2-popup .swal2-styled.swal2-confirm{border:0;border-radius:.25em;background:initial;background-color:#3085d6;color:#fff;font-size:1.0625em}.swal2-popup .swal2-styled.swal2-cancel{border:0;border-radius:.25em;background:initial;background-color:#aaa;color:#fff;font-size:1.0625em}.swal2-popup .swal2-styled:focus{outline:0;box-shadow:0 0 0 2px #fff,0 0 0 4px rgba(50,100,150,.4)}.swal2-popup .swal2-styled::-moz-focus-inner{border:0}.swal2-popup .swal2-footer{justify-content:center;margin:1.25em 0 0;padding:1em 0 0;border-top:1px solid #eee;color:#545454;font-size:1em}.swal2-popup .swal2-image{max-width:100%;margin:1.25em auto}.swal2-popup .swal2-close{position:absolute;top:0;right:0;justify-content:center;width:1.2em;height:1.2em;padding:0;transition:color .1s ease-out;border:none;border-radius:0;outline:initial;background:0 0;color:#ccc;font-family:serif;font-size:2.5em;line-height:1.2;cursor:pointer;overflow:hidden}.swal2-popup .swal2-close:hover{-webkit-transform:none;transform:none;color:#f27474}.swal2-popup>.swal2-checkbox,.swal2-popup>.swal2-file,.swal2-popup>.swal2-input,.swal2-popup>.swal2-radio,.swal2-popup>.swal2-select,.swal2-popup>.swal2-textarea{display:none}.swal2-popup .swal2-content{justify-content:center;margin:0;padding:0;color:#545454;font-size:1.125em;font-weight:300;line-height:normal;z-index:1;word-wrap:break-word}.swal2-popup #swal2-content{text-align:center}.swal2-popup .swal2-checkbox,.swal2-popup .swal2-file,.swal2-popup .swal2-input,.swal2-popup .swal2-radio,.swal2-popup .swal2-select,.swal2-popup .swal2-textarea{margin:1em auto}.swal2-popup .swal2-file,.swal2-popup .swal2-input,.swal2-popup .swal2-textarea{width:100%;transition:border-color .3s,box-shadow .3s;border:1px solid #d9d9d9;border-radius:.1875em;font-size:1.125em;box-shadow:inset 0 1px 1px rgba(0,0,0,.06);box-sizing:border-box}.swal2-popup .swal2-file.swal2-inputerror,.swal2-popup .swal2-input.swal2-inputerror,.swal2-popup .swal2-textarea.swal2-inputerror{border-color:#f27474!important;box-shadow:0 0 2px #f27474!important}.swal2-popup .swal2-file:focus,.swal2-popup .swal2-input:focus,.swal2-popup .swal2-textarea:focus{border:1px solid #b4dbed;outline:0;box-shadow:0 0 3px #c4e6f5}.swal2-popup .swal2-file::-webkit-input-placeholder,.swal2-popup .swal2-input::-webkit-input-placeholder,.swal2-popup .swal2-textarea::-webkit-input-placeholder{color:#ccc}.swal2-popup .swal2-file:-ms-input-placeholder,.swal2-popup .swal2-input:-ms-input-placeholder,.swal2-popup .swal2-textarea:-ms-input-placeholder{color:#ccc}.swal2-popup .swal2-file::-ms-input-placeholder,.swal2-popup .swal2-input::-ms-input-placeholder,.swal2-popup .swal2-textarea::-ms-input-placeholder{color:#ccc}.swal2-popup .swal2-file::placeholder,.swal2-popup .swal2-input::placeholder,.swal2-popup .swal2-textarea::placeholder{color:#ccc}.swal2-popup .swal2-range input{width:80%}.swal2-popup .swal2-range output{width:20%;font-weight:600;text-align:center}.swal2-popup .swal2-range input,.swal2-popup .swal2-range output{height:2.625em;margin:1em auto;padding:0;font-size:1.125em;line-height:2.625em}.swal2-popup .swal2-input{height:2.625em;padding:0 .75em}.swal2-popup .swal2-input[type=number]{max-width:10em}.swal2-popup .swal2-file{font-size:1.125em}.swal2-popup .swal2-textarea{height:6.75em;padding:.75em}.swal2-popup .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;color:#545454;font-size:1.125em}.swal2-popup .swal2-checkbox,.swal2-popup .swal2-radio{align-items:center;justify-content:center}.swal2-popup .swal2-checkbox label,.swal2-popup .swal2-radio label{margin:0 .6em;font-size:1.125em}.swal2-popup .swal2-checkbox input,.swal2-popup .swal2-radio input{margin:0 .4em}.swal2-popup .swal2-validation-message{display:none;align-items:center;justify-content:center;padding:.625em;background:#f0f0f0;color:#666;font-size:1em;font-weight:300;overflow:hidden}.swal2-popup .swal2-validation-message::before{display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center;content:'!';zoom:normal}@supports (-ms-accelerator:true){.swal2-range input{width:100%!important}.swal2-range output{display:none}}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-range input{width:100%!important}.swal2-range output{display:none}}@-moz-document url-prefix(){.swal2-close:focus{outline:2px solid rgba(50,100,150,.4)}}.swal2-icon{position:relative;justify-content:center;width:5em;height:5em;margin:1.25em auto 1.875em;border:.25em solid transparent;border-radius:50%;line-height:5em;cursor:default;box-sizing:content-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;zoom:normal}.swal2-icon-text{font-size:3.75em}.swal2-icon.swal2-error{border-color:#f27474}.swal2-icon.swal2-error .swal2-x-mark{position:relative;flex-grow:1}.swal2-icon.swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.swal2-icon.swal2-warning{border-color:#facea8;color:#f8bb86}.swal2-icon.swal2-info{border-color:#9de0f6;color:#3fc3ee}.swal2-icon.swal2-question{border-color:#c9dae1;color:#87adbd}.swal2-icon.swal2-success{border-color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;-webkit-transform:rotate(45deg);transform:rotate(45deg);border-radius:50%}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.4375em;left:-2.0635em;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transform-origin:3.75em 3.75em;transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.6875em;left:1.875em;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transform-origin:0 3.75em;transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}.swal2-icon.swal2-success .swal2-success-ring{position:absolute;top:-.25em;left:-.25em;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%;z-index:2;box-sizing:content-box}.swal2-icon.swal2-success .swal2-success-fix{position:absolute;top:.5em;left:1.625em;width:.4375em;height:5.625em;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);z-index:1}.swal2-icon.swal2-success [class^=swal2-success-line]{display:block;position:absolute;height:.3125em;border-radius:.125em;background-color:#a5dc86;z-index:2}.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.875em;width:1.5625em;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.swal2-icon.swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.swal2-progresssteps{align-items:center;margin:0 0 1.25em;padding:0;font-weight:600}.swal2-progresssteps li{display:inline-block;position:relative}.swal2-progresssteps .swal2-progresscircle{width:2em;height:2em;border-radius:2em;background:#3085d6;color:#fff;line-height:2em;text-align:center;z-index:20}.swal2-progresssteps .swal2-progresscircle:first-child{margin-left:0}.swal2-progresssteps .swal2-progresscircle:last-child{margin-right:0}.swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep{background:#3085d6}.swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep~.swal2-progresscircle{background:#add8e6}.swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep~.swal2-progressline{background:#add8e6}.swal2-progresssteps .swal2-progressline{width:2.5em;height:.4em;margin:0 -1px;background:#3085d6;z-index:10}[class^=swal2]{-webkit-tap-highlight-color:transparent}.swal2-show{-webkit-animation:swal2-show .3s;animation:swal2-show .3s}.swal2-show.swal2-noanimation{-webkit-animation:none;animation:none}.swal2-hide{-webkit-animation:swal2-hide .15s forwards;animation:swal2-hide .15s forwards}.swal2-hide.swal2-noanimation{-webkit-animation:none;animation:none}.swal2-rtl .swal2-close{right:auto;left:0}.swal2-animate-success-icon .swal2-success-line-tip{-webkit-animation:swal2-animate-success-line-tip .75s;animation:swal2-animate-success-line-tip .75s}.swal2-animate-success-icon .swal2-success-line-long{-webkit-animation:swal2-animate-success-line-long .75s;animation:swal2-animate-success-line-long .75s}.swal2-animate-success-icon .swal2-success-circular-line-right{-webkit-animation:swal2-rotate-success-circular-line 4.25s ease-in;animation:swal2-rotate-success-circular-line 4.25s ease-in}.swal2-animate-error-icon{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-animate-error-icon .swal2-x-mark{-webkit-animation:swal2-animate-error-x-mark .5s;animation:swal2-animate-error-x-mark .5s}@-webkit-keyframes swal2-rotate-loading{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes swal2-rotate-loading{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@media print{body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow-y:scroll!important}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container{position:initial!important}}</style><style type="text/css">/* Chart.js */
    @-webkit-keyframes chartjs-render-animation{from{opacity:0.99}to{opacity:1}}@keyframes chartjs-render-animation{from{opacity:0.99}to{opacity:1}}.chartjs-render-monitor{-webkit-animation:chartjs-render-animation 0.001s;animation:chartjs-render-animation 0.001s;}</style><style type="text/css">.apexcharts-canvas {
      position: relative;
      user-select: none;
      /* cannot give overflow: hidden as it will crop tooltips which overflow outside chart area */
    }
    
    /* scrollbar is not visible by default for legend, hence forcing the visibility */
    .apexcharts-canvas ::-webkit-scrollbar {
      -webkit-appearance: none;
      width: 6px;
    }
    .apexcharts-canvas ::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background-color: rgba(0,0,0,.5);
      box-shadow: 0 0 1px rgba(255,255,255,.5);
      -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
    }
    
    .apexcharts-inner {
      position: relative;
    }
    
    .legend-mouseover-inactive {
      transition: 0.15s ease all;
      opacity: 0.20;
    }
    
    .apexcharts-series-collapsed {
      opacity: 0;
    }
    
    .apexcharts-gridline, .apexcharts-text {
      pointer-events: none;
    }
    
    .apexcharts-tooltip {
      border-radius: 5px;
      box-shadow: 2px 2px 6px -4px #999;
      cursor: default;
      font-size: 14px;
      left: 62px;
      opacity: 0;
      pointer-events: none;
      position: absolute;
      top: 20px;
      overflow: hidden;
      white-space: nowrap;
      z-index: 12;
      transition: 0.15s ease all;
    }
    .apexcharts-tooltip.light {
      border: 1px solid #e3e3e3;
      background: rgba(255, 255, 255, 0.96);
    }
    .apexcharts-tooltip.dark {
      color: #fff;
      background: rgba(30,30,30, 0.8);
    }
    
    .apexcharts-tooltip .apexcharts-marker,
    .apexcharts-area-series .apexcharts-area,
    .apexcharts-line {
      pointer-events: none;
    }
    
    .apexcharts-tooltip.active {
      opacity: 1;
      transition: 0.15s ease all;
    }
    
    .apexcharts-tooltip-title {
      padding: 6px;
      font-size: 15px;
      margin-bottom: 4px;
    }
    .apexcharts-tooltip.light .apexcharts-tooltip-title {
      background: #ECEFF1;
      border-bottom: 1px solid #ddd;
    }
    .apexcharts-tooltip.dark .apexcharts-tooltip-title {
      background: rgba(0, 0, 0, 0.7);
      border-bottom: 1px solid #222;
    }
    
    .apexcharts-tooltip-text-value,
    .apexcharts-tooltip-text-z-value {
      display: inline-block;
      font-weight: 600;
      margin-left: 5px;
    }
    
    .apexcharts-tooltip-text-z-label:empty,
    .apexcharts-tooltip-text-z-value:empty {
      display: none;
    }
    
    .apexcharts-tooltip-text-value, 
    .apexcharts-tooltip-text-z-value {
      font-weight: 600;
    }
    
    .apexcharts-tooltip-marker {
      width: 12px;
      height: 12px;
      position: relative;
      top: 0px;
      margin-right: 10px;
      border-radius: 50%;
    }
    
    .apexcharts-tooltip-series-group {
      padding: 0 10px;
      display: none;
      text-align: left;
      justify-content: left;
      align-items: center;
    }
    
    .apexcharts-tooltip-series-group.active .apexcharts-tooltip-marker {
      opacity: 1;
    }
    .apexcharts-tooltip-series-group.active, .apexcharts-tooltip-series-group:last-child {
      padding-bottom: 4px;
    }
    .apexcharts-tooltip-y-group {
      padding: 6px 0 5px;
    }
    .apexcharts-tooltip-candlestick {
      padding: 4px 8px;
    }
    .apexcharts-tooltip-candlestick > div {
      margin: 4px 0;
    }
    .apexcharts-tooltip-candlestick span.value {
      font-weight: bold;
    }
    
    .apexcharts-xaxistooltip {
      opacity: 0;
      padding: 9px 10px;
      pointer-events: none;
      color: #373d3f;
      font-size: 13px;
      text-align: center;
      border-radius: 2px;
      position: absolute;
      z-index: 10;
        background: #ECEFF1;
      border: 1px solid #90A4AE;
      transition: 0.15s ease all;
    }
    
    .apexcharts-xaxistooltip:after, .apexcharts-xaxistooltip:before {
        left: 50%;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
    }
    
    .apexcharts-xaxistooltip:after {
        border-color: rgba(236, 239, 241, 0);
        border-width: 6px;
        margin-left: -6px;
    }
    .apexcharts-xaxistooltip:before {
        border-color: rgba(144, 164, 174, 0);
        border-width: 7px;
        margin-left: -7px;
    }
    
    .apexcharts-xaxistooltip-bottom:after, .apexcharts-xaxistooltip-bottom:before {
      bottom: 100%;
    }
    
    .apexcharts-xaxistooltip-bottom:after {
      border-bottom-color: #ECEFF1;
    }
    .apexcharts-xaxistooltip-bottom:before {
      border-bottom-color: #90A4AE;
    }
    
    .apexcharts-xaxistooltip-top:after, .apexcharts-xaxistooltip-top:before {
      top: 100%;
    }
    .apexcharts-xaxistooltip-top:after {
      border-top-color: #ECEFF1;
    }
    .apexcharts-xaxistooltip-top:before {
      border-top-color: #90A4AE;
    }
    
    .apexcharts-xaxistooltip.active {
      opacity: 1;
      transition: 0.15s ease all;
    }
    
    .apexcharts-yaxistooltip {
      opacity: 0;
      padding: 4px 10px;
      pointer-events: none;
      color: #373d3f;
      font-size: 13px;
      text-align: center;
      border-radius: 2px;
      position: absolute;
      z-index: 10;
        background: #ECEFF1;
      border: 1px solid #90A4AE;
    }
    
    .apexcharts-yaxistooltip:after, .apexcharts-yaxistooltip:before {
        top: 50%;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
    }
    .apexcharts-yaxistooltip:after {
        border-color: rgba(236, 239, 241, 0);
        border-width: 6px;
        margin-top: -6px;
    }
    .apexcharts-yaxistooltip:before {
        border-color: rgba(144, 164, 174, 0);
        border-width: 7px;
        margin-top: -7px;
    }
    
    .apexcharts-yaxistooltip-left:after, .apexcharts-yaxistooltip-left:before {
      left: 100%;
    }
    .apexcharts-yaxistooltip-left:after {
      border-left-color: #ECEFF1;
    }
    .apexcharts-yaxistooltip-left:before {
      border-left-color: #90A4AE;
    }
    
    .apexcharts-yaxistooltip-right:after, .apexcharts-yaxistooltip-right:before {
      right: 100%;
    }
    .apexcharts-yaxistooltip-right:after {
      border-right-color: #ECEFF1;
    }
    .apexcharts-yaxistooltip-right:before {
      border-right-color: #90A4AE;
    }
    
    .apexcharts-yaxistooltip.active {
      opacity: 1;
    }
    
    .apexcharts-xcrosshairs, .apexcharts-ycrosshairs {
      pointer-events: none;
      opacity: 0;
      transition: 0.15s ease all;
    }
    
    .apexcharts-xcrosshairs.active, .apexcharts-ycrosshairs.active {
      opacity: 1;
      transition: 0.15s ease all;
    }
    
    .apexcharts-ycrosshairs-hidden {
      opacity: 0;
    }
    
    .apexcharts-zoom-rect {
      pointer-events: none;
    }
    .apexcharts-selection-rect {
      cursor: move;
    }
    
    .svg_select_points, .svg_select_points_rot {
      opacity: 0;
      visibility: hidden;
    }
    .svg_select_points_l, .svg_select_points_r {
      cursor: ew-resize;
      opacity: 1;
      visibility: visible;
      fill: #888;
    }
    .apexcharts-canvas.zoomable .hovering-zoom {
      cursor: crosshair
    }
    .apexcharts-canvas.zoomable .hovering-pan {
      cursor: move
    }
    
    .apexcharts-xaxis,
    .apexcharts-yaxis {
      pointer-events: none;
    }
    
    .apexcharts-zoom-icon, 
    .apexcharts-zoom-in-icon,
    .apexcharts-zoom-out-icon,
    .apexcharts-reset-zoom-icon, 
    .apexcharts-pan-icon, 
    .apexcharts-selection-icon,
    .apexcharts-menu-icon {
      cursor: pointer;
      width: 20px;
      height: 20px;
      text-align: center;
    }
    
    
    .apexcharts-zoom-icon svg, 
    .apexcharts-zoom-in-icon svg,
    .apexcharts-zoom-out-icon svg,
    .apexcharts-reset-zoom-icon svg,
    .apexcharts-menu-icon svg {
      fill: #6E8192;
    }
    .apexcharts-selection-icon svg {
      fill: #444;
      transform: scale(0.86)
    }
    .apexcharts-zoom-icon.selected svg, 
    .apexcharts-selection-icon.selected svg, 
    .apexcharts-reset-zoom-icon.selected svg {
      fill: #008FFB;
    }
    .apexcharts-selection-icon:not(.selected):hover svg,
    .apexcharts-zoom-icon:not(.selected):hover svg, 
    .apexcharts-zoom-in-icon:hover svg, 
    .apexcharts-zoom-out-icon:hover svg, 
    .apexcharts-reset-zoom-icon:hover svg, 
    .apexcharts-menu-icon:hover svg {
      fill: #333;
    }
    
    .apexcharts-selection-icon, .apexcharts-menu-icon {
      margin-right: 3px;
      margin-left: 5px;
      position: relative;
      top: 1px;
    }
    .apexcharts-reset-zoom-icon {
      margin-left: 7px;
    }
    .apexcharts-zoom-icon {
      transform: scale(1);
    }
    
    .apexcharts-zoom-in-icon, .apexcharts-zoom-out-icon {
      transform: scale(0.8)
    }
    
    .apexcharts-zoom-out-icon {
      margin-right: 3px;
    }
    
    .apexcharts-pan-icon {
      transform: scale(0.72);
      position: relative;
      left: 1px;
      top: 0px;
    }
    .apexcharts-pan-icon svg {
      fill: #fff;
      stroke: #6E8192;
      stroke-width: 2;
    }
    .apexcharts-pan-icon.selected svg {
      stroke: #008FFB;
    }
    .apexcharts-pan-icon:not(.selected):hover svg {
      stroke: #333;
    }
    
    .apexcharts-toolbar {
      position: absolute;
      z-index: 11;
      top: 0px;
      right: 3px;
      max-width: 176px;
      text-align: right;
      border-radius: 3px;
      padding: 0px 6px 2px 6px;
      display: flex;
      justify-content: space-between;
      align-items: center; 
    }
    
    .apexcharts-toolbar svg {
      pointer-events: none;
    }
    
    .apexcharts-menu {
      background: #fff;
      position: absolute;
      top: 100%;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 3px;
      right: 10px;
      opacity: 0;
      min-width: 110px;
      transition: 0.15s ease all;
      pointer-events: none;
    }
    
    .apexcharts-menu.open {
      opacity: 1;
      pointer-events: all;
      transition: 0.15s ease all;
    }
    
    .apexcharts-menu-item {
      padding: 6px 7px;
      font-size: 12px;
      cursor: pointer;
    }
    .apexcharts-menu-item:hover {
      background: #eee;
    }
    
    @media screen and (min-width: 768px) {
      .apexcharts-toolbar {
        /*opacity: 0;*/
      }
    
      .apexcharts-canvas:hover .apexcharts-toolbar {
        opacity: 1;
      } 
    }
    
    .apexcharts-datalabel.hidden {
      opacity: 0;
    }
    
    .apexcharts-pie-label,
    .apexcharts-datalabel, .apexcharts-datalabel-label, .apexcharts-datalabel-value {
      cursor: default;
      pointer-events: none;
    }
    
    .apexcharts-pie-label-delay {
      opacity: 0;
      animation-name: opaque;
      animation-duration: 0.3s;
      animation-fill-mode: forwards;
      animation-timing-function: ease;
    }
    
    .apexcharts-canvas .hidden {
      opacity: 0;
    }
    
    .apexcharts-hide .apexcharts-series-points {
      opacity: 0;
    }
    
    .apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
    .apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events, .apexcharts-radar-series path, .apexcharts-radar-series polygon {
      pointer-events: none;
    }
    
    /* markers */
    
    .apexcharts-marker {
      transition: 0.15s ease all;
    }
    
    @keyframes opaque {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }</style><style type="text/css">.jqstooltip { position: absolute;left: 0px;top: 0px;visibility: hidden;background: rgb(0, 0, 0) transparent;background-color: rgba(0,0,0,0.6);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);-ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000)";color: white;font: 10px arial, san serif;text-align: left;white-space: nowrap;padding: 5px;border: 1px solid white;box-sizing: content-box;z-index: 10000;}.jqsfield { color: white;font: 10px arial, san serif;text-align: left;}</style><script id="simplify-jobs-page-script" src="chrome-extension://pbanhockgagggenencehbnadejlgchfc/js/pageScript.bundle.js"></script></head>
    <body cz-shortcut-listen="true" data-new-gr-c-s-check-loaded="14.1235.0" data-gr-ext-installed="">
        <form method="post" action="./SearchAcademicScheduled_001.aspx" id="form1">
    <div class="aspNetHidden">
    <input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value="">
    <input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value="">
    <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="u1g0waqnRFbFp6hBRinnd2y+ZUyFF3WHidzF5vx448TW2OvEQZdnZrEd3ojPc5nsc6nzYEN8m+KfIEtuQsZ92dYGTLShQAt1qNY1JsvECHxQ8abGzGvsiiTO3hfX2SvWr7phwFsr3N2ZIw1gEdSNTCF+DpmDSHR4JDftYD7dz0DGmp+1mWSZsq/DW3SfnbfQRmMAV7tHlA6sov6KPUA+UUKj5dL9Q22OOXVO2u74ifRTMePEYc/G4X0+5w5qkWlIyXt6PCeZBQSQP6nKiPAMOtyPAh02jda0eaOFMyVVQAiCoP1T8f3LMHOErZHuJi0AwIQtehLsiDRcWsrw+fvQzijRJF3uFNU0tV6XNlFsvKIuTCtyQlecXJMs7vFD7OlK3UQHEWvXBXsq9B7vlBsdRHY6g8irz41AtuvNIKLf8Iaon4rckfjnxwF9mCNxdCssWUV3jdmR9teUxJuDpuCNv+cfWa/6hxhm4ICTcOvdP1CAUVg3EZacdGxRhcvocgGBMnbw5tcCtMQNDDw8n4Au+lno7RjbN9Qz83taShRYF8HkObYN1bqM0OoT2qNJg0fRVPNcI40EHUo5KciDHh7CyMwqYVhGEK1HGwBohVPOgzVqBedZ7nYP+dGdJt7XpQUBC3sqkxs6H2+kNN5oGjG3I4qkEq6nlNsaJdov7ruv2+B8PCKcv669FTwHRjWKzl/fPZ/xATK9qn9kijCO5oCqHCvJ0QfZgymvREFmEaslXa3o2VPGTyx9EsxecdeyH9cP4ENAeDyspLcrvxZtuCZA7knMBOpcsCdVJBmugaP/4k4lKP4VE5fbGe395cYJWaqH/a8JI69xni14UafAtpWELUHjyE3tq5g1RvDMXL5wd+7C3Qb1VhEr/+5rRMKfCT1gPXLidCA1YnPMxQcSjObnCVRJZPtfkyv3h0IrHJMwVCQiDngZ4nwgNbcPAm+wBYUU4IlujZH6/zfXMABqXPzVbBmd41rJ+zQnY3ATJKGC4CvqrUpjrUFAlSMEGBz2ai71vWOa0KidhobvACXn5/+K8l0UPbIchsJradcu+3+vZ3VvH2ME4QmZofCY++oA5q/VVYq+jHkBgawW68NIPYA/8I9ScZ2EeeMsyh3yRhipH81iScHT3fPtlXgLw5IsRpuP21YqesNVmpy98TOnnrQnGrhyUqFL54eH9NfL+x37YPcTSQTvBrKSoU4A9q71Mvfn4H9+ub2POUpXB8Hv8BX7gArM4zWHpwTRAK4S1qEBr2rIXv5NFySnIXlco+a2Ed6Bbwbmb0wnWgOUsNf8LpPO2VMZxmusgumJgvCj5+GnuONapzWixJ7XgcMElzCVmEBdJh8bGDfTiaBrmXjHyH2MmzZFCCuK+JUVUyoXpvTM9SOkWwUAVCBQERw1iL2RXGKS2Rxi6dfYfgZuzBnUEvoOIeedrNx935Yin48FCOobp4Mhs5iYtOyAiBHiKOfIzYvU6jh8m0kkJXHlt6F+wEaqwEyX0z8MeCfDXkfs9U/O9/5OKja4xgO/Kjk5NJXAbLM+JUKGjUudvGFEkTdJuCP4Mk/+ExICGrrtwfrO72x6OQYmBqvi8GkKLanxAGFCUHssL5GqtfP0uX4x3r5tflADBsAfL9dV4+pZAK7H/cyM76koepETHdLXtAwfUciMT6Gw0Rx3uvHkGVRfcY6/5+JjJSgO7IyS4XcBBrZRma2ySOy59247o2C6rFxO5PY2xy/qmLtlC0uSYK8pgOSvZ/ur9CkzEatGjCP5nxT2p4MVMlGaiQ1g2X8v2jphFLYqCI8fxRh3Rr8xmI51ZE+v9jrOh/Dx1/Q7svTnOHs8iaNlNctRwR7PdQw4HjGbTbQSL4/p7yQwxQ4RDKlLpQM3YBEtgmmEK436NCpPxEGWJ7QkAKL/k7qf0N3qk2zNM4J/pA36N3EEiUgnFjhhO7WpZ/7cguXENdx+uqB6nOiMS4+B7blHEMiGP39u6TQsfWsb+/IpmSfNge9MgGanA3pBtG7ouWDjwAsuBhmJ0fIcNSa9BGUkm7rfTBXzMYWy3tUMGEv/wBMEzcXEYYQ/6o0q5gp6MnOQUKeKgDZKhaNKJqo7PICPMf2ZNfRusUfDdKI6ComilkYTtc+5sNbcIbi3vmPFlaxFE4BIx6Sj1zXNPIrxZLwpRweUzrTXIOn9ZjY3H3f2NoRzYGX9SbolZVno+r03sJjFuZJFihB5U0WtllAB2/DNYKvbf1ShOTHKThmBXmOsxwrZbOwAvepySLVSo2yCCsMiqMzjrEme9qsE6b+ByB+LQ17mo1uS88SH04Fo7X6cODn2z7HsKLOW8JLHOn1r3P92ON+pg1zXByjn95CuQsRLtNR1YylEYNaGrTNWxsrEbsqFQm76ABl5jUCWg+21cVbgUcy5pmPSS95yJFk9bbPdJC0goC7nvFgAdGVvDv0uIjuIiic/EqTa9T2/XzDlKsmMek+voRiWyNDRgYG9Dk3yOMl98daVvGS9jaw3Km+DdLHrXO5dsAV9ucpxh0ce/Zi/yNu7v/bRwPo28ATjOl8Kx7ZEA+BHgm/s6Ro7msXSpi0qEdc0T7DJkC5qZWxkYTNdMOeQ0q2boyWovcedCb3OsjbDqMne1Ylvn1Jcq8qSEwwBuUovl++Fz1yW8QFmDL2ablj6NBRiD4/WM1C0CvBpH1IFf+C7Ke/ArsyrAV4mbTj3OqiSFLDmXUhiKDLOFb9Z3xJAvBtIcgSPyovihU1b+JmJ1yVSr22q7ovIzMvB0cCZ1gkRruKRiRegzcYI9wdtih6eDuP0e8YZw8UANE+Ow/vjX7Ti4LJjWn+oaGOxAlQYwx6tetfauLBFZu23cpFPYvgTPJ6rbEdICCJZ8K/glopWTfmFFc0qLgIS3gAPMal3xa2jZFRtVpdV3j2Zw2XPsSMOEL+HhqXir4dKCunnN2THYlpgM/UqegdAInp70v4U57RAns17Wzo+Wm/oRWmIqEeabMoqlVZdizDPJGUfgEtmUJEo031nTK5XqhOwkctOxJ/vlIhkU17t/If9njYTFydGJmVgb04+0pqulhwJipu7wzeZVdZ1r9PMSMZqfs0VHnD4JA+MrKvEOZV6YLHVc5gSFXHYBTiiwwVjbYaBFkAPK1egdwXuKgSa5fQL1YGWo51HqNRepcudz75iznmc4nH8F1XlvYpxBb4IXx+1TyS/fgQHldvU5Ebeh52vrw9Lwj6vkjJLdQXVHJxZXV34bH0OaLLsxf44QLUFN5jERY4wXHVBRWxVYYS6toD8uY74fBLYhNiKcPKCbP82ktjB5HePqsZRKsldX83CN/nsDp5Fw3McG5heEr7wcf8XleJHeyNaOqL/mfq3OPjyVl43+ScKLVd5uYcOWkOawdvDACh49mBCy+oAb1aCIJ7SVka5nXTXxGpvRrI02wSy4yMzbLVy2KaMgWsbdRfBMCoV4C1qWGP5f9bgZub1IV6RgLvvfjhreEPxjgRV1NsWSPB5hwXMS2jnDRtTtred6XQcqa3IWVX8XC9AyBCElg518ZX4ZMIsEOktDf7LktIZiee77/i5NZ2QLBUS4+yMBwjXtgNc0Ag8hvCB6l1m47MkLiq3QLbLXFvD0nvnzbXG4rqXH+Yf/YuX6JJ0MMBBY8lacZcEsw5AmmHvZmT6LnAQsIANGhLE+F2nIvFjlDxsEZBA4lWo5wyV5k5/M5lfp3VDpB7hF/SH6yrdHAYEjMbL9lfJ9JC5xadzmESKoBcF3j7Zd4gyVcZSYo2pjGrYcqsk0WdjXdSrCYnvh14LmxT7rhbJCgJHf9sKxcmcOiN4prnVbSFE1GsCzAlHfavl0L1LKUmOhyxXUFy6NKMRIOspb+71tjKaewLTrIb6FsOLWrNXcnPA+H34uftOeodGEJUJVbXEqiZEYKWCm5p/5D55fPoyOvJR6C0eO3VDQ2xU9wZaWArAwuln7c63FQwADdYUQbl99+pf0RLHX62bPO3Oljvg71dWV1mt+LnQomkSX2W0BG6HsyLGS4yb7gWn3p1EA3kyubdCH/GU6iob8myUh0Gl5WFBDWIbU1pHcJvQSAgxAuMlYeMQa39O3+712+zD8rj6cI/M0vgspOHW7u57b9axrQagI/IFzlDkS8ILRe2lcAGsTqouECb5eDgvFfWgFLNFqdP5kMgXb9d96fGhxbjptJofNwy7FmgG+XPlhdVMQ+ys7V/pn7Qv1sdLri6AM/UQfjjRyEu+DVtmhOAcEMFemBwNK6L8EI5o2C1LuGTbnSAPQ0Ddg2wa8aNaBe0L0Z4CTJGwGAIlH0L08Sz7muS2KGZHNt3iBR7dop2njyYh7pKN9wzZ6qNr54DHX9O7OdCpvVBCZ3omu2Iowb32BIC9xy400UQarpZDqKi7Z6OUoIkLZT1wTiiycuIbMlrCJUylCHrBYaWwGjn2sbiFkXeuVt7xrYKaKyMSc73xRsXQ93jQaEaL70sp0EFOIsCAgtJZ1S0WYgzkpXTzCnMgrlG7/b9jcN0jm23fi0lQ+/tDWrzc18qfmy5DY9mE3Fi4GmTv743QdVZ1OgNm5dsqDGD6fd0h2BDvdIxg6G6OuQ3WboUyYrl6V71c+c36hLIe2uMxSdm+uRMhsIgnWriW9us62aNKfh0N1R1LxTltsemhCJ6V/ICgmS6PIS+3jtmLQL/bbxVcQ1xAdimkhje0JZAJTEObWToNeNifmCvkc3g+M24MG+kqFi+S4EGlgdzV5rmOG3BWTzgsaUmj8tfy7q0fT16QiSt8uTMrnzhHxGCPKqdFOWDVCVdb0XhfnS3Zg+8YvGyedXTNRql8Z3DuvI4hGF85oQzcOnJZvVb/t6pzkqnmrpbi4UIuikicetCsGbr+Xgq14PsiFG6sCQ93u62gUUgkLdR/VYcfn63Xu2JaaaNX0RImv4zd4ukEMthKAUQC7cfWu0cCEb3VOxLiCCjKdrc8fmPl09AugvFB5tzwpmXx57ZAhpaEK90YRMYI07DqGb07gXd0XDFTgY+k5wJxJK80Tt9nn9coxepSk6ddSzcuIKqUMAREA/eFekQjziEVwtfUqC3PfCy9Wsm5XUhgrcNeGyflxvqZUAAXWg50u095062EWcArBL0o6G0Vh8yzChFc606fOy4a80BWQgPLfyGI+G504oJn2Bs8Vkz3yEh8NEv6B4hXcECTlrWPgPd+butC/gx/Fgt6adQezTWjbbZ1fsDNp2Fd98FIxNOPW7nS7LP4CegBZ7dXg7tKt0l4KKa823mmOcClGfF7+zdICBQXY8+p+MhV9SGKyCmcuZ/vhQIk1JSS9mCKD9/BwH7zipeQEvsjDyBYyMf+NO3n2sdd07BwjN/FSS/mCxAZuDIxg38YDeAm34ZwkxN/YwescbnWlHqBLDjHk9f7TeUixC98/t+mxw2vFJMZkkA66OBeSvB0NyqT0EGvukJQotAH3sTO8SfwxNA07E/zkqn438K5d8aw1B1ZNCJ3pzunBZG5XrrVIMWJrpNiBYPfz2um2XSUeToKu1rE5vjM9BSL0fIe+Oe1c2TwXnI+yeYr81RaTEwSUbsum+5dU6o2XzL7jB9rUxXk6YrQtT8O2daNoDw83qdTk3FUCSZ/0xPHhL19Xckdk4Z4s+klFD3R5fq4hP9ezennSgma3zbmr0kgJnedBZTsYrAcgvp9yvi2pZ9BiDGJJMERt/Ije6spALku+u9KbqickzYIngr/tOpli9gnKO02CH7fyl6LhZ3olWDFDESHJaFWe3WRy5MklJn7IRpS4i25Kl958afrEa7ReHdpdBCA9+/ZR0SYOkTsCAcnOaguL8CPPsl1JCYBWcA6OLFamknDolGfCs+Ao+2TfaFi0xhApS1kiny7MoWhZ/yOiZZ72ggyNbJc9sMb/NmXhK26VQrg5nyIk2o0BXfZdbjGh71tpuzy8gcPapZyiAJs1CtaSNfhUoQLvagke5Xw72oJ1vYEqmYJbueMoBLKc8eQ4i5v4iGYC5JV1xuJdWmEF7ZQyFdae9CetIZtMAtBIhi69RpYRyqm468nN2ulAhD6ckTsJ7uw2vcVVjR6ytC/ejNuI+k6Ra+kaMtIJDxPtVR95ySJu0IQF4JxsbaQMu3du3pQ1nwv/wH1hT3WcC+hKF2jeK4r8OEMzlXOgWujOBMnC1P7qpAudJNGg2scGkQlul2iQphdKRBN42Q7zxQf2MaA/UwMKwd3PydQ1VS8ZUas2yrv3xecIaCEJzMH9zcp1vuk/pHgoj8srkuUSc2edKjHmlcJ5CK1vDfRaFJV28UBO7iuFwDivJj0JTsmGzsmEBNgHxdz2jvCKas05vKdDL4YZYUASNtBMI5vNFczD3eoyF0zlDbyo0fPK7TJGHxWLQUEjgPWR54p9ZcOslqcgximjTeB76Ko6w1YlgiK30W80DAPzALwuZK+6IrSdRgFGLkFv29YwtLlC1WxZyESHvVdjm1oLeGaCwLWo73EolgPGcuw5oH1gyCO7tvy+xPH+Mc7AM1r6SKDgwI5IP78EASoclcewuTMSHQUXgFE4BPROE9vKkQzKJRJyOa9DHUuUxiylJEr+Vmx6Lr2+4STl6xN3JjA8QOXGbbh+ummw6FxWzIUW9ok4y/cRsCvkdjL/49SJA/AfsANDDI+WqvlUbnEnL1KztfpOrkW7fjSR2vbHC6nEl6N4qfIc6Ww65Iq5O560W/esHmYFDwr1N/959r5lKDHp+mW+3XQW9OiGOLzSshYeRIAOlMAX7s2oYdhBkoo9H49tm1bO2pAIDF3GPIdlaxCmPff2NTWfBRc7FuQBG5DRVY873CUSHHfwWjpVibSEgTEx5V4Vvk4RGHVyiR3Vy6dibjWWnkQjc5RhRhbfRxtmP1zSX3E+e+6jvcRgTNwzDGta3nnhWf4kLBjPbT4kFiIbL6RjD9MQF3P2lxQKWjzNHq7MvAe9w+Bp+eRsCKXqVbJkm5E6aD59sOHgzGbK9kHctY7iklmNFrS341NAzwDsXpu3bzha4wXp5hrffz9mW0+nQ85ByvkbV/wNSSCFe8aFQKbfm4HXdfZ5eprctEu40C7z26iyNSZh+kaS9JXCxSUrN4n+jW1bDYsJSxWmoo4QFlttI7RFm4jEaR0hOWn5Fz4w849L+6XgWoldsbQwMztUkUCkwCs0thvfPqfyFizOzmuPog4EQTw6Yy+C394d2/qutRm8HwkvRdgUFhVOS9Pln/wEZQOE6hudPNGDOTTAQ5+nr4g/MxjzXsOLZ4u9AkSgNl7rL7Wmw7os6sHGCdfRyZAlSfuXFGYcyn89tcJiFEZw5a5r50aVo/flg9Xjzn2PmrwqSrPtbICUYSoCjUANTwIR8rtklvJzWEuaKgLMLEa+frbfwCjwQgUL3xGInYh06or+y55RdJuw6Z8X4epx0tM4/9oPbML90pUNZ7FGe6OamJ8m69fT8sy94X+80uGCgMSPDDb4k4GFo4Z4UlN+l7fYUvlqyjJxCyuLokT87/oA0PtvzDOvRMkQQphZ9enD2LE69Siq4EMkbuwFTbUdduY7qBgQ0UxY0Od9V1mUkJuCKMGEFHTEpBNawJN+YH14s2O5HRfGaGIguJxqkUa/lZvSrxtMlmTzOLxy4RMH/ydUREPtvivlaBgUM6ubx4rDzaQvTjWb5GyyZtZskGSPTdZmKuWn53Q55Iqc4PcHM1shtNv9ybvYoTg7XUEKoBW6N1rn42Jp7KqBOjlUN1rDPFk+TM2lgDibzj5r34QdPdnbNsDpbIphinw/8AJ8F0omDHO3HXroADEik0D1lDEmFLiAEW8AGc2CBiGtapVNIfgvEi/jmx1WsMFPwXg2jicvXF+DVUxm4X8ibfekz822WLiqr5OE85xyTdkRZM0p8BFrknRAOcbfisDVYVCkJZWw4+yxGGWrrFF5wPNoNN5N4+xrCMQFpbrB6AqM0FuiW5kAiEBydljDRyZJv4bMB2D6TwIWJaYFNv4xURs4f8HI1zzuC7YPnkOrYGhGHBdg1diSLpyt3Q8rP0eUy1SkxOYhKb2b4EphCSlubdTmAy0rZZuRhM71mscUbSzTzORAK1UNgMfa6/n6u487pZJ/vkBnYgSu2ALqIppHvQtGJ3JHOSA8mOBkU7NtWAUm44LdP2tZfu1HxIpDcF1M+G7TTYIE+KhPHk0oZNjFOZKDpQhPssU6G8Md0crBSHaE2c6oKSyeTeQEZm2sipgAfHhrZbRfEtv01ZCqQWNgu7pnjSFZ2szMAqTQWl8mwJxeWLKb36khgM606EGKp+JWnPvTVOCLciCHwtD+pycFNEtft5iETTRWGPb+MTaBzXVs8OdGHyTxOdt//pZzZbdYI7+d7gCohKpyzFrt4BOrC5NchXlcChgBEKXJv+l/nbJ9aTKR7S5xociKxptrjYJS5izYaBbHLj0f+JJylOP0k6DQGnvCkFjtDrve1mQgUVZmK6UQJzAE05Ifb6+tYe5uOpc+NYm8reuBven5BaU/jBxm5WT4e6MByHQSV5/2QfySw/56lO/ewRSFqWovtdYcrI9M3RAX5Qfhl1YjbAVIsjxd15EeypnXNpBJObCAingLp8aMkHAaKkZbt9ZXDZ1JQHwumIL5Bn6JZwaIofN49ILXYroLATASTE5iKy/SlEcH+X7oAdYjbSLjRkjAN79mQde7t8QueDvkF2amGRLgNHWLN/ENAIgiJGemcR+vptuLyk3hjFq8Pf1MAMAQnXFQxSDswi37Z1fZQ646/JimXz1vkGfnm3CJ58suy8A2CHPVqYpmRoYLPfYyTgT1qj7/xla1khP54G/P42EOJoNmPVbppkKf6vWeIPb96uEH/d4xcziwcj3udsHvziwzL9KQ1J0EFLeb6v69HhNdrTf++x/qmt32qfu9eGfRxbEh5R4KM676H82KNORgamSvkLCj2hpI3DV2wuco6VkzQfsMA5t94BKukPhRA3G/cSs+/5ATBO81ZPBumjGnuiXh8nGI5DKbQqfI4nO5bQVVoKLfXez5T7+BYUWyXFuHSt2hSn9skrTphIDyn4z+54IPh4LvfP+9G7HNwVWYbRHj1IWGdSDIl2T3MU4ieTjY5XEmF+zSO9//x0DSRr+6hvGWBkKJ40Lw8iYLEwtItQqhhHJYxGTkgrtxFHOGGR2CRwQn/hsMgrwsfKz0urc7Ku0xzWALJl2HtSrfofNBx/2opWEJkZkx0+1LdknaMjCsORMlmli3JPS4KrCtqHkPkD36qb/u5VaNxv1QVw2Nx2q4IdZn7EU8eWUwAspYknSE9Q4M77hoGTTMwEvqYT6LZ0veUpgbag+qV6tf9ZI2WrQxJuCZd25TdpQKGOpoY6HA8GS8tKBPcTcjOtB3vBEYeuWrkAPb5M88EBKsxK4gdLRcibP7ZD7gzp/urgWOpc2bxB72WYm5yFIocqrZz1O269cBB2DzTs7Ier2pJA8G6Wp1a+G+n5sDn/s8oFn+ZCiRLRUmeqesNg5rLGTgWgXH8quVV/RT44n9A8eam7REix6p8IabCB2Y1J2vV4wmpKsS6Z2P+z9Cr7XvHCazDtla5m6YeV+yVrns5rT95lcIh935g7QhvoC7Q5/TXwgC0ums2FPBzmuMeOR/Xt7xr5yhJk43YbHkx5P7svD7wL982XznhZZWS/r/SuB2HIMMgSGaphZHrjb+j3VP5NXpKWhy6Sq2vIN917BONoUrhQoSH3863klstSFQQl8Yh09KFhsZ8NhhFnvrkBJGzwHo9k3gkT/3GDhNh/O5RpZRMNYBHUyOz+7cae/3nm+1P5eLqZ8w57LkHG/51CBCmN0QZysG6SZUeOuEHLuOfn3mKsJd/DQBSfqxaIoLSNizGB8wKIcpHm1kOmHXS00SDO+WgoBt1/GdMvhEQO/r9CdEvZY7z0IejDagjogMgjza8+JLGQJZf6he0tpCwA7D9+gadueSGrv1/1uBAl5Ipd0Opge8sy1mwXNFdkrnO2SMoJsBzmQCF+HjKywbASAW46dYT9LG+7tgwnzgs+XnOQPgmsdPJcsK20BEu+GuTjfPLSr7cr+cVY46Uzf8zbQ6LH33rC6wDzOAEK+0VpdFFXMZYmolEtH9ZCWwjxMeoxRr++zGj/GictJJdHOv9oWjLySd7F1bL1lCZX8Z0lhe2d60tc5ISs09F71/yWIWVscelE+eGYYCW/lEEZxI/dHudtnt7gPdjK13Z6LfEZUnuUlTWE1Ck8lxI1PecsORonj92sPFa3BigPvOaN499btV33aqfZ5K4hVPuQ1nsGigWWiYlcMpUpN6qXstyU+b8NyLnUKJ7yPZg4Xh38UmBf39Xdc8mLCLqUPGv+utMc6ERguXt4A71WE0DYbNGjFhMM3RyQ+6waW5aKPXu7VWe1zSka63u64iT5Jw01eKKq/PFTbs917FF2lIcKNgaPFmVFMpn6x7aaYhwhzwchNJDluPyS2nTV8WAxNHGjlcQuCcktfD4CBjAylNIgbAfjHmjBQtMojJy2YplSC+cCOSvkYRiHoXQj+iQzFTLvStSardmIJYMrSOimlqZFE+xv2rAFounofSdgychY+MYyE7W3nXj02B6MKmVkgrpNQG061dsvQoPLpPjnZaLJqapLVUVevA+Em92sMWm6aKCDSOOJa8k/duMMkDeXRH0Il6ieDhK6WifdPOpS8Kxf3+rtcwRurGacGnmK/dABvQ/eTYgSAWC1QxCR4slRoZ+Bkc7NPzkxcM4Ayepjc7f4cgIWpmFmYf0q/sDN/aeYfF/m6Mnqp/1SJsp+0SZZ2NuLsZvxj4FwRh+Up+6OeR/yNX8sYF+2LI80PURxpe2eLpixbzd6Y35OUvztra0wuhBlz7znWAEGMDGdE5FvBNGur22JAzhuXpUlXmiqXeeEaMsBIaR8jFjyPjbCD5WagFrwvimoyRHe0/asjR4clAHn5gr12NRklDnT69Ea2IdSqVJ6n00mglwlkpYrpdV2vrpmqKErjJCh0MLaeotCJBnf06unHVDGIlByMhyieXv30LaYF6psCpS2PvtMuugnMQx5SGhmXO33d8PhB6lVd8aFgThzlVX4WuhJbHfe3R0FnCNJLM9rUG2YIjoGajy1mkfMFZWNLgY0t8HQOBbrilaydMG6bs/baZBjMbwhcub0GXJR8PgUuA/m4akEqqi8SJaufo6y9U4RO35kghYwjBoJzoEA9Qn4iM2j3QL+/DqFMUcfB1DXemwtVjFjjcofK7NsebdH07mBNok4DQagASb3ay03bFA1keXs2FVnWuhL5GgmObTWDPHuG/Pjn0gagyGkfbdq8sSiu5cFQLWZSm4jKFR5vnntfntDNhCySrIxYD20s2BKHM2ZRxrkQ03NkWWABAVxtH9O5W4PcUUWnpedxERbd/uI8U4VWC9ypJn3kzWDptVweRxyrnmjN9wJ/GPr1I3/zXSYcYkpYrMwgdiuYBvM3RnvTmT9y6Mf8IWgWxCZFKeobZa8b+8e5u4/IMcqWXfK3xuIJMPL4FpqkmuhgYX34mLaLvOrItumrOVOehU9sJHqx5/BriA05Rvptt0XldKxPkqOf18Se2VGIdJjpeNWvrTnboAOAXpkZhnYE4JyBkNi/uLdfoxxMJY/gLSVKTdhREu0uE/va+mKhjd786oalL1mM9ewoAelrCQMkQZzxeBFXA4WFoKNSCXArkvDGK5Xd1npj1bBRlfdO+ChZSbPrVkqUlV5XDRcdN/f2MkMVJqskcLL2BCBxnkUayJv6uWhF8VVqXQh1DCTZPt6CRtF5ZWzLLHYABXo7Ce9MXPY39VNP9BbEA/KCzQhR3hQNIhUsSiw7e/EeQPxllQMwX+TOhd3p6IbmRzg8EJRNi50pOfT8FTTwAl/ztNjeEFYNCzP6UadsIe/eVu153Tx598LQh2h+KiOuJ4iX6r/6qWKe/XOWwMrn+7mdZDGI1edWuY5yYXWNSLcgkJuWUWPB2sO1nrT0FGkg/SfW3YvObRlqDS71s4Cpj5JthFF1Uyogk5BdzjgYloqBLYp+iZGy5WbhgoZjdYfh0juQvQkPJOWFM3q+QwVL+gJt/13kzmjhAttwWnwpV8S2sB3jwQkGFcS/49INUyB+gpPI/fz3Msn1z0begilyqjrVpGRhS80KWkrO6zindhhSPTDWgMj43GglYe/COIE3V6Zp30OuyH4RpP3c2dbvAG8Y2VQ54bS9ozwmAd9l4dzXqA3YEY7ZR8lM9a7xgKmyip8lREsHA73Ma8Ujv1MBgiCGzKcpkTXZIPGSb7XWXCYSeRkCT6isksU9Z6KRkT480W1u+08IyuEXwN7rXzeGT0L0G9LueD2tKXW74Ynp2hvryOOXG8EO1up4hWuNAzYOdZxKz4WOY/0/LyWAubfmhT4SGt8pBzzeY3jDekH7tY6YYpXeyq/tuUYGTZbiN9P6R/YXRL6nXNK1RgPXUnxfNofMIhpDsFReK20SAdC8maDmrf055ZNMGqsG0e6Cxq4GBja3Xr4wqj4a/RiZRekumr1Dts03xh+cH2nFAs/1/Y8pFXXvyuXbHxU/lMHVNtj3sEyzVUtH6RZ5VndzhcXvPlAw8kw/xKJdXxvsprw0z0+5LhG7QVB8yQGSEEy5wlL0e3e+eXBZIaEuTUFmdfsQt0xlzSf/e0tdklHaQO/cymp93X0/gMxG44x6xA6Db00vqP7g5iXDWHsSpc2MsKk6u8/9850dnZ4Ql38uyenQphbAvtFPjhuFkuSFxv9khWlGT+TGTTs4e3kOyFrY4EycGwbod8vSOHbEl/nKOOM+s5H7JkISNl5uCqK8HIOtEAAe7gEWaDJMUobnmTXNpfeAbt7/nrA1xeBp5JXGcmyHtDY+CBA+Q8Gy5DF1hKrAbl70k8/AlYc9EWERNzfSt5wgFfVU7PGkZ+RaLduS4isY14byZNG1vuOpxOhJwy+n1Kzcx8DnsdemyulIO3eDyq0WPWfHN1aDnLeb1VbtO0U83v0tgN6d5GN8IvYOw5/AqrfILe4a7ctSaPTheS+OsHfRp4X1Nf7bdJkalShUo7ZGXtCU/b/Z5EfQyn3y4j6wiUxbXWmxiA1SjshETePU8dLWoHI1yuP/SonJXGR5HWMIudFQUwcYRtXxD2T0odDnQgFmUQWwdAEBStpI8Ce2KQexaRvl2l6wUCGSHLxX8xEaKUeJtMik+NjBIYWczu/ASyhZ2KA9w855C6HOzNP89fQNUCER45gr4EI18fFkq0rjEsGRpIosjNXGRW+G+ulJ+jzklD9RmrUd9YFhxiRkZSIFG/czuXZmzNIBawpYgwdyBV3QVwHiAxsw8fqprfvyqlEJN3eOWA97UPeD47Wrx4Y8rj4P6LZNONySld1CK2gci3cEwFbEKfj1GhmPYA3oa0hDWVhBvSEirtfE4OKbMY9tbi4FxVBUwtD91Svi/7QNnEnUT0/AhUCXVVC6SUuWrptFZioKjAsLDVhfzkIlS8Y6tORgDg94W/qYXixqVO6zb5LCGwTQ/QZws/VeJ0bw+cy15344sEN/z1UsrBe2N2Lm5MVAaBJOaDyQF68Yh3JEA8BnRxhLxbRt9jsEDvFbKw16G7xpg/og5eWQH2wiejcyh+DawB7XNK5Eha06ExCeiYNAZwXz1BXq5j14qNZkH/TI3ZiwXMGEoml4SZT3C56zEweiB7MlSgjNKjVRR5vohfUBGylLUABIs5eY9qvslNSG1hlcomVHO2OrpTFXz2dyRJZMz9Y/ZLa/cLXYHLEDyk00ZNPfj/Uw7ssahosvuygj2rhe+vCY+Lb/cpuS/BjEo+k3r/GDC87/2MitUGc5+uW5sNaK7pxfwolWHtakx7qYYTqYbo3/qJ6pXGLuAxOb1HB/c9pV2IVfQc/pfcIgFSlzxVrxD4WHXK+WxkAkcobxTHPYUM/2Ah3xZNYHGXBby0ptOBVFiyw+aZaKEXruAgz/tzIpC0V5is3NpCfTqCyj7UN9gtvwX5ecEjJ+Jz1+RJ66W1B3JECqpPKifR7AhB8HozmhNzByhOHRiaHejssM9LjOCGcW95ci70bLT4f+YmVSz/UHvMtK28lid5mZ+jeDrgofGBxYas9zsrQ9izCXaTf57XZv/uyhhWHxruDnc31ahsrGUimkHnslrcoyfU2+0bFaAXPMb27MTHftwe0bjerKXRv0KGCyCMrlb9cxOlAbT1l3Z7I7yIcp723tCASIIvElx4DUjmW5hB3ncsxl33tMNyxnQCISiGwjMpZua94UkCMoGxhZACzi2bDhPofY9z0jTcQdPgLMZRLf1SMGULeYeqXtrBgZ+Xgh0zXUHCt44c2fIp4PBMhoTIQsWTAibyFuE7/1Ilrz7ykGaJyBLt0JMg3Y9LayI8BqkQQr7feoEeKK9tkHnlJj+Y/hNXknwDWT3/kg/q0s5OnXVXbK8WM7cx4tuLcx15bwM+FZnhBsULPwWIsr+77wmfZ5QPZx8G5Cy3SOjl3d8Lffux2hcZn1FxfsEfqUnfQrOT6n/c6+0EVT8Iqa7yvFTjitq7ZlQ8B55o2/7KRgKT9RFCW7CNp5dz8MEaAsEzYh/sbU7HlkSXfI6ZgLA1SmK05mqCrSY34bbcSy6aIscCgLSnR6sPCjaCbTPHqLOfT+isBsSXFTY6sZKkLvKjX36oQ7E5RrzBf207/Laopf94p8dMc4di04Zl6GRmcWISyPLUx7TMG6TPgiWvrj8dlDl3F9M8Kb363kySqcICITTMQ16+D0ngwur4xzMTZFiSaSvZ998GdrOtWu28ghLOrHX+23aZef3BPWg43yGKOy+EEE1GJMO39FJN7S6eLztCc9uVvILIOfBOnJatl6dwDJ6ov31393MZdIhnCfJe1OlnLEZv6EBVinf/clxTpjJpsf7xJBhYR+pzFYvps0gY9GUYGT9eGz//jUST5JMYoVwTl1bKGfeB3WuOFi9HbMJl+uhGbzg08aJLn+b0JX2L/S16z5Yf5Bl/MRNgGnyJGQk+uB4F32bij48RwF1kCS7N1Gf0laW2kz4IsRfMeQU+DhVR3PAJS/SP+lfKZHuy8Q/HNOFVFRlfluRn1yaYsbq8Ar3JK5vXFSA8zx95t0WCRz8VskQV0n29kG0o/a5cZGJCSsDjLYOBEbRD62F+D2Ev84pQnKc4qAwDvLnTRNWVoeb74EERTMi6DdKT2J+Q8gSLaw0ZWlb14rVO0Qsiy4IpE0NLl0xqLGaAdKgpkZTSE4U/soGPLFGbikgyYJerOZlSXOOn/E1D2tkzLfLB0bf6AveJfN0smKHoFM8Qmk86zM2uesq66QKqC9Oj8BpL5+GVK1pNngGGzOPziFWCeY3Fg6YTpTSz4vO/mm0vuLcnNhgvktQWMrYXGVlg7Z2ZFXcRCkWjNwwMOPd3qWZ01gdHOkzXwO7Mob8r/vfs4L99frM73Pm92GXVRRNyh0j1dU1Ve6IXRmHUPwbVzTI0ANs7skKGfRaJRUR5VW3BV2kQSk8v0vp1zphNzyvaygJqtTpsQmU1XZkMJCl2MAG47xM5+WQpzIQPjVeGPg+r8QSnPFcyPu/doQNNZKr8F6cZFhWHrH+rAM2b9xXIa+Cbe8u2ra80uKtxeocDEgiEcd8/zHGEs1TqiyZnpM+EiR9AYSSJlHo1aYN+yU7ssi0AnqX9q/LroF37NgXXTs8IaR9XeuginDC5qQWXwvQMRsF87sarHgiprik3+qQh5hLYEmFKSvU5qzqyRRYeplm5bkgg23tWuHcLp3UY9dySM3vcZHLsihn0Iw/WJILGZp0j5IDD02uOq3dLN5L2cP8Y0BqJPMs2g3rwaONUW6UMhLQsZjjNZyQOlhFtf240CNDN0sN0W45WaYPDlPC1/wbb5+XIfNIHysDekwu8Ozy/lTy685SP+JV42oGgqrTilHbIQQRzwG05E/7m9YMCHSCPbFL6HbvSHuMewJyzqOvNH7uA79Ye/wUvFZAyI5TNmbqjIcpVnidL81RcO8WlCQrCL6hhpAdx3NB10fKNuekCqMQ/B1b494yDhMXljszxkjVZ1MO921cGBG4wOz57FXDBJ7SGVPJOTeV4QIxaJe18nmj4owD3Kjkd31ekCj0kP92PSUNqLaKhuW80cCi0py8KNF/uixG2h6SnGmoX52bDCM6oHxZqlwR/VBM65vmrEV/+Tfh24QnVhwXmdIWork3Y390M2MTYx3QtXYvlDrbuJYdJHOLYDlSKbaMeI4wPcEHdpUu1UOl9FiJsRC/SjpOFPndo+bEi5xUOXWnSofRd3qc9lgmz+Nd1CUyd2HpXQkbxK+g186vLKi4C1ovaFzSGBWrYP66YvrgdWfjRufWWr97l1MSskZXu99ocF+4ztlxc/tMpmkQZItY5viaoV0yTGWbyc/2CRmbJsTlXuEYQVJfte41OCQp2Su47k49tYelC+zxcDBVtnKyopa8TAOVB2p6sZFW82NNzF4Os8/F/oI5KqIkqFnqibUIAHieF38/Z6nKQfNXDVIo5/LAaBU8oIULQZ7tRpuhOMcQPJ3uv9noDt0MxMahcDI/oiyMgPdiL2qLNq0KaQgycnyCIL8RJ1NczFFW4PmOdoBNdZnPAPraVamNaUcdrgaFgipoA1pNEJa8Ov8lm1PZuqQye5Ir7GM6uReqphdlJHrOyPwffyyVkmSanr/gqLGbEtkXR+CxFJ/uK50SrJldbCmcTayl+H2B/wIBgphHvy35TUI8HQirU1G3wVFPuLflIT1NaAqL+JFrZczAwoAsStf4HV06ba6js93o21MoGLokUV594DqaXtTU1CH0hMl5KSHZalwbS1W4VfryRmY57Gj8wp+s7SoPwueU9eEbr78+z9yjbpC/X4Y9DvvriLak51ecAjBZFYx9ujDIyzAvgFfH2kBWndkWnkip4BhSoH/8xyq/oxPZ6NJFGgnRUjDKKeTN6Anq9uQr/TQq4+tO7xoL6Hlgkr1l/LsW4SHNrzLCtQA3iK75ZZEsltO5rxkhVfoHmI0Wzapy0du5oaqh1BAlF0s4Q7GtpZLEoV/CLYX/+d/0DzDWI7bl0LOqulujG6xbBJmbwLW/nuP3flQK5Xyx7ZIez0YSbUJCrrwTaYPHgA1ubg72v7vTVxBalcQoZHPppINnIGoo2GFUk2XQge+pX2gJxrF1Emtqt99JN4oOSqyj2iAz7dm0AAiVSTgQGq7u5/FU1V2EViRcc658ww62q2SbCkfZsgYv3wD0rOs709NJjslyiVc79kuvIZmvCDyLwLgMRcByLj1wrxrqgvhlqerJVraTPHh2w2WRlcqwgrrsTa0TTlxeG1/PscUSIdz3YXnrLMVQC9JCDoDvLqJHenHfZXSdRBopH53hRkUk4KPeUA+8Sl+96COXknJbBP9EiSCNMg5BQSCd23N0elm2aE/M+1JMwvOyogPxsrHgNXgosKW8z4dAaIR4W0LwCxLcUNf6shwTfowva/QjJurCxt7Vegk+EGunXz6ddi65xguyO82jRCsmCVuAL7l84ebiFxIBEkGIUIpfkpks2tTNxklu1PeS5wz1XWGdrq6/Utu4MXfvh89ChYEV4bExm1PB2ShiWLrBrdfwCllL7tvWXnmb6gmcUulzKPs5P5gpe5u3sidnTDNH8aTTOskbCJyYrZD6J7pPBaa7ZrNrczdsfb/ZKjrjc9up0cFb6dqEu6Rajj15FXSU5HXhCdf/mfogoh+AtI47ukRspWWI7QL4pAuzmhv5jh6oxLaHznjwtRZNO9f2SS4+LVrvdkV4Px7z3WwLMpk/1RlDsPbQGKPwSvq81MO6xkqAJbsr8/I2YnIch6dLvCBIf3ttO+EpaTofd9Z4Yj9vpqciqymLPULfbJjJXPLT8AcYQTPx6rHk/b/I8byG9E8M00iRpLGATQlh8xagYdNazzzIsrp89SMHLu0HiSt+kHztVdP8dZHB8qtuPx/4c9rsRJBH3iia4GzUzB6UYez6r4o/W/r/fgwFmL2atBOGwjsLk+Ny9RzLdq04R520eowExTg5YRbLgTNOC3fDSehXu2Kg794scrVTIJXZVsS3PQvqEZfvGdkjaEQRB07yKxtiFoJRPLRMJUV1BpTI94Y721wjdyjB/4vlWO/x4RRHhP9s4puXT4zOrA99kfTXHd5L31meFeT2COZiW6ox6dgGlbqb1QUG/dlkSBkdkiKYJvmxfNrX3SmXi0xxV4dH5DIykA7+IK548H/6tyRK/TEqcTS1f/h4+5cf56ciuxn3Mt45IzH5hyoj9GYQ2LqS/j8q4uzXRYjeWgrEIGvLBkwIY2qFBbL8opPrynIYWVn0A3wRJWv1LY6RzFgYxrVCLBHo3l/ziVhCj56+uSy/Z7ipClIMHk0ZT25w2b1CtbqXqxOIv/PNKW0iChNUjqjUjRyGoWRpKvbt7tAJC+cUJWza5q9dkOleND04yCqQ5VL5B09nc2qlaux+TbP1C/RZmcQKbxeEmqLTCZDB0h0X6QDbyOdTu/Z3rZcXX0h0MJp3JTvRmR3xyC2WDIQnRsDopN0RynyUXf/la6uIo6qmRrz4nCfGW18vdItBBj2A2Ku2gGHNPutFBIsxNOfbGGcp4QVuBSasL4Hu2BDeTPEAjmV1p85HYq87qX18COip1qFnQD4swB8UodeKWqRl9lQ/wWTqeU9ve+th6+dTPgf+z5E6VBwZZgNd0oxz9HZMBYboWchRxQcMI4xmg4o08+IJyNP9HqadN10w2omfEWKJKwfmFODuYmy1E+LkUpe88Q5A8byaSJEzkAIr7HB3i58ct5czlk7unIf+Kyc+QNkecM6czMJUOCNQKViLJ76o8sGEZygBRB1/blYlfQVr/+SljuqNWzADzQV4UbTXRdJQp4Eoyf9uiGNhRqmhFut83Gd7wizYKVo0xoJJGKMoiI9+spT2mHrJ1ALB5pu/Gw4gPQd7qdYBNid2HCd4IcG49h1rBf/CS8VqJuqFdef3lKTYEfdotx2BEjss1Bo0ys6Tp8Jei6YKnLFtZ2Od567YE4RaBBzlyK2wzTxLUwS5xpOWwuBX9oUFkn2uQjXddnudRJADBYwcyPzcuRWEUs9sMc2ytfcKQezjTrTyfOTywU6EG0LYOyDA6iIGSobbCzL9Rp+7JGD19ISYqiYQvYetl2EP25N3U/tKW0V/S7ITo1rMqE5fQx639PN75VqDGlcSkOqEV+HLXDkM2gVzIyNu/NAll4oyX14VDXIUIPJGsKqK1CD1RblNUIbRDx+otGr3nZtkCPy61F6LHx/d8zQPMByQOBZvYKBl36jDycP5ksePhQFtA8X61eO2W6k47iYlXgL2GAwYMbC/R+a4ejvFv2C4oJRV8h/+vU0ZRdTddzlz4BUlJG1su4/Bpw7L9r4INTADKEvajl4ZUAxQOItN9stej5zZUy/QSIewW1O58M3ZKhiZLtV6jYZdYYMFftFSqR3FkEzAW7Ryz2szmjCsm6YCbHYOqCnA/U3RHKHMCYSAofkr50ehROA1b/xGAWZVf4MQZgsgNzgPCnkXBIBDIlO8+Vdwa7tZIIQTaKR2zarHfBoRHU2FqEyj/LM2k7CHqNLJ2e0DcPr7ntKRzHXzDNBm+GjAdINf4jZDK+j22mmQOyL0NzlLLhj6fD0bhzul9hoCyqCYQsfps5mJ5Oe3ggKjKV7w8Tuo/M1FlW13TBmjQiCqYwuFXtz9b1d6ZUuxhdskDvsygvOiuDHeaktlUttHMZjtcKBjoWTNh3zfqF8XaI2UQ6EeMrieqfdOgC21L7U0z66XupzeatMC2Eq8OXL+85ddoj5CUvaydCjJ4OPav2kyqfgSLDrAoG0Z0heDm6V8bswOFqLQ9UjOtKc8DqKaICPXucaq8zBS+Y7FCRuL0wJCOVMWgdEAAck90OMug7BiUlqdoVtvXUYmy4FtXVXL4LZFn5F289AWNb+MHeeBj74pRrSR6vGcgDS+JAN4JsMGJHX+oZspinqMWnHVNFuqLWj0Jd8MsGf9RvKDCMYeon7D8evjn7s0+7mcfg/pgTajd/ZU6aCYkVPG9KwDflZ8n6bp7R9Ar1bfo6Vyi/oVWVfol08RJse5Vv2sW/axs2aIqp+UtG2H+hZUtp8ciZMu1LsikWvKBmLXjcX4XFB7BKrCjxbWO0r8KH/9ZfDfeMSsPSkvmoEEHnD4e0mvZNJul2AQMp8o6PwJQBL1AGSr6PYIkfozpMEOWrTYeXweFGSHqx5DLLiLf3vMpaLfZdByUyK7BH8OWZqZ27HbPWpPp7Di3xCsjJpxmqaFXUml8oOcaVy5KeDqVlUPYwEfhZdZHg9AlpwuZL4gxlq17vZuosxOcDFZ7sAdxfZo0IXziMDx+L83Hzl36BQouJ8J/we4rWc14rIT9EWaqkBgZXHFMoSvQfB2i6gcHqqQdnLYnlQJN+7Hb4n8C+cwvVHWFnDYm6RtTTQalN/TvBJtHXljaqjcqQAi3E64cLv1EXcRVTVo8wMZEMcf4ZbzfqZrvkRkQOLJMN9Rcfe8pG2pMu69DV6rOOqbLAWBWrfcZd6WshTxc2r87wRLDRVz3dte4a1RUSngkYlRzowoxiUZoSntDslnAHoSHOzx7Uo08Xg78a02zlURcRRUIHAylOhZaBiW4HNWgrERpMHe5nvaCSf5vHiLFhsmQyN4Zz/yekKUzpWkssMPi5jTiD08I++Q+04/OhlO08TTq3FU3geWtUVtEKCqiDBlf5Gxkx2d4yTXbveAruE72L46E6e9rt7v7QyjzoEqbTlSA67pLJpY2Glu7DaTZx9JTI+lGCAHnaKoPOT7ACaOn8X8DDPJLWgro3aTcnXlid76HBvQILpvWRSE5/Qv7Ojjv/YASBHP9edDZ7zDhHtyiLV79HHxN3qVXKDlPovuGa/Guz833wKbqler9t7FGB77zkIcdouTNfnQncaZBQYi1H+Oo8WiECbeefo5kOA2OluG/jFNwhW21yC+h/uuIAJXsaINuqAEIAkHti+pPbIGnABLi8BRWctOPBMr0dKVHhdOqHf/DY7m7ouFR62AlB09i10Q2wF/zxqH1ZJA8RcjyAu7brJTpNSylR8XVj8XUXiagFlijwXnun0r2stzR1BFcs0lBfBO4gpbHXTpgtM3Nblzs1UwM+Jf8QiUC+SULtn06pHEgCy7/dEOxPv/5TtOAwUf/+XW8/13/mxU+dAxGWqNAT3weNRR8lPo5l0FDr9TbLIlmrFgAuelVCRrgbm0RNaCqnn1VUc9mUejZsmIXkmUMghLIvE2kDsvpGEBczoRiyE3dMOLUsI8V4x+cTn+WLhmW0Nh0cKvyKhCBhEmOgb4NN2e6Z2JveYErX/hyHHpYXLjpqBiefTbP4yukG+3fYD8wB8ZqiPxDqvVCPM9tUiGhU5GSCfMEo0P0xkudAy5F3C9OV2q0DsjXK9IaH0Tm1qUxNGUDj6dUrSA1+InYdXEzizgk436lzgwJWilcziP8YuP02ftl6TyVtHeVu9ahXT83RsJ17GTTjvXOErJ6QK6QR/YkINPF8GHTK1sV72nOXgdhXPzZ5jaTx6H9pFW4/ZEwGgvyCLCDpP9y3FSj7FuYdLnrZcrQU+vxm+bQ2HEZwomlsoAVHCf8QaVDq6XIO9lebVvjg+ZyZtaTOfHB21g/o9e3gUWFHiRV/7vN95I1v2OHq+Lk2su+6W5tHKizQDJWwiCqM4E5wQhmGDwOvKRuFvhHo2oFwyZO1eRC5dIOopNV3ubp6ffzV6TLeIfb0H9qlkbL1iiDXVwutR+AC3WtJs5AXNfeJj8XuNZCwMqxxECPJuUDT6F99QSmFUTzWZPGzYmUct5C+yfPBhz438+dJbEi2tTV+p+WER11SScF6ek+GYK6dm+Y+Ab/n262LLv+AJke3/4YVD2qrXmqde6I1li/v9LRTNHktDzonZLzTEFEzpY/IhFunWrS0vEFOU4EahbnIWQjqSv4VgfcynznHlVKnWeeh8cbrO+b3Hep+jnd7PvRz7DMi+HyhzWLAHG6zfoJZnx96Sz0WncyrZQeZ7DryYucVSVyMwf55vY/Qt7c7jKYNzBuUMExiNq++gL9vAN2k+Z8aBMPXb6Cv6K8zVcHCagr1wItPdG20FMWq5zOCOMyX1E/0vL94WDEIzt4xwQuIaQ/AOhyfF5n69j5n51B46ZzSEAJT5OCkwiMs5szMHjXRolr4/0FTw2nkFc3fXO3ZhXyH4huvI0OpJ6RUfGZqSBuDX7ujEqclZn3pFBrOS1Hx7F/L86aeqKok99605KTZsgmvL268Vza9y6VnIOBwzrVej37Dp7ZGQ4ygShyouSBz0b9Ki7d1knwPCtMRrRIsKZR9fB9Y4BFA+VaIBRfOWUQGaciHoWRGDp+GtWTbk/93m8QVz7HEyGdLcRwWdnsqpasGd6lkK4DPPe+3AL755Rdxo8vJ6i9WlEgvr0pDs1aQ90Rxc7BY+OyoGkE1E6KRV409oko/DetDhZ+qJ9b9bvJ7oAmDS4VxekVUe8w0th0tVwci3S073R7zdSQ3anJZnKK/SufcbTMvDJSQf0xpCc2fte40yAsT9XxwBqrkX0IBed1bfso8ohgHWB5+rfzo+GfrA26afTzSD835PWAWjlR8N07xGUW13v0y+BIun6TKNFLvVmYnHZVMs6M0AGh6MF5oj0f4P2+qhGnK+QKcQwxu46fDzKERkisCB9BEadQv3jBHOYmU+nal7CF7V8EH/Ono3guHfuWFJdJsvVB2h/GYf5J5hebSycYmHaS52Trk1LVZJwBBHZqgLMp/HBdxkZupzk2kyncEJnBC7uswfBb2To9etdIyzvePbRQvZnLzah0epJ4JsgpgnHy0LjxPJrJDWrswcwBIGb5dzghtTNmMnABujbM62pUcGfEaofFjqH6x3Z4N0D1kddTWTCYmFu1Ejk8sYVwGNhtIMbWtmLvP0+RR3XDJDQkbzK4NGQ5+0vuRXmsgLtoDkg6Y7rwo96zSx0Rkr6fgDQf0mTQC5cMUx52AgEt7J7zRU0yoUgzg4PUE5kZ/M3iLf1SDpEB2aw1fn70ap/t2gxhXXLDhTZaFUr+LSMXd6HC2fJg2p20iqIH5kEOwoTglTRDYQl4LzokTOFRftG0moK/Xlh1Qt7h/UNWcPkW7sLrSqR4JWC5d734CmnB0v+9Zh6t3KX5VvR8c8PBL0kvDjFoJ+DrWKQruln57zY9AIWLhSJkHQsvC6JU8MEFl3UX9JkXHRyrZTtQimkzapGjJtfYPl9hFrm39tiqklt3o11jcJVsLiB/g3WTIPnb94S/iLHYCaIrKryVIdLAPzZyAa738jR0LS36hEhCfl2q/uZ/4cAPE73vNM9TjEGue8pT983k7uFlUc+Mymm+FXxMReM7CYyBQq9xrc7d9Y7Z8oIQc5IThBr3pf08UkOV2CkZvWHGVsfeQhef2lepet1JodCy4PvF/x4GUhPUNGK2+F6z5UCnkXIqFIg6aUW4R/790qLm97dLxiusMF1PaQr/0495Mw6B0+21ms263RqkHpnrEMam8G4DGLuC7ptGu8uXXEFC54s4bMtbQsI8wCxyKumz8PijekPnxLrz0YWI9kyRfzL8zKSOtYPwJNgVdJy60YOm4LlITtdyyrsnbrY8U4cLwarCqs/r3BeaHKPn5u7wBVU7/YPyZ+xeHh6rmqBHnzxXGsH5llLSCloBmucwirvfIvBu/a/+MXNx/ggADM/z/VxzGWDnlmtbV8kPIPK3a/otujaBHSrG2t+Ah8xbuOWodmoblx84IdPYvBmlrTUQnwLEM+a/czCHR4/hoSVkvCGgQilRg1IrrxuZM/cwkG9B6U+MFkT75b2Ao9bHrxuIa622CW5qABUUAFJ303Bs2Qnj1WnK2KANCCs5dMxHWOpiom3ypxAyP7r3wgB6tWCdMyCzvzOtbvbnLAkkl39tot3K1tu0AgwxubySeazRM0yGphH3tMX2g4yVOo1tpwrCk+i7FSruuOfsexv9qyGB0XbwDJ3R2lbrhQ7sfAD+nrUhjIrYgFVKo7t0GZZUOIee8sMkbgKdVDcF71620fcg9lvS0tm+kP60kMve+wKKjx/VV/RIbwmy6LwUugw7vhu60EoRc9wrPrh/6rgL2mOHgrBdW9XWKt0pvhV6aNzoinJz6zGBad/VJbVF6HDE1s2e1N9/mx4iaH0ksKurEuyBHd1U/YBVpAYGlQ85Jb7m9jFa7mNJwgzCRVRKt0okacXTZ0xWnnjlBDPi3uQDkdBArvBevgkDGVDuXVwj9k+5GdKzvL38NgOZ5+Y6qd4HTQs00IBW7HXQd8oN6RUdi/YFsDy6fwYQytuQIWhzmppdBLnffKIMRxk9r02QeiyI6auvjb41WRGcE+DjIp+CdEwM83ZDTWJa/ICNRfte0A0d+YJ5XEIUeikHYE42K/BHgm8wpb/4/Mp+h2T1TVHxaKRMIc+iiL26E+kCoL0oxeokiqWQZD958nBrfeeQPxZ1RrF6NAtAd4ssMSSiiD5qBO/1kjSM+e0uaFsTCPeFMvgvvFaL80WekDkgnX2sy8sYgFkeaOTkOP2ky7EOwtEkT7aEXCXB+Wv4XIETkHpd4nXSlueJ0=">
    </div>
    
    <script type="text/javascript">
    //<![CDATA[
    var theForm = document.forms['form1'];
    if (!theForm) {
        theForm = document.form1;
    }
    function __doPostBack(eventTarget, eventArgument) {
        if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
            theForm.__EVENTTARGET.value = eventTarget;
            theForm.__EVENTARGUMENT.value = eventArgument;
            theForm.submit();
        }
    }
    //]]>
    </script>
    
    
    <div class="aspNetHidden">
    
        <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="6BCE89D6">
        <input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="I+o6i7bs9t0wSDV5SH2KgYFWdBZcs486MZS6a6hDHk/kjBpcPgxI4iUfZqCJMuB78QJXEApTrTI2EProRYh3CvTXVpwp4PDjZHTNMktzxTA1ZjJCzmSqQTJecltU0I5ITOVFEL9OI/NFiV0p3nvJS7D3kF57E7X95xAIO4x6hQs=">
    </div>
            <div class="app-container app-theme-white body-tabs-shadow fixed-header fixed-sidebar closed-sidebar closed-sidebar-mobile">
                <div class="app-header header-shadow d-print-none ">
                    <div class="app-header__logo">
                        <div class="logo-src"></div>
                        <div class="header__pane ml-auto">
                            <div>
                                <button type="button" class="hamburger close-sidebar-btn hamburger--elastic" data-class="closed-sidebar">
                                    <span class="hamburger-box">
                                        <span class="hamburger-inner"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="app-header__mobile-menu">
                        <div>
                            <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                                <span class="hamburger-box">
                                    <span class="hamburger-inner"></span>
                                </span>
                            </button>
                        </div>
                    </div>
                    <div class="app-header__menu">
                        <span>
                            <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                                <span class="btn-icon-wrapper">
                                    <i class="fa fa-ellipsis-v fa-w-6"></i>
                                </span>
                            </button>
                        </span>
                    </div>
                    <div class="app-header__content">
                        <div class="app-header-left text-center timeline-title">
                            <h4>Student Portal - SIS</h4>
    
                        </div>
                        <div class="app-header-right">
                            <div class="header-dots">
                            </div>
    
    
                            
    
        <div class="widget-content-right ml-3 header-user-info ">
            <h5><span id="ContentPlaceHolderperson_LabelPageInactive" style="color:Red;"></span></h5>
        </div>
        <div class="header-btn-lg pr-0">
            <div class="widget-content p-0">
                <div class="widget-content-wrapper">
                    <div class="widget-content-left">
                        <div class="btn-group">
                            <a data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="p-0 btn">Hello,
                                <span>GUC\mohamed.elsaadi</span>
                                </a><a id="ContentPlaceHolderperson_LinkButtonactor" href="javascript:__doPostBack('ctl00$ctl00$ContentPlaceHolderperson$LinkButtonactor','')"></a>
                            
                        </div>
                    </div>
                    <div class="widget-content-left  ml-3 header-user-info">
                        <div class="widget-heading">
                            <img width="42" class="rounded-circle" src="/CSS/architectui-html-pro-1.4.0/assets/images/images.png" alt="">
                        </div>
                        <div class="widget-subheading">
                        </div>
                    </div>
                    <div class="widget-content-right header-user-info ml-3">
                    </div>
                </div>
            </div>
        </div>
    
    
    
    
                            <div class="header-btn-lg">
                            </div>
                        </div>
                    </div>
                </div>
    
    
                <div class="app-main">
                    <div class="app-sidebar sidebar-shadow">
                        <div class="app-header__logo">
                            <div class="logo-src">
                            </div>
                            <div class="header__pane ml-auto">
                                <div>
                                    <button type="button" class="hamburger close-sidebar-btn hamburger--elastic" data-class="closed-sidebar">
                                        <span class="hamburger-box">
                                            <span class="hamburger-inner"></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="app-header__mobile-menu">
                            <div>
                                <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                                    <span class="hamburger-box">
                                        <span class="hamburger-inner"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                        <div class="app-header__menu">
                            <span>
                                <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                                    <span class="btn-icon-wrapper">
                                        <i class="fa fa-ellipsis-v fa-w-6"></i>
                                    </span>
                                </button>
                            </span>
                        </div>
                        <div class="scrollbar-sidebar ps ps--active-x" style="overflow-y: scroll !important; overflow-x:hidden !important;">
                            <div class="app-sidebar__inner">
                                
        <div class="p-3">
            <input type="text" id="search-input" placeholder="Menu Search..." class="form-control">
        </div>
        
        <ul class="vertical-nav-menu metismenu" id="menu">
            <li class="app-sidebar__heading"></li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Main
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/index.aspx">
                                
                                DashBoard
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Main/Notifications.aspx">
                                
                                Your Notification
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Main/StudentFeedback.aspx">
                                
                                System FeedBack
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/UserProfile/UserProfileSearch.aspx">
                                
                                Search for staff
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Attendance
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Attendance/ClassAttendance_ViewStudentAttendance_001.aspx">
                                
                                View Attendance
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Berlin
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Berlin/Default.aspx">
                                
                                Study Abroad
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Course
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/CourseWork/Upload_001.aspx">
                                
                                Upload Course Work
    
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Evaluation
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Evaluation/EvaluateCourse.aspx">
                                
                                Evaluate Course
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Evaluation/EvaluateStaff.aspx">
                                
                                Evaluate Staff
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Exam
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Exam/ExamExcuseFinal.aspx">
                                
                                Final Exam Excuse 
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Exam/MakeupRegistration.aspx">
                                
                                Makeup Registration
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Exam/ViewExamSeat_01.aspx">
                                
                                Exam Seats
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Financial
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Financial/BalanceView_001.aspx">
                                
                                Financial Balance
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Grade
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Grade/CheckGrade_01.aspx">
                                
                                Check Grades
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Grade/CheckGradePerviousSemester_01.aspx">
                                
                                Check Previous Grades
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Grade/Transcript_001.aspx">
                                
                                Transcript
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Registration
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Registration/ChoosePharmacyProject_001.aspx">
                                
                                Pharmacy Project
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/ChoosePharmacyTrainingGroup_001.aspx">
                                
                                Pharmacy Training
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/CourseRegistrationView_001.aspx">
                                
                                Registered Courses 
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/ElectiveNavigation.aspx">
                                
                                Declare Elective/Seminar/Track
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/ExtraCourseReg.aspx">
                                
                                Placement Test Registration
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/LawRegistration.aspx">
                                
                                Law Registration
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/MajorNavigation.aspx">
                                
                                Declare Faculty/Major
    
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/SummerDashboard_001.aspx">
                                
                                Summer System
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Registration/SummerLanguageCoursesRegistration_001.aspx">
                                
                                Summer Language
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Reservation
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Reservations/Bus/Reservation.aspx">
                                
                                Bus Reservation
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Reservations/GermanPlacementReservation.aspx">
                                
                                German Placement Reservation
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Reservations/Session/SessionReservation_001.aspx">
                                
                                Session Reservation
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Reservations/TripReservation_01.aspx">
                                
                                Trip Reservation
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li class="mm-active">
                    <a href="#" class="main-menu " aria-expanded="true">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Scheduling
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse mm-show">
    
                
                         
                            <li>
                                <a href="/student_ext/Scheduling/ChangeGroupRequest.aspx">
                                
                                Change Group
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Scheduling/GeneralGroupSchedule.aspx">
                                
                                General Group Schedule
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Scheduling/GroupSchedule.aspx">
                                
                                Your Schedule
                                </a>
                            </li>
                        
                    
                         
                            <li class="mm-active">
                                <a href="/student_ext/Scheduling/SearchAcademicScheduled_001.aspx" class="mm-active" aria-expanded="true">
                                
                                Academic Schedule
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
                 <li>
                    <a href="#" class="main-menu ">
                       
                        <i class="metismenu-icon icon_application-form"></i>
                        Thesis
                        <i class="metismenu-state-icon caret-left icon_double-down"></i>
                        
                        
                    </a>
                 <ul class="mm-collapse">
    
                
                         
                            <li>
                                <a href="/student_ext/Thesis/Internship.aspx">
                                
                                Internship
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Thesis/ThesisNavigation.aspx">
                                
                                Select Thesis
                                </a>
                            </li>
                        
                    
                         
                            <li>
                                <a href="/student_ext/Thesis/UploadThesisTopicFile_01.aspx">
                                
                                Upload Thesis Topic File
                                </a>
                            </li>
                        
                    
                      </ul>
                      </li>
            
    
        </ul>
    
    
    
    
    
    
                            </div>
                        <div class="ps__rail-x" style="width: 60px; left: 0px; bottom: 0px;"><div class="ps__thumb-x" tabindex="0" style="left: 0px; width: 54px;"></div></div><div class="ps__rail-y" style="top: 0px; right: 0px;"><div class="ps__thumb-y" tabindex="0" style="top: 0px; height: 0px;"></div></div></div>
                    </div>
    
                    <div class="app-main__outer">
                        <div class="app-main__inner">
                            <div class="">  
                                <div class="">
                                    <div class="">
                                    </div>
                                    <div class="">
    
                                        
        <div class="app-inner-layout__header bg-heavy-rain">
                                <div class="app-page-title">
                                    <div class="page-title-wrapper">
                                        <div class="page-title-heading p-2">
                                            <div class="page-title-icon ">
                                                <i class="icon_terms-and-conditions"></i>
                                            </div>
                                            <div>
                                                <span id="ContentPlaceHolderright_LabelPageName" class="h5">Academic Schedule</span>
                                                <br>
                                                  <span id="ContentPlaceHolderright_Labelmasterpagemessage" style="font-size:16px;color:red;"></span>
                                                <div class="page-title-subheading">
                                                
                                                </div>
                                            </div>
                                        </div>
                                        <div class="page-title-actions d-print-none">
                                           <div class="p-2">
                                               <a href="https://apps.guc.edu.eg/www/BRM/UserEntries.aspx" data-toggle="tooltip" title="" data-placement="bottom" class="btn-shadow mr-3 btn btn-dark" data-original-title="Please contribute with us to enhance your experience">
                                                    <i class="fa fa-stack fa-star"></i>
                                                </a>
                                           </div>
                                            
                                        </div>    </div>
                                </div>                </div>
       
    
        
    
    
        <div class="row">
            <div class="col-12">
    
    
                <div class="row">
                    <div class="col-xl-12  col-lg-12 col-md-12 col-sm-12">
    
                        <div class="main-card mb-3 card text-left">
                            <div class="card-header">Usage Guide Lines (Searching and/or Adding Course(s)):</div>
                            <div class="card-body">
                                <h3>View All Course/Staff Schedules (Compensation)</h3>
                                <br>
                                <ul>
                                    <li>To add courses (if needed): please Click 
                                                      the <span class="add_link icon_link">+</span>
                                        symbol next to <b>Courses</b><br>
                                        Note that filtering is optional, you can use 
                                                      it to find quickly a course by typing a part from its name or code 
                                                      into the white box provided. 
                                                      </li><li>To add instructor or TA, Click the <span class="add_link icon_link">+</span> symbol 
                                                      next to <b>Staff</b>
                                    (if needed), also you 
                                                      can use the provided filter to quickly find the instructor 
                                                      </li><li>If you change your mind, simply click 
                                                      <span class=" icon_link">x</span>
                                    symbol 
                                                      preceding the filter to remove this choice 
                                                      </li><li>Finally, Click on Show Schedule to View 
                                                      all selected courses and instructor’s scheduled slots
                                                          <br>
                                                          e.g., to view slots of course A taught by 
                                                      staff X and slots of course B taught by staff Y, add the courses A 
                                                      and B, and staff members X and Y 
                                                      </li><li>Move your mouse cursor over any slot to 
                                                      see all slots for that course and all slots assigned to that 
                                                      instructor. Courses will be highlighted in <span class="active-course">red</span> while the 
                                                      instructor’s will be in <span class="active-staff">yellow</span> </li>
                                </ul>
                                <p></p>
                                <p>
                                    Filtering tips: 
                                                    </p><ul>
                                                        <li>
                                                        To select a specific course by code, 
                                                      simply enter its code in the filter and immediately the Courses 
                                                      list will be updated 
                                                      </li><li>This system filters are case 
                                                      insensitive<br>
                                                          e.g., if you typed: CSEN 202 
                                                      is just like typing csen 202 
                                                      </li><li>Wildcard symbol: <b>(.*)</b>
                                                        </li><li>Protip: you can use regular 
                                                      expressions</li>
                                                    </ul>
    
                                    <br>
    
                                    <div class="text-center">
                                        
                                        
                                        <p>Courses: <a class="icon_link add_link" href="javascript: generate_dropdown(courses, '#courses_list', 'course[]')">+</a></p>
                                        <div id="courses_list" class="to_add"></div>
    
                                        <p>Staff: <a class="icon_link add_link" href="javascript: generate_dropdown(tas, '#teaching_assistants', 'ta[]')">+</a></p>
                                        <div id="teaching_assistants"></div>
                                        <p>
                                            <input type="submit" name="ctl00$ctl00$ContentPlaceHolderright$ContentPlaceHoldercontent$B_ShowSchedule" value="Show Schedule" id="ContentPlaceHolderright_ContentPlaceHoldercontent_B_ShowSchedule" class="btn btn-primary" style="z-index: 0"><br>
                                        </p>
                                </div>
    
                            </div>
                            <div class="d-block text-right card-footer">
                            </div>
                        </div>
    
    
                    </div>
    
    
    
                </div>
    
    
                <br>
    
    
                  <div class="row">
                    <div class="col-xl-12  col-lg-12 col-md-12 col-sm-12">
    
                        <div class="main-card mb-3 card text-left">
                            <div class="card-header">Schedule:
                                <h6><span id="ContentPlaceHolderright_ContentPlaceHoldercontent_LblStaffName"> Abdel Megid Allam</span></h6>
                            </div>
                            <div class="card-body" style="overflow-x: auto; ">
                               
                                 <table id="ContentPlaceHolderright_ContentPlaceHoldercontent_schedule" class="schedule table table-bordered">
        <tbody><tr>
            <th>
                                                    &nbsp;</th><th>
                                                    1<sup>st</sup> First</th><th>
                                                    2<sup>nd</sup> Second</th><th>
                                                    3<sup>rd</sup> Third</th><th>
                                                    4<sup>th</sup> Fourth</th><th>
                                                    5<sup>th</sup> Fifth</th><th>
                                                    6<sup>th</sup> Sixth</th><th>
                                                    7<sup>th</sup> Seventh</th><th>
                                                    8<sup>th</sup> Eighth</th>
        </tr><tr>
            <th>
                                                Saturday</th><td>&nbsp;</td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH7 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH7 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="191"><dl><dt>Group</dt><dd class="course-191">ELCT 604 - 6ELCT4 (Tutorial)</dd><dt>Location</dt><dd>D4.105</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div><div class="slot" data-staff-id="2560" data-course-id="1523"><dl><dt>Group</dt><dd class="course-1523">MATS  911 - 10EMS-EL1 (Lecture)</dd><dt>Location</dt><dd>D4.301</dd><dt>Staff</dt><dd class="staff-2560">Abbas Yehia</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH7 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH7 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH1 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH1 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH1 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH1 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div></td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr><tr>
            <th>
                                            Sunday</th><td>&nbsp;<div class="slot" data-staff-id="9188" data-course-id="2703"><dl><dt>Group</dt><dd class="course-2703">PHTXt 612 - 6PH1 (Tutorial)</dd><dt>Location</dt><dd>B2.207</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div><div class="slot" data-staff-id="3064" data-course-id="76"><dl><dt>Group</dt><dd class="course-76">COMM 402 - 4IET1 (Lecture)</dd><dt>Location</dt><dd>H9</dd><dt>Staff</dt><dd class="staff-3064">Abdel Megid Allam</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH3 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2703"><dl><dt>Group</dt><dd class="course-2703">PHTXt 612 - 6PH7 (Tutorial)</dd><dt>Location</dt><dd>B2.207</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="79"><dl><dt>Group</dt><dd class="course-79">ELCT 201 - 2ENG39 (Tutorial)</dd><dt>Location</dt><dd>C5.105</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH3 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="191"><dl><dt>Group</dt><dd class="course-191">ELCT 604 - 6ELCT5 (Tutorial)</dd><dt>Location</dt><dd>C5.205</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="3064" data-course-id="635"><dl><dt>Group</dt><dd class="course-635">COMM 1002 - 10COMM1 (Lecture)</dd><dt>Location</dt><dd>D4.101</dd><dt>Staff</dt><dd class="staff-3064">Abdel Megid Allam</dd></dl><hr></div><div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH9 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="79"><dl><dt>Group</dt><dd class="course-79">ELCT 201 - 2ENG31 (Tutorial)</dd><dt>Location</dt><dd>C5.101</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH9 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="79"><dl><dt>Group</dt><dd class="course-79">ELCT 201 - 2ENG32 (Tutorial)</dd><dt>Location</dt><dd>D3.103</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr><tr>
            <th>
                                            Monday</th><td>&nbsp;<div class="slot" data-staff-id="9188" data-course-id="2703"><dl><dt>Group</dt><dd class="course-2703">PHTXt 612 - 6PH10 (Tutorial)</dd><dt>Location</dt><dd>B3.206</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="79"><dl><dt>Group</dt><dd class="course-79">ELCT 201 - 2ENG19 (Tutorial)</dd><dt>Location</dt><dd>D3.104</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="9199" data-course-id="79"><dl><dt>Group</dt><dd class="course-79">ELCT 201 - 2ENG7 (Tutorial)</dd><dt>Location</dt><dd>C5.201</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH11 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9199" data-course-id="79"><dl><dt>Group</dt><dd class="course-79">ELCT 201 - 2ENG18 (Tutorial)</dd><dt>Location</dt><dd>C5.105</dd><dt>Staff</dt><dd class="staff-9199">Abdelaziz Gohar</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH11 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div></td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr><tr>
            <th>
                                            Tuesday</th><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1211"><dl><dt>Group</dt><dd class="course-1211">MD 613 - 6ART-MD2 (Tutorial)</dd><dt>Location</dt><dd>D2.102-MD</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1211"><dl><dt>Group</dt><dd class="course-1211">MD 613 - 6ART-MD2 (Practical)</dd><dt>Location</dt><dd>D2.102-MD</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1211"><dl><dt>Group</dt><dd class="course-1211">MD 613 - 6ART-MD1 (Tutorial)</dd><dt>Location</dt><dd>D2.102-MD</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1211"><dl><dt>Group</dt><dd class="course-1211">MD 613 - 6ART-MD1 (Practical)</dd><dt>Location</dt><dd>D2.102-MD</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr><tr>
            <th>
                                            Wednesday</th><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH5 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH5 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div><div class="slot" data-staff-id="3064" data-course-id="186"><dl><dt>Group</dt><dd class="course-186">COMM 606 - 6COMM1 (Practical)</dd><dt>Location</dt><dd>C4.306</dd><dt>Staff</dt><dd class="staff-3064">Abdel Megid Allam</dd></dl><hr></div><div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Film3 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div><div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Hybrid Media5 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="8572" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH5 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-8572">Aesha  Mohammed </dd></dl><hr></div><div class="slot" data-staff-id="9188" data-course-id="2704"><dl><dt>Group</dt><dd class="course-2704">PHTXp 612 - 6PH5 (Practical)</dd><dt>Location</dt><dd>B1.01</dd><dt>Staff</dt><dd class="staff-9188">Abdelrahman Shoman</dd></dl><hr></div><div class="slot" data-staff-id="3064" data-course-id="186"><dl><dt>Group</dt><dd class="course-186">COMM 606 - 6COMM1 (Practical)</dd><dt>Location</dt><dd>C4.306</dd><dt>Staff</dt><dd class="staff-3064">Abdel Megid Allam</dd></dl><hr></div><div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Film3 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div><div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Hybrid Media5 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Film3 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div><div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Hybrid Media5 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr><tr>
            <th>
                                            Thursday</th><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Sound for Visuals2 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Sound for Visuals2 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;<div class="slot" data-staff-id="5968" data-course-id="1534"><dl><dt>Group</dt><dd class="course-1534">PR 1001 - 10ART-MD-Sound for Visuals2 (Tutorial)</dd><dt>Location</dt><dd>D4.01</dd><dt>Staff</dt><dd class="staff-5968">Abla Abdelnaby</dd></dl><hr></div></td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr><tr>
            <th>
                                            Friday</th><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>
        </tr>
    </tbody></table>
    
                            </div>
                            <div class="d-block text-right card-footer">
                            </div>
                        </div>
    
    
                    </div>
    
    
    
                </div>
    
    
    
    
    
    
    
    
            </div>
        </div>
    
    
    
    
    
    
        <br><br><br><br><br><br><br><br>
        
    
    
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
    
    
            </div>
    
    
    
            <div class="app-drawer-wrapper">
                <div class="drawer-nav-btn">
                    <button type="button" class="hamburger hamburger--elastic is-active">
                        <span class="hamburger-box"><span class="hamburger-inner"></span></span>
                    </button>
                </div>
                <div class="drawer-content-wrapper">
                    <div class="scrollbar-container ps">
                        
        <h3 class="drawer-heading">ShortCuts</h3>
        <div class="drawer-section">
            <div class="row">
            </div>
            <div class="divider"></div>
    
        </div>
    
    
                    <div class="ps__rail-x" style="left: 0px; bottom: 0px;"><div class="ps__thumb-x" tabindex="0" style="left: 0px; width: 0px;"></div></div><div class="ps__rail-y" style="top: 0px; right: 0px;"><div class="ps__thumb-y" tabindex="0" style="top: 0px; height: 0px;"></div></div></div>
                </div>
            </div>
    
            
    
        
    
    
    
            
    
            <input type="hidden" name="ctl00$ctl00$div_position" id="div_position" value="3773.***********">
        
    <script type="text/javascript">var courses = [{'id': '1155', 'value': 'ABSK 901: Academic Business Skills I'}, {'id': '1156', 'value': 'ABSK 902: Academic Business Skills II'}, {'id': '13', 'value': 'AE 101: Introduction to Academic English (B)'}, {'id': '2090', 'value': 'ARAB 201: Arabic For Law II (A)'}, {'id': '2091', 'value': 'ARAB 202: Arabic For Law II (B)'}, {'id': '2954', 'value': 'ARAB 203: Arabic For Law I (A)'}, {'id': '2955', 'value': 'ARAB 204: Arabic For Law I (B)'}, {'id': '1886', 'value': 'ARCH  800: Architecture Bachelor Thesis'}, {'id': '1864', 'value': 'ARCH 1001: Design Studio VII - International Urban Design and Landscaping Project'}, {'id': '1865', 'value': 'ARCH 1002: Design Studio VII - Working Drawings'}, {'id': '1866', 'value': 'ARCH 1003: Advanced Theory of Architecture and Urban Design'}, {'id': '2927', 'value': 'ARCH 1048: Place Making Play in Cities'}, {'id': '2928', 'value': 'ARCH 1049: Emergency Architecture - Displacement Urbanism'}, {'id': '2930', 'value': 'ARCH 1050: SPATIAL INFUSIONS-Ephemeral impact in permanent environments'}, {'id': '3026', 'value': 'ARCH 1051: The Aura and The Skin'}, {'id': '3027', 'value': 'ARCH 1052: Live Cycle Earth Constructions'}, {'id': '3028', 'value': 'ARCH 1053: Visual Storytelling in Cairo'}, {'id': '1504', 'value': 'ARCH 202: History of Architecture I'}, {'id': '1505', 'value': 'ARCH 203: Visual Design'}, {'id': '1506', 'value': 'ARCH 204: Introduction to Architectural Design'}, {'id': '1507', 'value': 'ARCH 205: Building Technology I(Building Construction, Building Physics, Building Materials, Lighting, Acoustics, Technical Installations)'}, {'id': '1554', 'value': 'ARCH 206: 3D Model-Making Studio'}, {'id': '1550', 'value': 'ARCH 303: Theory of Architecture I'}, {'id': '1609', 'value': 'ARCH 401: History of Architecture III'}, {'id': '1610', 'value': 'ARCH 402: CAD / CAM II'}, {'id': '1611', 'value': 'ARCH 404: Building Technology III'}, {'id': '1612', 'value': 'ARCH 405: Design Studio II'}, {'id': '1613', 'value': 'ARCH 406: Concrete Structures Design'}, {'id': '1629', 'value': 'ARCH 601: CAAD/CAM IV'}, {'id': '1630', 'value': 'ARCH 602: Design Studio IV (selected Urban and Architectural Design Projects)'}, {'id': '1631', 'value': 'ARCH 603: Surveying'}, {'id': '1858', 'value': 'ARCH 901: Design Studio VI - Urban Design and Landscaping'}, {'id': '2099', 'value': 'ARCH 918: Introduction to Interior Design'}, {'id': '14', 'value': 'AS 102: English for Academic Purposes (A1)'}, {'id': '199', 'value': 'BINF 201: Introduction to business informatics'}, {'id': '486', 'value': 'BINF 405: Information and Communication Architecture II'}, {'id': '2708', 'value': 'BINF 406: Digital Transformation'}, {'id': '597', 'value': 'BINF 506: Research Methodology for BINF'}, {'id': '626', 'value': 'BINF 609: Enterprise System Applications'}, {'id': '808', 'value': 'BINF 720: Seminar in Business Informatics'}, {'id': '1008', 'value': 'BINF 820: Bachelor Project'}, {'id': '1186', 'value': 'BINF 901: Business Process Management'}, {'id': '2963', 'value': 'BIOT 1001: Selected Advanced Specialized Topics I'}, {'id': '2964', 'value': 'BIOT 1002: Selected Advanced Specialized Topics II'}, {'id': '2965', 'value': 'BIOT 1003: Selected Advanced Specialized Topics III'}, {'id': '896', 'value': 'BIOT 601: Introduction to Management'}, {'id': '895', 'value': 'BIOT 611: Biophysics'}, {'id': '892', 'value': 'BIOT 651: Introduction to Biosafety'}, {'id': '891', 'value': 'BIOT 681: Bioinformatics'}, {'id': '2501', 'value': 'BIOT 693: Technical Chemistry & Process Engineering'}, {'id': '2938', 'value': 'BIOT 810: Bioinformatics II'}, {'id': '902', 'value': 'BIOT 899: Bachelor Thesis'}, {'id': '2503', 'value': 'BIOTp 633: Genetics & Genetic Engineering I'}, {'id': '2502', 'value': 'BIOTp 643: Cell Biology'}, {'id': '2500', 'value': 'BIOTt 633: Genetics & Genetic Engineering I'}, {'id': '2499', 'value': 'BIOTt 643: Cell Biology'}, {'id': '1868', 'value': 'BTECH 1001: Design Studio VII - Architecture'}, {'id': '1869', 'value': 'BTECH 1002: Design Studio VII - Working Drawings'}, {'id': '1870', 'value': 'BTECH 1003: Renewable Energy Technologies in Buildings'}, {'id': '2317', 'value': 'BTECH 1008: Building Information Modeling'}, {'id': '2929', 'value': 'BTECH 1018: Bio-Structures'}, {'id': '1190', 'value': 'CBD 201: Digital Media II'}, {'id': '1192', 'value': 'CBD 402: Web Design'}, {'id': '1193', 'value': 'CGD 401: Computer Tools Graphics'}, {'id': '457', 'value': 'CHEM 401: Physical Chemistry'}, {'id': '2480', 'value': 'CICO 801: Enforcement'}, {'id': '1795', 'value': 'CIG  602: Construction Management I'}, {'id': '1799', 'value': 'CIG  604: Building and High-rise Construction'}, {'id': '1839', 'value': 'CIG 1001: Numerical Methods and Advanced Statistics'}, {'id': '1838', 'value': 'CIG 1002: GIS for Civil Engineering Applications'}, {'id': '1908', 'value': 'CIG 1004: Environmental Impact Assessment'}, {'id': '1843', 'value': 'CIG 1005: Construction Management III'}, {'id': '3014', 'value': 'CIG 1018: Claims and Dispute Management in Construction Project'}, {'id': '1555', 'value': 'CIG 201: Civil Engineering Drawing'}, {'id': '1558', 'value': 'CIG 403: Plane Surveying'}, {'id': '2084', 'value': 'CILA 202: Legal Research'}, {'id': '2085', 'value': 'CILA 203: Legal Research and Drafting Legal Opinion'}, {'id': '2175', 'value': 'CILA 301: Civil Law (Theory of Contract)'}, {'id': '2497', 'value': 'CILA 603: Law of Civil and Commercial Procedures'}, {'id': '2403', 'value': 'CILA 701: Social Insurance Law'}, {'id': '2483', 'value': 'CILA 801: International Intellectual Property Rights'}, {'id': '2484', 'value': 'CILA 802: Civil Law (Ownership & Derived rights)'}, {'id': '1844', 'value': 'CIS 1003: Special Topics in Reinforced Concrete Design'}, {'id': '1603', 'value': 'CIS 401: Properties and Testing of Materials II'}, {'id': '1602', 'value': 'CIS 402: Structural Analysis II'}, {'id': '1644', 'value': 'CIS 601: Steel Structures Design II'}, {'id': '1640', 'value': 'CIS 603: Design of Reinforced Concrete Structures II'}, {'id': '1710', 'value': 'CIS 606: Geotechnical and Foundation Engineering'}, {'id': '1711', 'value': 'CIS 607: Matrix Analysis in Structural Engineering'}, {'id': '1608', 'value': 'CIT 401: Transportation Systems'}, {'id': '1712', 'value': 'CIT 603: Highway and Railway Design'}, {'id': '1637', 'value': 'CIW 402: Fluid Mechanics'}, {'id': '1647', 'value': 'CIW 601: Hydraulics'}, {'id': '2911', 'value': 'CIW 903: River Engineering'}, {'id': '3033', 'value': 'CLPH  1202: Pharmacotherapy II'}, {'id': '3035', 'value': 'CLPH  1204: Clinical Nutrition'}, {'id': '2933', 'value': 'CLPH 1032: Pharmacoepidemiology & Economy'}, {'id': '2650', 'value': 'CLPH 106: Clinical Nutrition'}, {'id': '3032', 'value': 'CLPH 1201: Health Informatics II'}, {'id': '3034', 'value': 'CLPH 1203: Selected Clinical Topics II'}, {'id': '2647', 'value': 'CLPH 301: Pharmacotherapy III'}, {'id': '2936', 'value': 'CLPHp 1023: Pharmacotherapy'}, {'id': '2776', 'value': 'CLPHp 811: Clinical Pharmacy Practice I'}, {'id': '2932', 'value': 'CLPHt 1023: Pharmacotherapy'}, {'id': '2775', 'value': 'CLPHt 811: Clinical Pharmacy Practice I'}, {'id': '1194', 'value': 'CMD 401: Computer Tools Media'}, {'id': '2034', 'value': 'CMLA 101: Introduction to Common Law'}, {'id': '2079', 'value': 'CMLA 201: Political Systems'}, {'id': '2324', 'value': 'CMLA 401: Administrative Law'}, {'id': '2384', 'value': 'CMLA 601: Administrative Law II'}, {'id': '2322', 'value': 'COLA 401: The Legal Aspects of International Trade Law and WTO'}, {'id': '2380', 'value': 'COLA 601: Commercial Law (Companies and Investment Law)'}, {'id': '2479', 'value': 'COLA 801: International Commercial Arbitration'}, {'id': '2485', 'value': 'COLA 803: Securities regulation in a Comparative perspective'}, {'id': '3029', 'value': 'COLA 804: Commercial Law (Banking Operations & Financial Law)'}, {'id': '634', 'value': 'COMM 1001: Modulation and Coding'}, {'id': '635', 'value': 'COMM 1002: Adaptive Antennas'}, {'id': '636', 'value': 'COMM 1003: Information Theory'}, {'id': '637', 'value': 'COMM 1005: Advanced Communication Lab'}, {'id': '2566', 'value': 'COMM 1018: Future Mobile Technologies'}, {'id': '401', 'value': 'COMM 401: Signal & System Theory'}, {'id': '76', 'value': 'COMM 402: Electromagnetics'}, {'id': '182', 'value': 'COMM 601: Modulation I'}, {'id': '181', 'value': 'COMM 602: Digital Signal Processing'}, {'id': '183', 'value': 'COMM 603: Radio Frequency Engineering'}, {'id': '184', 'value': 'COMM 604: Channel Coding'}, {'id': '186', 'value': 'COMM 606: Communication Lab'}, {'id': '1195', 'value': 'CPD 401: Computer Tools Product'}, {'id': '101', 'value': 'CPS 402: Communication & Presentation Skills (A2)'}, {'id': '2082', 'value': 'CRLA 201: Criminology and Penology'}, {'id': '2325', 'value': 'CRLA 401: Administrative Law (Constitutional & Legal Framework)'}, {'id': '2326', 'value': 'CRLA 402: Criminal Law (General Rules)'}, {'id': '2498', 'value': 'CRLA 603: Moot Court Including visiting courts and Legal Institutions'}, {'id': '1699', 'value': 'CSEN  1034: Seminar on Rational Belief Change'}, {'id': '628', 'value': 'CSEN 1001: Computer and Network Security'}, {'id': '629', 'value': 'CSEN 1002: Advanced Computer Lab'}, {'id': '632', 'value': 'CSEN 1003: Compiler'}, {'id': '1157', 'value': 'CSEN 1008: Seminar on Intelligent Transportation Systems'}, {'id': '350', 'value': 'CSEN 102: Introduction to computer science'}, {'id': '1766', 'value': 'CSEN 1038: Advanced Data structures and Algorithms'}, {'id': '2093', 'value': 'CSEN 1076: Natural Language Processing and Information Retrieval'}, {'id': '2368', 'value': 'CSEN 1088: Seminar in Smart Cities'}, {'id': '2676', 'value': 'CSEN 1118: Seminar in Machine Learning and AI for Art'}, {'id': '2748', 'value': 'CSEN 1126: Seminar in Hand-drawn Sketches in Artificial Intelligence'}, {'id': '2749', 'value': 'CSEN 1127: Seminar in Healthcare Technology'}, {'id': '2750', 'value': 'CSEN 1128: Seminar in Machine Learning Applications in Bioinformatics'}, {'id': '2918', 'value': 'CSEN 1134: Seminar on Unsupervised Classification'}, {'id': '2919', 'value': 'CSEN 1135: Seminar on Brain Tumor Detection Out of MRI Scans'}, {'id': '3021', 'value': 'CSEN 1139: Seminar in Leveraging Large Language Models for Practical Applications'}, {'id': '3022', 'value': 'CSEN 1140: Seminar in Graph Neural Networks: Theory and Applications'}, {'id': '3023', 'value': 'CSEN 1141: Seminar on Remote Sensing Image Analysis'}, {'id': '3024', 'value': 'CSEN 1142: Seminar in Innovations in Medical Imaging'}, {'id': '3025', 'value': 'CSEN 1143: Seminar on XR in Healthcare and Therapy'}, {'id': '19', 'value': 'CSEN 202: Introduction to Computer Programming'}, {'id': '402', 'value': 'CSEN 401: Computer Programming Lab'}, {'id': '717', 'value': 'CSEN 402: Computer Organization and System Programming'}, {'id': '80', 'value': 'CSEN 403: Concepts of Programming Languages'}, {'id': '510', 'value': 'CSEN 404: Introduction to Networks'}, {'id': '174', 'value': 'CSEN 601: Computer System Architecture'}, {'id': '175', 'value': 'CSEN 602: Operating Systems'}, {'id': '178', 'value': 'CSEN 603: Software Engineering'}, {'id': '436', 'value': 'CSEN 603: Software Engineering'}, {'id': '437', 'value': 'CSEN 604: Data Bases II'}, {'id': '563', 'value': 'CSEN 907: Knowledge Representation And Reasoning'}, {'id': '26', 'value': 'CSIS 202: Introduction to Computer Science II'}, {'id': '77', 'value': 'CSIS 402: Computer Organization and System Programming'}, {'id': '2947', 'value': 'CSTH 1202: Cosmetics Business Essentials'}, {'id': '3037', 'value': 'CSTH 1205: Cosmetics Formulation Technology II'}, {'id': '3038', 'value': 'CSTH 1206: Marketing Innovations with AI Integration'}, {'id': '3039', 'value': 'CSTH 1207:  Graduation Project'}, {'id': '1877', 'value': 'CTRL 1901: Advanced Accounting'}, {'id': '142', 'value': 'CTRL 202: Financial Accounting II'}, {'id': '93', 'value': 'CTRL 402: Management Accounting II'}, {'id': '160', 'value': 'CTRL 606: Taxation'}, {'id': '221', 'value': 'CTRL 705: Seminar in Management Control'}, {'id': '223', 'value': 'CTRL 707: Internal Auditing and Risk Management I'}, {'id': '1703', 'value': 'CTRL 710: Cost Accounting'}, {'id': '1953', 'value': 'CTRL 712: Financial Reporting and Analysis'}, {'id': '1952', 'value': 'CTRL 714: Intermediate Accounting II'}, {'id': '286', 'value': 'CTRL 720: Seminar in Accounting (1st Major seminar)'}, {'id': '1581', 'value': 'CTRL 801: Management Control Thesis'}, {'id': '509', 'value': 'CTRL 820: Seminar in Accounting (2nd Major seminar)'}, {'id': '37', 'value': 'CTRL 901: Advanced Accounting'}, {'id': '1112', 'value': 'DD 201: Bionic and Perception'}, {'id': '15', 'value': 'DE 101: Basic German 1'}, {'id': '33', 'value': 'DE 202: Basic German 2'}, {'id': '64', 'value': 'DE 303: Basic German 3'}, {'id': '73', 'value': 'DE 404: Basic German 4'}, {'id': '630', 'value': 'DMET 1001: Image Processing'}, {'id': '631', 'value': 'DMET 1002: Advanced Media Lab'}, {'id': '633', 'value': 'DMET 1003: Audio and Acoustics'}, {'id': '1890', 'value': 'DMET 1042: Fundamentals of VoIP'}, {'id': '2069', 'value': 'DMET 1057: Seminar in Selected Applications of Media Processing'}, {'id': '2299', 'value': 'DMET 1061: Seminar in Computer Vision and Image Understanding'}, {'id': '2461', 'value': 'DMET 1067: Deep Learning in Computer Vision'}, {'id': '2672', 'value': 'DMET 1072: Computer Animation'}, {'id': '3013', 'value': 'DMET 1075: Introduction to Augmented and Virtual Reality'}, {'id': '3017', 'value': 'DMET 1076: Seminar on Deep Learning for Computer Vision'}, {'id': '3019', 'value': 'DMET 1077: Seminar on Advanced Applications of LL and Vision Models'}, {'id': '438', 'value': 'DMET 601: Web Technologies and Usability'}, {'id': '403', 'value': 'DMET 602: Network & Media lab'}, {'id': '1071', 'value': 'DMET 603: Digital Signal Processing'}, {'id': '2656', 'value': 'DTSC 104: Advanced Methods in Data Science '}, {'id': '2657', 'value': 'DTSC 105: Capstone Project '}, {'id': '2659', 'value': 'DTSC 107: Text Mining '}, {'id': '2660', 'value': 'DTSC 108: Advanced Big Data Analytics Technologies  '}, {'id': '3031', 'value': 'DTSC 110: Financial Technologies'}, {'id': '1878', 'value': 'ECON 1901: Business Economics'}, {'id': '57', 'value': 'ECON 403: Macroeconomics'}, {'id': '159', 'value': 'ECON 605: Money & Banking'}, {'id': '229', 'value': 'ECON 701: International Trade & Trade Policy'}, {'id': '469', 'value': 'ECON 713: International Trade'}, {'id': '470', 'value': 'ECON 714: Introduction to Econometrics'}, {'id': '502', 'value': 'ECON 820: Seminar in Economics (2nd Major seminar)'}, {'id': '38', 'value': 'ECON 901: Business Economics'}, {'id': '617', 'value': 'ECON 920: Advanced Microeconomics'}, {'id': '2311', 'value': 'EDPT  1017: Introduction to Lean Manufacturing'}, {'id': '709', 'value': 'EDPT 1010: Design of Jigs, Fixtures'}, {'id': '710', 'value': 'EDPT 1011: Facility Planning'}, {'id': '711', 'value': 'EDPT 1012: Manufacturing System Design and Simulation'}, {'id': '712', 'value': 'EDPT 1013: Materials and Process Selection in Design'}, {'id': '432', 'value': 'EDPT 201: Production Technology'}, {'id': '322', 'value': 'EDPT 401: CAD- Lab'}, {'id': '693', 'value': 'EDPT 402: Material Removal Processes and Machines'}, {'id': '694', 'value': 'EDPT 403: Machine Drawing With CAD'}, {'id': '452', 'value': 'EDPT 601: Materials Manufacturing Technology'}, {'id': '196', 'value': 'EDPT 602: Engineering Design II'}, {'id': '713', 'value': 'EDPT 603: Theory Of Metal Cutting'}, {'id': '714', 'value': 'EDPT 604: Metal Forming Processes and Machines'}, {'id': '638', 'value': 'ELCT 1001: Optoelectronic Devices and Circuits'}, {'id': '639', 'value': 'ELCT 1002: Systems on a Chip'}, {'id': '640', 'value': 'ELCT 1003: High Speed Electronic Circuits'}, {'id': '641', 'value': 'ELCT 1005: Advanced Microelectronics Lab'}, {'id': '2458', 'value': 'ELCT 1018: Introduction to Quantum Technologies & Computing'}, {'id': '2794', 'value': 'ELCT 1021: Cosmetic Technology'}, {'id': '2795', 'value': 'ELCT 1041: Clinical Nutrition'}, {'id': '2796', 'value': 'ELCT 1051: Applied Toxicology'}, {'id': '2923', 'value': 'ELCT 1052: Inorganic Pharmaceuticals and Diagnostics'}, {'id': '2924', 'value': 'ELCT 1053: Recent Trends in Medicinal Chemistry and Drug Discovery'}, {'id': '2797', 'value': 'ELCT 1111: Pharmacogenomics and Pharmacogenetics'}, {'id': '79', 'value': 'ELCT 201: Digital Logic Design'}, {'id': '47', 'value': 'ELCT 401: Electrical Circuits II'}, {'id': '465', 'value': 'ELCT 601: Digital System Design'}, {'id': '594', 'value': 'ELCT 601: Electrical Engineering'}, {'id': '189', 'value': 'ELCT 602: Solid State Electronics'}, {'id': '190', 'value': 'ELCT 603: Optoelectronics'}, {'id': '191', 'value': 'ELCT 604: Electronic Circuits'}, {'id': '192', 'value': 'ELCT 605: Microelectronics Lab'}, {'id': '1101', 'value': 'ELCT 609: Electronic Circuits'}, {'id': '1219', 'value': 'ELCT 910: Integrated Circuit Design for Wireless Communications'}, {'id': '549', 'value': 'ENG 800: Engineering Bachelor Thesis'}, {'id': '49', 'value': 'ENGD 301: Engineering Drawing & Design'}, {'id': '83', 'value': 'ENME 401: Strength of Materials I'}, {'id': '459', 'value': 'ENME 402: Mechanics II'}, {'id': '84', 'value': 'ENME 602: Numerical Analysis'}, {'id': '595', 'value': 'ENME 603: Heat and Mass Transfer'}, {'id': '1882', 'value': 'FINC 1901: Corporate Finance'}, {'id': '2613', 'value': 'FINC 2101: Advanced Topics in Accounting '}, {'id': '2625', 'value': 'FINC 2201: Financial Markets & Institutions '}, {'id': '2615', 'value': 'FINC 2301: Advanced Topics in Finance'}, {'id': '2624', 'value': 'FINC 2302: Contemporary Issues in Finance'}, {'id': '2620', 'value': 'FINC 2401: Financial Analysis & Security Valuation'}, {'id': '2629', 'value': 'FINC 2500: Finance Comprehensive Exam'}, {'id': '2630', 'value': 'FINC 2600: Finance Dissertation'}, {'id': '89', 'value': 'FINC 403: Managerial Finance'}, {'id': '108', 'value': 'FINC 702: Portfolio Management and Investement Analysis'}, {'id': '212', 'value': 'FINC 704: Advanced Corporate Finance'}, {'id': '214', 'value': 'FINC 705: Finance Seminar'}, {'id': '475', 'value': 'FINC 713: Banking Management and Credit Analysis'}, {'id': '272', 'value': 'FINC 714: Derivatives'}, {'id': '273', 'value': 'FINC 720: Seminar in Finance (1st Major seminar)'}, {'id': '1580', 'value': 'FINC 801: Finance Thesis'}, {'id': '504', 'value': 'FINC 820: Seminar in Finance (2nd Major seminar)'}, {'id': '67', 'value': 'FINC 901: Corporate Finance'}, {'id': '1187', 'value': 'GD 202: Grid Structure and Color Systems'}, {'id': '1196', 'value': 'GD 402: GD Project'}, {'id': '1201', 'value': 'GD 613: Sign Systems'}, {'id': '1202', 'value': 'GD 614: Font Design II (Arabic)'}, {'id': '1530', 'value': 'GD 615: Global Communication I'}, {'id': '1529', 'value': 'GD 616: Interaction Design I'}, {'id': '1205', 'value': 'GD 801: Bachelor Project - Graphic Design'}, {'id': '1153', 'value': 'GMAT 902: Management Aptitude Enhancement- Verbal'}, {'id': '1154', 'value': 'GMAT 903: Management Aptitude Enhancement- Quantitative'}, {'id': '1880', 'value': 'HROB 1901: Introduction to Human Resource Management'}, {'id': '88', 'value': 'HROB 201: Human Resources Management'}, {'id': '2488', 'value': 'HROB 203: Human Resources Management for BI'}, {'id': '226', 'value': 'HROB 703: Organizational Change'}, {'id': '228', 'value': 'HROB 705: Seminar In Human Resources & Organizational Behavior'}, {'id': '513', 'value': 'HROB 706: Compensation Management'}, {'id': '1961', 'value': 'HROB 713: Compensation and Performance Management'}, {'id': '1950', 'value': 'HROB 714: Staffing & Development'}, {'id': '278', 'value': 'HROB 720: Seminar in Human Resources (1st Major seminar)'}, {'id': '1584', 'value': 'HROB 801: Human Resources & Organizational Behavior Thesis'}, {'id': '506', 'value': 'HROB 820: Seminar in Human Resources (2nd Major seminar)'}, {'id': '40', 'value': 'HROB 901: Introduction to Human Resource Management'}, {'id': '754', 'value': 'HUMA 1001: Project Management'}, {'id': '683', 'value': 'HUMA 1001: Project Management'}, {'id': '2592', 'value': 'HUMA 416: Human Rights '}, {'id': '715', 'value': 'HUMA 601: Introduction to Management'}, {'id': '232', 'value': 'IBUS 703: Transnational Management'}, {'id': '233', 'value': 'IBUS 705: Seminar in International Business'}, {'id': '2858', 'value': 'IBUS 710: Contemporary Issues in International Business'}, {'id': '1954', 'value': 'IBUS 713: Intercultural Aspects'}, {'id': '1586', 'value': 'IBUS 801: International Business Thesis'}, {'id': '508', 'value': 'IBUS 820: Seminar in International Business (2nd Major seminar)'}, {'id': '624', 'value': 'IBUS 920: Advanced International Business'}, {'id': '1879', 'value': 'INNO 1901: Entrepreneurship'}, {'id': '246', 'value': 'INNO 701: Managing the Innovation Process'}, {'id': '249', 'value': 'INNO 705: Seminar In Innovation & Technology Management'}, {'id': '987', 'value': 'INNO 714: Project Management'}, {'id': '2857', 'value': 'INNO 715: Emerging Technologies and Business Model Innovation'}, {'id': '767', 'value': 'INNO 720: Seminar in Innovation (1st Major seminar)'}, {'id': '1588', 'value': 'INNO 801: Innovation and Technology Thesis'}, {'id': '988', 'value': 'INNO 820: Seminar in Innovation (2nd Major seminar)'}, {'id': '39', 'value': 'INNO 901: Entrepreneurship'}, {'id': '100', 'value': 'INSY 402: Information Systems II'}, {'id': '234', 'value': 'INSY 701: Information Management'}, {'id': '238', 'value': 'INSY 705: Seminar in Information Systems'}, {'id': '2716', 'value': 'INSY 708: Digital Transformation'}, {'id': '477', 'value': 'INSY 714: IT Project Management'}, {'id': '2874', 'value': 'INSY 715: Digital Transformation'}, {'id': '281', 'value': 'INSY 720: Seminar in Information Systems (1st Major seminar)'}, {'id': '1585', 'value': 'INSY 801: Information Systems Thesis'}, {'id': '505', 'value': 'INSY 820: Seminar in Information Systems (2nd Major seminar)'}, {'id': '2081', 'value': 'ISSH 201: Principles of Islamic Sharia'}, {'id': '2378', 'value': 'ISSH 601: Islamic Sharia (Inheritance, Will and Trust)'}, {'id': '2490', 'value': 'LAW 801: Environmental Law: International & National Standards'}, {'id': '2778', 'value': 'LAW 804: Sports Law'}, {'id': '2487', 'value': 'LAW 805: Bachelor Thesis'}, {'id': '31', 'value': 'LAWS 201: Principles of Law'}, {'id': '2', 'value': 'MATH 103: Maths'}, {'id': '29', 'value': 'MATH 201: Math & Statistics II'}, {'id': '17', 'value': 'MATH 203: Mathematics I'}, {'id': '200', 'value': 'Math 204: Mathematics for business informatics II'}, {'id': '74', 'value': 'MATH 401: Math IV Probability and Statistics'}, {'id': '1072', 'value': 'MATH 404: Math IV'}, {'id': '36', 'value': 'MATH 901: Math & Statistics'}, {'id': '1523', 'value': 'MATS  911: Manufacturing of Plastic and Rubber Products'}, {'id': '646', 'value': 'MATS 1001: Surface Engineering'}, {'id': '647', 'value': 'MATS 1002: Destructive and Non Destructive Testing of materials'}, {'id': '1471', 'value': 'MATS 1010: Failure analysis of mechanical components'}, {'id': '2302', 'value': 'MATS 1019: New product Development and Innovation Management'}, {'id': '2567', 'value': 'MATS 1023: Soft Matter Nanotechnology'}, {'id': '81', 'value': 'MATS 401: Ferrous and Non-Ferrous Alloys'}, {'id': '460', 'value': 'MATS 402: Materials Lab I'}, {'id': '695', 'value': 'MATS 403: Materials Engineering I: Metallic Materials'}, {'id': '718', 'value': 'MATS 404: Introduction to Materials Engineering'}, {'id': '1104', 'value': 'MATS 405: Destructive and Non Destructive Testing Of Materials'}, {'id': '593', 'value': 'MATS 601: Composites and Ceramic Materials'}, {'id': '454', 'value': 'MATS 602: Engineering Polymers'}, {'id': '1768', 'value': 'MCTR  1010: Image processing for Mechatronics'}, {'id': '730', 'value': 'MCTR 1002: Autonomous System'}, {'id': '1460', 'value': 'MCTR 1004: Cooling of Electronic Systems'}, {'id': '1469', 'value': 'MCTR 1006: Renewable/Sustainable Energy Technology'}, {'id': '1486', 'value': 'MCTR 1007: Modeling and Simulation of Electrohydraulic Systems'}, {'id': '1579', 'value': 'MCTR 1009: Finite Element Methods'}, {'id': '2003', 'value': 'MCTR 1017: Industrial Sensors And Applications'}, {'id': '2570', 'value': 'MCTR 1024: Reinforcement Learning and Optimal Control'}, {'id': '721', 'value': 'MCTR 601: Mechatronics Engineering'}, {'id': '1526', 'value': 'MCTR 908: Electric-Drives'}, {'id': '1188', 'value': 'MD 202: Photography'}, {'id': '1198', 'value': 'MD 402: Media Design Project'}, {'id': '933', 'value': 'MD 609: Digital Compositing I'}, {'id': '1216', 'value': 'MD 612: Montage'}, {'id': '1211', 'value': 'MD 613: Sound'}, {'id': '1212', 'value': 'MD 614: Moving image/interactive design'}, {'id': '1213', 'value': 'MD 615: Media Installation'}, {'id': '1204', 'value': 'MD 801: Bachelor Project - Media Design'}, {'id': '2611', 'value': 'MGMT 2101: Advanced Multivariate Statistics'}, {'id': '2612', 'value': 'MGMT 2102: Advanced Organizational Behavior '}, {'id': '2616', 'value': 'MGMT 2201: Industry 4.0'}, {'id': '2622', 'value': 'MGMT 2202: Advanced Strategic Management'}, {'id': '2619', 'value': 'MGMT 2301: Advanced Research Methodology'}, {'id': '2628', 'value': 'MGMT 2302: Strategic Human Resource Management '}, {'id': '2621', 'value': 'MGMT 2401: Qualitative Analysis '}, {'id': '2626', 'value': 'MGMT 2402: Organizational Performance Management '}, {'id': '2618', 'value': 'MGMT 2403: Organization Design'}, {'id': '2804', 'value': 'MGMT 2500: Management Comprehensive Exam'}, {'id': '2807', 'value': 'MGMT 2600: Management Dissertation'}, {'id': '137', 'value': 'MGMT 502: Research Methodology'}, {'id': '161', 'value': 'MGMT 604: Quantitative & Qualitative Analysis'}, {'id': '548', 'value': 'MGMT 800: Management Bachelor Thesis'}, {'id': '176', 'value': 'MNGT 601: Introduction to Management'}, {'id': '1881', 'value': 'MRKT 1901: Marketing'}, {'id': '2614', 'value': 'MRKT 2201: Marketing Theory'}, {'id': '2803', 'value': 'MRKT 2500: Marketing Comprehensive Exam'}, {'id': '92', 'value': 'MRKT 402: Marketing II'}, {'id': '158', 'value': 'MRKT 602: Consumer Behaviour'}, {'id': '71', 'value': 'MRKT 704: International Marketing'}, {'id': '216', 'value': 'MRKT 705: Seminar in Marketing'}, {'id': '480', 'value': 'MRKT 713: International Marketing'}, {'id': '1951', 'value': 'MRKT 714: Marketing Channels and Distribution'}, {'id': '2862', 'value': 'MRKT 717: Sustainability Marketing'}, {'id': '217', 'value': 'MRKT 718: Strategic Marketing'}, {'id': '276', 'value': 'MRKT 720: Seminar in Marketing (1st Major seminar)'}, {'id': '1664', 'value': 'MRKT 730: Special Topics in Marketing'}, {'id': '1582', 'value': 'MRKT 801: Marketing Thesis'}, {'id': '507', 'value': 'MRKT 820: Seminar in Marketing (2nd Major seminar)'}, {'id': '66', 'value': 'MRKT 901: Marketing'}, {'id': '2350', 'value': 'NETW  1013: Machine Learning'}, {'id': '642', 'value': 'NETW 1001: Network Management'}, {'id': '643', 'value': 'NETW 1002: Systems and Network Security'}, {'id': '644', 'value': 'NETW 1003: Network Planning'}, {'id': '645', 'value': 'NETW 1005: Advanced Network Lab'}, {'id': '1705', 'value': 'NETW 1009: Cloud Computing (ISM)'}, {'id': '353', 'value': 'NETW 501: Communication Networks'}, {'id': '185', 'value': 'NETW 601: Transmission and Switching'}, {'id': '974', 'value': 'NETW 602: Network Lab'}, {'id': '2709', 'value': 'NETW 603: Computer System Architecture'}, {'id': '300', 'value': 'NETW 703: Network Protocols'}, {'id': '304', 'value': 'NETW 707: Modelling and Simulation'}, {'id': '1883', 'value': 'OPER 1901: Operations Management'}, {'id': '91', 'value': 'OPER 602: Operations II'}, {'id': '244', 'value': 'OPER 705: Seminar in Operations Management'}, {'id': '731', 'value': 'OPER 713: Operation in Service Organization'}, {'id': '732', 'value': 'OPER 714: Supply Chain Management'}, {'id': '1665', 'value': 'OPER 730: Special Topics in Operation Management'}, {'id': '1583', 'value': 'OPER 801: Operations & Production Management Thesis'}, {'id': '733', 'value': 'OPER 820: Seminar in Operations (2nd Major seminar)'}, {'id': '105', 'value': 'OPER 901: Operations Management'}, {'id': '1189', 'value': 'PD 202: Form generation'}, {'id': '1197', 'value': 'PD 402: PD Project'}, {'id': '830', 'value': 'PD 601: Exhibition Design'}, {'id': '832', 'value': 'PD 603: Computer Aided Design (CAD)'}, {'id': '837', 'value': 'PD 608: Material Science'}, {'id': '1214', 'value': 'PD 609: Lighting Design'}, {'id': '1215', 'value': 'PD 610: Production Techniques'}, {'id': '1152', 'value': 'PD 801: Bachelor Project - Product Design'}, {'id': '2327', 'value': 'PEPF 401: Political Economics'}, {'id': '2383', 'value': 'PEPF 601: Public Finance'}, {'id': '2706', 'value': 'PHBCp 622: Biochemistry II'}, {'id': '2705', 'value': 'PHBCt 622: Biochemistry II'}, {'id': '2112', 'value': 'PHBLp 202: Pharmaceutical Biology II'}, {'id': '2596', 'value': 'PHBLp 411: Pharmacognosy II'}, {'id': '2701', 'value': 'PHBLp 641: Phytochemistry I'}, {'id': '2111', 'value': 'PHBLt 202: Pharmaceutical Biology II'}, {'id': '2591', 'value': 'PHBLt 411: Pharmacognosy II'}, {'id': '2700', 'value': 'PHBLt 641: Phytochemistry I'}, {'id': '2935', 'value': 'PHBTp 1011: Public Health and Tropical Medicine'}, {'id': '2170', 'value': 'PHBTp 1022: Biotechnology II'}, {'id': '2594', 'value': 'PHBTp 402: Pharmaceutical Microbiology'}, {'id': '2934', 'value': 'PHBTt 1011: Public Health and Tropical Medicine'}, {'id': '2160', 'value': 'PHBTt 1022: Biotechnology II'}, {'id': '2588', 'value': 'PHBTt 402: Pharmaceutical Microbiology'}, {'id': '2770', 'value': 'PHCM 875: Pharmaceutical Chemistry IV'}, {'id': '2108', 'value': 'PHCMp 223: Pharmaceutical Analytical Chemistry'}, {'id': '2114', 'value': 'PHCMp 241: Physics for Pharmacy'}, {'id': '2593', 'value': 'PHCMp 433: Organic Pharmaceutical Chemistry  II'}, {'id': '2694', 'value': 'PHCMp 663: Instrumental analysis II'}, {'id': '2692', 'value': 'PHCMp 673: Pharmaceutical Chemistry II'}, {'id': '443', 'value': 'PHCMp 874: Pharmaceutical Chemistry IV'}, {'id': '2768', 'value': 'PHCMp 882: Drug Design'}, {'id': '2107', 'value': 'PHCMt 223: Pharmaceutical Analytical Chemistry'}, {'id': '2113', 'value': 'PHCMt 241: Physics for Pharmacy'}, {'id': '2587', 'value': 'PHCMt 433: Organic Pharmaceutical Chemistry  II'}, {'id': '2693', 'value': 'PHCMt 663: Instrumental analysis II'}, {'id': '2691', 'value': 'PHCMt 673: Pharmaceutical Chemistry II'}, {'id': '2757', 'value': 'PHCMt 881: Drug Design'}, {'id': '2950', 'value': 'PHMK 1211: Strategic Marketing'}, {'id': '2951', 'value': 'PHMK 1212: Brand Management'}, {'id': '2952', 'value': 'PHMK 1213: Pharma AI and Digital Marketing'}, {'id': '2953', 'value': 'PHMK 1214: Graduation Project'}, {'id': '2590', 'value': 'PHMU 403: Pathology and Histology'}, {'id': '2496', 'value': 'PHMUp 202: Physiology and Anatomy I'}, {'id': '2493', 'value': 'PHMUt 202: Physiology and Anatomy I'}, {'id': '2785', 'value': 'PHPE 1201: Health Technology Assessment  and Market Access'}, {'id': '2786', 'value': 'PHPE 1202: Pharmacoeconomics Modelling'}, {'id': '2787', 'value': 'PHPE 1203: Strategic Management in Pharmaceutical industry'}, {'id': '2788', 'value': 'PHPE 1204: Special Topics and Seminars in Pharmacoeconomics'}, {'id': '2666', 'value': 'PHRT 101: Selected Advanced Specialized Research Techniques'}, {'id': '2495', 'value': 'PHTC 203: History of Pharmacy & Biotechnology'}, {'id': '2707', 'value': 'PHTC 622: Legislation and Pharmacy Laws'}, {'id': '2777', 'value': 'PHTC 842: Quality Control, Quality Assurance and GMP'}, {'id': '2595', 'value': 'PHTCp 412: Pharmaceutics II'}, {'id': '2772', 'value': 'PHTCp 833: Pharmaceutical Technology I'}, {'id': '446', 'value': 'PHTCp 833: Pharmaceutical Technology II'}, {'id': '2589', 'value': 'PHTCt 412: Pharmaceutics II'}, {'id': '2771', 'value': 'PHTCt 833: Pharmaceutical Technology I'}, {'id': '445', 'value': 'PHTCt 833: Pharmaceutical Technology II'}, {'id': '2665', 'value': 'PHTP 101: Selected Advanced Specialized Topics'}, {'id': '2494', 'value': 'PHTX 201: Pharmaceutical & Medical Terminology'}, {'id': '2702', 'value': 'PHTX 680: Pharmaceutical Marketing and Entrepreneurship'}, {'id': '2769', 'value': 'PHTX 823: Pharmacology III'}, {'id': '447', 'value': 'PHTX 831: Toxicology I'}, {'id': '2704', 'value': 'PHTXp 612: Pharmacology I'}, {'id': '2774', 'value': 'PHTXp 834: Toxicology'}, {'id': '2703', 'value': 'PHTXt 612: Pharmacology I'}, {'id': '2773', 'value': 'PHTXt 834: Toxicology'}, {'id': '450', 'value': 'PHYS 202: Physics II'}, {'id': '1534', 'value': 'PR 1001: Advanced Design Project II'}, {'id': '2381', 'value': 'PRIN 601: Private International Law (Nationality and Status of Foreigners)'}, {'id': '2323', 'value': 'PUIN 402: Public International Law'}, {'id': '2481', 'value': 'PUL 801: Tax Law'}, {'id': '34', 'value': 'RPW 401: Research Paper Writing (A2)'}, {'id': '72', 'value': 'RSMD 901: Research Methodology'}, {'id': '16', 'value': 'SM 101: Scientific Methods (A1)'}, {'id': '3030', 'value': 'STAT 901: Applied  Statistics'}, {'id': '1884', 'value': 'STRA 1901: Strategic Management'}, {'id': '69', 'value': 'STRA 701: Corporate Renewal / Change Management'}, {'id': '106', 'value': 'STRA 702: Business Dynamics'}, {'id': '107', 'value': 'STRA 703: Strategic Management'}, {'id': '70', 'value': 'STRA 704: Strategic Management Analysis'}, {'id': '218', 'value': 'STRA 705: Strategic Management Seminar'}, {'id': '1947', 'value': 'STRA 713: Strategic Analysis'}, {'id': '1949', 'value': 'STRA 714: Strategic Decision Making'}, {'id': '270', 'value': 'STRA 720: Seminar in Strategic Management (1st Major seminar)'}, {'id': '1587', 'value': 'STRA 801: Strategic Management Thesis'}, {'id': '503', 'value': 'STRA 820: Seminar in Strategic Management (2nd Major seminar)'}, {'id': '1422', 'value': 'TH 1001: Design and Organization'}, {'id': '1436', 'value': 'TH 1002: Design and Economy'}, {'id': '1015', 'value': 'TH 201: Cultural History'}, {'id': '1026', 'value': 'TH 401: Design Theory'}, {'id': '1191', 'value': 'TH 402: Design and Sciences'}, {'id': '838', 'value': 'TH 601: Communication Theory (Basic)'}, {'id': '1203', 'value': 'TH 603: Culture Theory'}, {'id': '1632', 'value': 'UP 601: Theory of Urban Planning'}, {'id': '1633', 'value': 'UP 602: Housing'}, {'id': '1634', 'value': 'UP 603: Introduction to Urban Sociology'}], tas = [{'id': '2560', 'value': 'Abbas Abdelkarim Yehia'}, {'id': '3064', 'value': 'Abdel Megid Mahmoud Allam'}, {'id': '6851', 'value': 'Abdel Rahman Hatem ElSayed Mansour ElBarshoumy'}, {'id': '9199', 'value': 'Abdelaziz Mahmoud Awadallah Ali Hassan Gohar'}, {'id': '8891', 'value': 'Abdelrahman  Nagy Bakr Mohamed Ramdan Halawa '}, {'id': '6416', 'value': 'Abdelrahman Islam Ahmed Mahmoud'}, {'id': '9171', 'value': 'Abdelrahman Khaled Ali Asran Ahmed'}, {'id': '9188', 'value': 'Abdelrahman Mohamed Nabil Eldesoky Ali Shoman'}, {'id': '8283', 'value': 'Abdelrahman Salah Salama Mohamed Ghoneim'}, {'id': '9399', 'value': 'Abdullah Mahmoud Khodary Farag'}, {'id': '4050', 'value': 'Abeer Ahmed Elshahed'}, {'id': '5630', 'value': 'Abeer Samir Khalifa'}, {'id': '5968', 'value': 'Abla Mohamed Abdelnaby'}, {'id': '2401', 'value': 'Adel Ahmed Naiem'}, {'id': '4060', 'value': 'Adel Kamel Farag'}, {'id': '8096', 'value': 'Admir . Jukanovic'}, {'id': '8572', 'value': 'Aesha  Mohammed Tawfik Adam  Mohammed '}, {'id': '9259', 'value': 'Afaf Saied Shehata Mustafa Darwish'}, {'id': '7620', 'value': 'Aghathon Mories Bekhit'}, {'id': '4480', 'value': 'Agnieszka . Michalczyk'}, {'id': '9258', 'value': 'Ahmad  Fathy Abdelkhalek  Morsy'}, {'id': '9185', 'value': 'Ahmad Haytham Omar Aboelshwarb Mohamed Ragheb'}, {'id': '7220', 'value': 'Ahmad Mohammad Mohammad Mohammad Helmy'}, {'id': '9085', 'value': 'Ahmed  Alaa Eldin Ismail  Mohamed Fawzy Elansary'}, {'id': '8528', 'value': 'Ahmed  Maged Abdelhady Mohamed  Hashim '}, {'id': '8922', 'value': 'Ahmed  Mamdoh Ahmed Antar Sayed Ahmed'}, {'id': '9108', 'value': 'Ahmed  Mohammed Mahmoud Mohammed Ossairy'}, {'id': '8941', 'value': 'Ahmed  Sameh Alshabrawi  Ahmed'}, {'id': '8169', 'value': 'Ahmed Abd El-Hamid Mohamed Shafik Maarouf'}, {'id': '7670', 'value': 'Ahmed Abd Elreheem Tawfik'}, {'id': '6540', 'value': 'Ahmed Ali Abdelhai Ibrahim Dayhom'}, {'id': '8626', 'value': 'Ahmed Ali Abdelkader Abdalla Elkahwagy'}, {'id': '2334', 'value': 'Ahmed Amin Mohamed'}, {'id': '3952', 'value': 'Ahmed El Moneer'}, {'id': '5406', 'value': 'Ahmed Elsayed  Elhozayen'}, {'id': '3511', 'value': 'Ahmed Elsayed Elmahdy'}, {'id': '3270', 'value': 'Ahmed El-Sayed Wahby'}, {'id': '6545', 'value': 'Ahmed Fathy Khalifah'}, {'id': '9386', 'value': 'Ahmed Gamil Mohamed Ahmed'}, {'id': '9112', 'value': 'Ahmed Ibrahim Ahmed Othman'}, {'id': '8023', 'value': 'Ahmed Khairy Ahmed Abd Elhamid'}, {'id': '5512', 'value': 'Ahmed Maher Eltair'}, {'id': '134', 'value': 'Ahmed Metwali Abdel Aziz'}, {'id': '4466', 'value': 'Ahmed Mohamed Ali Naiel'}, {'id': '8244', 'value': 'Ahmed Mohammed Hassan'}, {'id': '8659', 'value': 'Ahmed Mohammed Hassan Abdelfattah'}, {'id': '9230', 'value': 'Ahmed Nagy Bakr Mohamed Ramadan'}, {'id': '7283', 'value': 'Ahmed Sameeh Abdelrady Hassan'}, {'id': '8833', 'value': 'Ahmed Samy Mohamed Fahmy Ahmed Ouf'}, {'id': '8969', 'value': 'Ahmed Soliman Abdelhamid Soliman Elsherbiny'}, {'id': '8252', 'value': 'Ahmed Walid Mahmoud Heussin El-Borgy'}, {'id': '6127', 'value': 'Ahmed Yehia Shash'}, {'id': '4340', 'value': 'Aida Nagy Hassan'}, {'id': '8860', 'value': 'Alaa Ahmed Masoud Mohamed Mohamed'}, {'id': '8879', 'value': 'Alaa Amr Mohamed Abdelazzem Mohamed'}, {'id': '6866', 'value': 'Alaa Gamal ElDin Mohamed Saeed Baligh'}, {'id': '5536', 'value': 'Alaa Mahmoud Selim'}, {'id': '4861', 'value': 'Alaa Mohamed Darwish'}, {'id': '2836', 'value': 'Alaa Mohamed Elanssary'}, {'id': '8864', 'value': 'Alaa Mohamed Sobeih Mohamed Eid '}, {'id': '9029', 'value': 'Alaa Tarek Khalil'}, {'id': '8968', 'value': 'Alberta . Carandente'}, {'id': '8506', 'value': 'Ali  Midhat Ali Elsayed  Suleiman '}, {'id': '9385', 'value': 'Ali Ahmed Ibrahim Abdelghafar'}, {'id': '9407', 'value': 'Ali Tamer Ali Reda'}, {'id': '8238', 'value': 'Ali Tarek Abdelaleem Mohamed Gweely'}, {'id': '7921', 'value': 'Aliaa Adel Mohamed Ali Abouelhag'}, {'id': '2342', 'value': 'Aliaa Anis Taha'}, {'id': '5115', 'value': 'Aliaa Maged Kamal Mohamed'}, {'id': '7911', 'value': 'Aly Eldin Hazem Atef Mohamed Rezk Sakr'}, {'id': '6949', 'value': 'Alyaa Hegazy Abdelhamid Ahmed Hegazy'}, {'id': '5036', 'value': 'Alzahraa Ibrahim Abdelhady'}, {'id': '3325', 'value': 'Amal Ibrahim Ali'}, {'id': '5176', 'value': 'Amany Zakaria Abdelmoneim'}, {'id': '5478', 'value': 'Amina  Ehab  Sobhy'}, {'id': '3061', 'value': 'Amir Roushdy Abdelhameed'}, {'id': '8993', 'value': 'Amir Tadros Nabih Abdo'}, {'id': '8665', 'value': 'Amira Mahmoud Mohammed Mohammed Nematalla'}, {'id': '9282', 'value': 'Amira Tarek Ibrahim Metwaly Elnoamany'}, {'id': '9274', 'value': 'Ammar Yehia Abdoulwahab Kasem'}, {'id': '7160', 'value': 'Amna Youssef Ahmed Ramzy'}, {'id': '9067', 'value': 'Amr  Alaa Hassan Ibrahim Hassan'}, {'id': '5448', 'value': 'Amr  Maher Elnemr'}, {'id': '8944', 'value': 'Amr  Yasser Mohammed Hussein Ahmed Kasber'}, {'id': '121', 'value': 'Amr Abdallah Abou Shousha'}, {'id': '8253', 'value': 'Amr Kais Elrai Attia'}, {'id': '9217', 'value': 'Amr Magdy Hassan Mahmoud Ayoub'}, {'id': '2468', 'value': 'Amr Talaat Abdel-Hamid'}, {'id': '9203', 'value': 'Amro Abdullah  Nachef'}, {'id': '7407', 'value': 'Andrew Medhat Guirguis Faried'}, {'id': '95', 'value': 'Anja . Bendtschneider'}, {'id': '165', 'value': 'Anke . Klingner'}, {'id': '41', 'value': 'Anne Abdel Moneim Hassan'}, {'id': '8167', 'value': 'Antonio Rodriguez Andres'}, {'id': '7400', 'value': 'Arig mahmoud Ahmed Farag Eweida'}, {'id': '8931', 'value': 'Arwa  Kadri Aly  Abdelmageed '}, {'id': '6917', 'value': 'Ashraf Gaber Sayed Morsy'}, {'id': '7990', 'value': 'Ashraf Osam Ahmed Elshamy'}, {'id': '8181', 'value': 'Asmaa  Mahmoud Abdallah Saleh'}, {'id': '8846', 'value': 'Asmaa Mohammed Abdelsalam Abas Eltaweel'}, {'id': '6499', 'value': 'Asmaa Mostafa Hussien Abdelkhalek'}, {'id': '3049', 'value': 'Atef Afifi Mohamed'}, {'id': '9273', 'value': 'Aya  Salama Abdelhady Mohamed'}, {'id': '8200', 'value': 'Aya Alaa Eldin Hassan Hassan Saleh'}, {'id': '8302', 'value': 'Aya Amir Mohamed Mousa Sharaf'}, {'id': '6902', 'value': 'Aya Ashraf Ezzat Ali Ismail'}, {'id': '9161', 'value': 'Aya Ayman Mohamed Kamal Anwar'}, {'id': '8939', 'value': 'Aya Hesham Helmy Abdelhalim'}, {'id': '7688', 'value': 'Aya Hesham Soliman Mohamed Refaat'}, {'id': '5518', 'value': 'Aya Mohammed Ashraf Shawkat'}, {'id': '8625', 'value': 'Aya Moustpha Fathi Hussein Negm'}, {'id': '8629', 'value': 'Aya Muhammad Attia Ahmad Elnahas'}, {'id': '9187', 'value': 'Aya Tamer Shawky Ibrahim'}, {'id': '6920', 'value': 'Aya Yasser Mohamed Hussein Ahmed Kasber'}, {'id': '4056', 'value': 'Aya Zakaria Rashed'}, {'id': '8371', 'value': 'Ayat  Osama Said Abdelsalam Abdallah'}, {'id': '7205', 'value': 'Ayatallah Ahmed Hamdy Mohammed Abd Al-Aziz Hashem'}, {'id': '8741', 'value': 'Ayham Marwan Mohamed Saleh Dalal'}, {'id': '784', 'value': 'Ayman Aly Elbadawy'}, {'id': '5442', 'value': 'Ayman Hamdy Nassar'}, {'id': '8174', 'value': 'Ayman Mounir  Alserafi'}, {'id': '7354', 'value': 'Ayman Raafat Ismail Elgndy'}, {'id': '3157', 'value': 'Bakr Mohamed Rabeeh'}, {'id': '9281', 'value': 'Basel Ahmed Abdelraouf Ahmed  Elkarety'}, {'id': '8512', 'value': 'Basma Elsayed Ibrahim Ahmed Elghobashy '}, {'id': '4446', 'value': 'Basma Mohamed  El-Shenawy'}, {'id': '7949', 'value': 'Basma Mohamed Afifi Ibrahim'}, {'id': '9409', 'value': 'Bassant  Essam Mahmoud Ali  Elnaggar '}, {'id': '9300', 'value': 'Bassant Adel Hamza Ahmed'}, {'id': '7619', 'value': 'Bassant Ahmed Mohamed Abd Elsamiea Keshta'}, {'id': '5099', 'value': 'Bassant Mostafa Salah'}, {'id': '6771', 'value': 'Carina Mylene Beyer'}, {'id': '7734', 'value': 'Carol Tarek Farouk Doos'}, {'id': '6413', 'value': 'Catherine Malak Noshy Ibrahim Elias'}, {'id': '7079', 'value': 'Christian . Schubert'}, {'id': '7558', 'value': 'Christian Bernhard Schmitt'}, {'id': '6898', 'value': 'Christine Adel Sedky Youssef'}, {'id': '9301', 'value': 'Claudine Hesham Lamei Efram'}, {'id': '49', 'value': 'Dahlia Hassan Sennara'}, {'id': '8509', 'value': 'Dalia Abdelwahab Hussien Abdelwahab'}, {'id': '6059', 'value': 'Dalia Akram Khouzam'}, {'id': '7210', 'value': 'Dalia Mamdouh Mahfouz Ali Ibrahim'}, {'id': '4038', 'value': 'Dalia Sherif Elhelw'}, {'id': '8516', 'value': 'Dalia Walid Mohamed Saeed Mohamed Abdallah Hewedy'}, {'id': '4113', 'value': 'Danira Ashraf Habashy'}, {'id': '3273', 'value': 'Darius Paul Zlotos'}, {'id': '2609', 'value': 'Diana Mostafa Mohamed'}, {'id': '5825', 'value': 'Dieter . Fritsch'}, {'id': '7892', 'value': 'Dima Ghassan Tannir'}, {'id': '9277', 'value': 'Dina  Mohamed Saad  Mohamed'}, {'id': '5551', 'value': 'Dina Hady Aboushady'}, {'id': '5514', 'value': 'Dina Ibrahim Ahmed'}, {'id': '8853', 'value': 'Dina Mohamed Sherif Ali'}, {'id': '3193', 'value': 'Dina Mohamed Yousri'}, {'id': '8517', 'value': 'Dina Reda Abdelhay Mohamed Eldamak'}, {'id': '8884', 'value': 'Dina Waref Lamei  Shalaby'}, {'id': '8978', 'value': 'Doaa  Elsaid Hassan  Mohamed '}, {'id': '2546', 'value': 'Doaa Taha El-Shihi'}, {'id': '5149', 'value': 'Dominique . Mauri'}, {'id': '8530', 'value': 'Donia  Hisham Mohamed  Elnaggar '}, {'id': '4631', 'value': 'Donia Mohamed Eyad'}, {'id': '2733', 'value': 'Ehab Ahmed Yaseen'}, {'id': '3533', 'value': 'Ehab Kamel Abou-Elkheir'}, {'id': '6538', 'value': 'Ehab Magdy Salah Noureldin'}, {'id': '3155', 'value': 'El-Sayed Ibrahim Morgan'}, {'id': '6764', 'value': 'Eman Ahmed Farouk Mahmoud Hassan'}, {'id': '4501', 'value': 'Eman Ahmed Hafez'}, {'id': '6240', 'value': 'Eman Ahmed Hamdy Azab'}, {'id': '8628', 'value': 'Eman Fikri Hassan Hassan Elezabi'}, {'id': '144', 'value': 'Engy Mohamed El Sawaf'}, {'id': '4474', 'value': 'Erwin walter Herzberger'}, {'id': '8561', 'value': 'Eslam Sabry Abdelrahim Mahmoud Hegazy'}, {'id': '5055', 'value': 'Esraa Abdelraouf Soliman'}, {'id': '9252', 'value': 'Esraa Ahmed Elmetwally Elshawaf'}, {'id': '8595', 'value': 'Esraa Mahmoud Abdullah Ahmed Ewies'}, {'id': '9302', 'value': 'Esraa Mohamed Gibreen Elsayed'}, {'id': '7954', 'value': 'Esraa Nasif Wagdy Mohamed Mobasher'}, {'id': '8309', 'value': 'Esraa Nasser Fawzy Mohamed Ezzat'}, {'id': '8663', 'value': 'Esraa Wael Saeed  Farag '}, {'id': '9197', 'value': 'Essraa Essam Mahmoud Ali'}, {'id': '7217', 'value': 'Ethar Amr Abdelghany Abdelfattah'}, {'id': '8201', 'value': 'Eyad  Mohammed Farhan  Husni'}, {'id': '8603', 'value': 'Eyad Mamdouh Gaber Mohamed Ayad'}, {'id': '8061', 'value': 'Ezzeldin Sobhi Metwalli Ali'}, {'id': '2606', 'value': 'Fadwa Farouk Foda'}, {'id': '6905', 'value': 'Farah Ahmed Ahmed Shafik Ahmed Soliman'}, {'id': '8554', 'value': 'Farah Ahmed Yousry Shams Aldin Kamal Ashraf'}, {'id': '8910', 'value': 'Farah khaled Mahmoud Moktar Ismail Kamaly'}, {'id': '8830', 'value': 'Farah Mohamed Hussien Mohamed Abdelalem'}, {'id': '9097', 'value': 'Farah Mohammed Mamdouh Mostafa Kamel Eldegheidy'}, {'id': '8900', 'value': 'Farah Saadeldin Nabih Ibrahim Mohamed'}, {'id': '9163', 'value': 'Farah Tarek Amin Samy Rofail'}, {'id': '8983', 'value': 'Farah Yasser Hassan Hodhod'}, {'id': '9314', 'value': 'Fares  Mohamed Aboelfotouh Metwaly Selim'}, {'id': '8427', 'value': 'Farida  Hassan Ahmed Mostafa  Elnaggar '}, {'id': '8551', 'value': 'Farida  Kamel Salah Eldin Kamel Shahin '}, {'id': '9160', 'value': 'Farida Ashraf Mohamed Rafik Mohamed Abdelbaset Selim'}, {'id': '8869', 'value': 'Farida Hassan Mohamed Ahmed Moubarak'}, {'id': '8909', 'value': 'Farida Helmy Abdelhalim Eldessouky'}, {'id': '8410', 'value': 'Farida Mahmoud Yahia Mahmoud Elhusseiny'}, {'id': '9240', 'value': 'Farida Samer Ahmed  Eldesouky'}, {'id': '8733', 'value': 'Farida Waleed WaheedEldin Abdelhamid'}, {'id': '2808', 'value': 'Fathy Saad Helail'}, {'id': '8573', 'value': 'Fatima  Alaaeldin Kamal Mohamed  Thabet '}, {'id': '8546', 'value': 'Fatma Alaa Eldin Ali Fahmy Raid'}, {'id': '8851', 'value': 'Fatma Hassan Mohamed Mohamed Mohamed Hamed'}, {'id': '8959', 'value': 'Fatma Mohamed Ali Radwan'}, {'id': '9313', 'value': 'Fatma Mohamed Ibrahim Farag'}, {'id': '9239', 'value': 'Fatma Sherif Hussein Hassan Mansour'}, {'id': '4004', 'value': 'Florian Becker Ritterspach'}, {'id': '34', 'value': 'Frank . Gunzer'}, {'id': '2457', 'value': 'Gailan Yehya Radwan'}, {'id': '4519', 'value': 'Gamal Abdel Shafy'}, {'id': '6607', 'value': 'Gamal Abdelhameed Mohamed Kassem'}, {'id': '4155', 'value': 'Gamal Mohamed Shehata'}, {'id': '9165', 'value': 'Gehan Reda Abdallah Hamed Ali'}, {'id': '6421', 'value': 'George Gadallah Yakout Gadallah'}, {'id': '8749', 'value': 'George Sameh Fahim Abdelsayed'}, {'id': '6638', 'value': 'Ghada Salaheldin Ali Sarhan'}, {'id': '9117', 'value': 'Ghada Sayed Mohamed Sayed Ghazala'}, {'id': '7299', 'value': 'Ghadir Mostafa mahmoud Mangood Dogim'}, {'id': '4822', 'value': 'Gilan Hamdi Hussein'}, {'id': '7933', 'value': 'Gorgena Asaad Anwar Kila'}, {'id': '6948', 'value': 'Gwendolyn . Kulick'}, {'id': '8233', 'value': 'Habiba Gamal Aboel Fotouh Mohamed Mohamed'}, {'id': '9261', 'value': 'Habiba Magdy Rashad Saadeldin Eldabaa'}, {'id': '8934', 'value': 'Habiba Mahdy Mohamed Flelfel'}, {'id': '5590', 'value': 'Habiba Mahmoud Shawkat'}, {'id': '8839', 'value': 'Habiba Mohamed Hamdy Mohamed Elsayed'}, {'id': '7239', 'value': 'Hadeel Adel Abbas  Mostafa'}, {'id': '3236', 'value': 'Hadeer Emad Shawky Hammad'}, {'id': '5150', 'value': 'Hadeer Mounir Ahmed'}, {'id': '7673', 'value': 'Hadwa Hassan Aziz  Pasha'}, {'id': '9205', 'value': 'Hagar Amin Ezzeldin Elmaghraby'}, {'id': '3237', 'value': 'Hagar Samir Adib Said'}, {'id': '8592', 'value': 'Hager Khaled Ali Muhamed Abuelyazid'}, {'id': '8404', 'value': 'Haidy  Ashraf Mostafa Kamal  Aziz '}, {'id': '6234', 'value': 'Haitham Abdelsalam Ahmed Omran'}, {'id': '2516', 'value': 'Hala Magdy Gaber'}, {'id': '799', 'value': 'Hamdy Abdallah Kandil'}, {'id': '8906', 'value': 'Hams  Khaled Farouk  Elhefnawy'}, {'id': '8831', 'value': 'Hams Wael Elgaraihy Mahmoud Dorgham'}, {'id': '9296', 'value': 'Hana  Tarek Mahmoud Ahmed  elsayed'}, {'id': '6807', 'value': 'Hana Medhat Abdelfattah Aly Sharabash'}, {'id': '9072', 'value': 'Hana Moemen Mohamed  Samy Abdelrahman'}, {'id': '9051', 'value': 'Hana Nabil Ibrahim Hussein'}, {'id': '7759', 'value': 'Hana Youssef Madbouly Ibrahim Issa'}, {'id': '9231', 'value': 'Haneen Ahmed Riad Elbatroukh'}, {'id': '8570', 'value': 'Hania  Wael Abdelrehim Mohamed Hassan  Elhoshy '}, {'id': '7969', 'value': 'Hania Waleed Tawfik Aly Elfil'}, {'id': '171', 'value': 'Hans-Georg . Breitinger'}, {'id': '3154', 'value': 'Hany Ahmed Ismail'}, {'id': '495', 'value': 'Hany Fathy Hammad'}, {'id': '69', 'value': 'Hany Mostafa El Sharkawy'}, {'id': '3644', 'value': 'Hassan . Ouda'}, {'id': '8872', 'value': 'Haya Mohamed Olabi'}, {'id': '23', 'value': 'Haytham Osman Ismail'}, {'id': '8961', 'value': 'Heba Abdelmohsen Mohamed  Basha '}, {'id': '5262', 'value': 'Heba Abdullah Mohamed  El Dahshan '}, {'id': '8932', 'value': 'Heba Awny Abdelhamid Ismail'}, {'id': '6507', 'value': 'Heba Ehab Mohamed Elnakib'}, {'id': '425', 'value': 'Heba El Sayed Handoussa'}, {'id': '5997', 'value': 'Heba Ezzat Dewedar'}, {'id': '8650', 'value': 'Heba Hesham Abdulhaleem  Mostafa'}, {'id': '7714', 'value': 'Heba Medhat Abdel Moaz Hegazy'}, {'id': '8211', 'value': 'Heba Mohamed Abdelwahab Mohamed'}, {'id': '6061', 'value': 'Heba Mohammed Nafea'}, {'id': '6275', 'value': 'Heba Sabry Mohamadin  Qenawy'}, {'id': '8923', 'value': 'Hebaallah  Samir Tohamy Eid  Amin'}, {'id': '87', 'value': 'Hebatallah Abdel Fattah Ghoneim'}, {'id': '9228', 'value': 'Hebatallah Gamal Abdelnasser Mohamed  Kenawy'}, {'id': '7111', 'value': 'Hebatullah Ashraf Alieldin Ibrahim Megahed'}, {'id': '7242', 'value': 'Hebatullah Hamdy Mohamed Saeed Elsharawy'}, {'id': '3426', 'value': 'Hebatullah Hamed Mohamed'}, {'id': '5289', 'value': 'Hebatullah Samir El Gamal'}, {'id': '8499', 'value': 'Hedaya Hamdi Abdelsattar Mohamed ElDamanhory'}, {'id': '4865', 'value': 'Heidi Moahmed Badr'}, {'id': '6065', 'value': 'Hend Abdalla Zaghloul'}, {'id': '8560', 'value': 'Hend Adham Hamed Yousef'}, {'id': '3435', 'value': 'Hend Mohamed Saber'}, {'id': '7977', 'value': 'Hesham Ahmed Mahmoud Mostafa Hegazi'}, {'id': '6869', 'value': 'Hesham Hamed Ahmed Ibrahim'}, {'id': '6923', 'value': 'Hesham Sherif Elsayed Hussein'}, {'id': '4802', 'value': 'Hisham Hassaballah Othman'}, {'id': '2451', 'value': 'Hisham Mostafa El Sherif'}, {'id': '8073', 'value': 'Hisham Sherif Gabr'}, {'id': '150', 'value': 'Hoda Moustafa El Henaoui'}, {'id': '8729', 'value': 'Hossam Eldin Hassan Abdelmunim'}, {'id': '7324', 'value': 'Husam Riad  Husain'}, {'id': '5467', 'value': 'Hussam Hussein Salama'}, {'id': '9235', 'value': 'Hussein  Ashraf Hussein  Sallam'}, {'id': '115', 'value': 'Hussein Raafat Nabil Raafat'}, {'id': '251', 'value': 'Ihab . Nadim'}, {'id': '7836', 'value': 'Iman Mostafa Ibrahim El-Batouty'}, {'id': '2435', 'value': 'Inas Esmat Ezz'}, {'id': '9178', 'value': 'Ingy Ashraf Herzi Rezkalla Fam'}, {'id': '145', 'value': 'Ingy Mohamed Abou-Zeid'}, {'id': '9380', 'value': 'Irina . Goryacheva'}, {'id': '8728', 'value': 'Islam Ahmed Mahmoud Elmaddah'}, {'id': '7408', 'value': 'Islam Saher Elsayed Fahmy  Hegazy'}, {'id': '2389', 'value': 'Islam Tariq Eddiasty'}, {'id': '8254', 'value': 'Jane Fayez Abdou Marzouk'}, {'id': '9324', 'value': 'Janne Tuomas Terasvirta'}, {'id': '8966', 'value': 'Jano Baher Boulos Abdo El Swefy'}, {'id': '8285', 'value': 'Jasmin Abdel Moneim Wardani'}, {'id': '8867', 'value': 'Jessica Magdy Gergis Haleem '}, {'id': '7760', 'value': 'Joy Samuel Labib Maher'}, {'id': '9153', 'value': 'Jutta Bernadette Wacht'}, {'id': '8420', 'value': 'Kareem  Elsayed Ahmed Abdelrahman'}, {'id': '9183', 'value': 'Kareem Ahmed Mohamed Mahmoud Hindi'}, {'id': '8841', 'value': 'Karen Magdy Anwar Ayoub Girgis'}, {'id': '9191', 'value': 'Karim Ahmed Abdelaziz Ahmed Mahmoud'}, {'id': '9382', 'value': 'Karim Hassan Mohamed Soliman Maaly'}, {'id': '8870', 'value': 'Karim Mohamed Elsayed Abdelsalam'}, {'id': '9263', 'value': 'Karim Mohamed Ragab Hamed'}, {'id': '9241', 'value': 'Karim Walid Mohamed Mohamed Mahmoud Hussenin'}, {'id': '8376', 'value': 'Kathryn  Victoria Best'}, {'id': '9227', 'value': 'Khaled  Tarek Mohamed Mohamed Kassem'}, {'id': '8848', 'value': 'Khaled Ahmed Fawzy Mohamed Salem'}, {'id': '84', 'value': 'Khaled Mohamed Abou Aisha'}, {'id': '9436', 'value': 'Khaled Mohamed Ibrahim Eladawy Nowara'}, {'id': '6460', 'value': 'Kholoud Abdelaziz Mahmoud Abdelrehim  Ahmed'}, {'id': '7474', 'value': 'Ksenia Georgieu Nikolskaya'}, {'id': '9216', 'value': 'Laila Ahmed Osman  Gohar'}, {'id': '7713', 'value': 'Laila Sherif Hamdy Bahloul'}, {'id': '3209', 'value': 'Lamia Ahmed Shihata'}, {'id': '8767', 'value': 'Lamiaa Mohamed Hamed Ahmed Eltantawi'}, {'id': '6822', 'value': 'Lise Abdel Masih Fakhri Aziz'}, {'id': '6066', 'value': 'Liza Samir Botros'}, {'id': '133', 'value': 'Lobna Abdel Aal Kassem'}, {'id': '6891', 'value': 'Lobna Ali Onsi Abdel Salam Mohamed'}, {'id': '5503', 'value': 'Lobna Mohamed Abdelrauf'}, {'id': '8970', 'value': 'Luis Pedro Costa Gomes'}, {'id': '8439', 'value': 'Luna  Ahmed Osman Mohamed  Elhifni '}, {'id': '8294', 'value': 'Magdi Elsayed Mohamed Mohamed Mostafa'}, {'id': '3766', 'value': 'Maggie Ahmed Ezzat Mashaly'}, {'id': '6423', 'value': 'Maggie Ihab Maher Shafik  Shamma '}, {'id': '5509', 'value': 'Magy Maged Wadea'}, {'id': '9417', 'value': 'Maha Aboubakr Ibrahim Ibrahim'}, {'id': '7256', 'value': 'Maha Ahmed Hassan  Shawki'}, {'id': '3494', 'value': 'Maha Ayman Hamdy'}, {'id': '332', 'value': 'Maha Gamal El-Din Ahmed'}, {'id': '6396', 'value': 'Maha Hesham Elsayed  Elfeshawy'}, {'id': '9307', 'value': 'Maha Saeed Helmy Ahmed Mohamed'}, {'id': '8220', 'value': 'Maher Mohammed Abo Elftoh'}, {'id': '8536', 'value': 'Mahinar Mohamed Ahmed Reda Mohamed'}, {'id': '7740', 'value': 'Mahmoud Ahmed Mahmoud Mabrouk Ahmed'}, {'id': '4884', 'value': 'Mahmoud El Khafif'}, {'id': '4482', 'value': 'Mahmoud Ibrahim Khalil'}, {'id': '8594', 'value': 'Mahmoud Saad Rabie Bayoomi Hamza'}, {'id': '8433', 'value': 'Mai  Mohamed Tarek Abdelaziz Mohamed Ghannoum '}, {'id': '7671', 'value': 'Mai Adel Mohamed Ibrahim'}, {'id': '8951', 'value': 'Mai El Motasem Hussein Kamel Masoud'}, {'id': '5777', 'value': 'Mai Magdy Mohamed Sleim'}, {'id': '9206', 'value': 'Mai Mahmoud Ahmed Abdeldayem'}, {'id': '7900', 'value': 'Mai Medhat Mahmoud Momtaz'}, {'id': '9200', 'value': 'Mai Mohsen Tawfik Kamel Gaweesh'}, {'id': '8269', 'value': 'Maiada Magdy Abdel Majeed'}, {'id': '8218', 'value': 'Manar Hatem Elmansy  Mohamed'}, {'id': '6747', 'value': 'Manar Karam Moawwad Aly'}, {'id': '74', 'value': 'Manar Mahmoud Soliman'}, {'id': '8597', 'value': 'Manlio . Michieletto'}, {'id': '8835', 'value': 'Maria Sherif Gamal  Reiad'}, {'id': '9202', 'value': 'Mariam  Ahmed Helmy Mahmoud'}, {'id': '8963', 'value': 'Mariam  Ayman Moustafa  Abdelrahman'}, {'id': '9303', 'value': 'Mariam  Khaled Mohamed  Aboelnaga Darwesh'}, {'id': '9245', 'value': 'Mariam  Mahmoud Mostafa  Elsharnouby'}, {'id': '8522', 'value': 'Mariam  Mohamed  Nabil '}, {'id': '8568', 'value': 'Mariam  Mohamed Abdelhakem Mahmoud  Mohamed '}, {'id': '8990', 'value': 'Mariam  Tamer Mohamed  Hamdy sabry Elshakankiry'}, {'id': '9214', 'value': 'Mariam  Yehia Mahmoud  Abobasha'}, {'id': '8507', 'value': 'Mariam Ahmed Adel Mahmoud  Elemary '}, {'id': '6895', 'value': 'Mariam Ahmed Mohamed Azab'}, {'id': '8954', 'value': 'Mariam Ashraf Abdalla El Sebaie'}, {'id': '8875', 'value': 'Mariam Ayman Mamdoh Abdelmonem  Eloraby'}, {'id': '8863', 'value': 'Mariam Ehab Azmy  Malak '}, {'id': '4990', 'value': 'Mariam Hussein Madkour'}, {'id': '8578', 'value': 'Mariam Mohamed Elsayed Ibrahim Salem'}, {'id': '8180', 'value': 'Mariam Mohamed Halim Sabri Mostafa Sabri'}, {'id': '8888', 'value': 'Mariam Mohamed Hamdy Eissa '}, {'id': '7695', 'value': 'Mariam Morgan Kamel Morgan'}, {'id': '8894', 'value': 'Mariam Wael Talaat  Sayed'}, {'id': '6503', 'value': 'Marian Magdy Ramsis Tadros'}, {'id': '9114', 'value': 'Marina Ehab Youssef Ghaly'}, {'id': '5535', 'value': 'Marina Sherif Beshay'}, {'id': '8101', 'value': 'Martina Yacoub Salides Yacoub Salides'}, {'id': '8212', 'value': 'Marwa  Mostafa Abdelazim  Khalil'}, {'id': '8213', 'value': 'Marwa Ali Abdelrafie Youssef Mohamed'}, {'id': '7163', 'value': 'Marwa Gamal Abdel Hamid Gadallah'}, {'id': '8856', 'value': 'Marwa Mahmoud Mohamed Mohamed Abla'}, {'id': '7060', 'value': 'Marwa Mohamed Abdel Halim'}, {'id': '8008', 'value': 'Marwa Muhammed Abbas Abdelmeguid'}, {'id': '7551', 'value': 'Marwa Shaaban Abdel Aziz Ahmed'}, {'id': '8602', 'value': 'Marwan Magdy Abdelaziz Emam Elbeialy'}, {'id': '9162', 'value': 'Marwan Tarek Sherif Almaraghi'}, {'id': '9242', 'value': 'Maryam Sayed Zayn Alabdeen Ali'}, {'id': '9106', 'value': 'Mayar Mohamed Abdalla Mohamed Tawfek'}, {'id': '7960', 'value': 'Mayar Osama Abdel-Satar Abdel-Wahab'}, {'id': '7640', 'value': 'Mayar Ossama Sami Foaad Reda'}, {'id': '7288', 'value': 'Mayar Waleed Salah Eldin Aly'}, {'id': '9299', 'value': 'Mayssa Abdelhamid Mahmoud Soliman'}, {'id': '2484', 'value': 'Menat Allah Mohamed Samir Darrag'}, {'id': '8660', 'value': 'Menatalla Ashraf Ragaey Azab Osman'}, {'id': '4489', 'value': 'Menatullah Mohsen Anwar'}, {'id': '6133', 'value': 'Menna Mohamed Ragab'}, {'id': '8881', 'value': 'Menna Mohamed Saeed Abdelaziz Bedir '}, {'id': '6813', 'value': 'Mennaallah Yasser elsayed elbadawy Abdelfadel Khalifa'}, {'id': '8548', 'value': 'Mennatallah  Hassan Saleh Abdo  Mohamed '}, {'id': '8531', 'value': 'Mennatallah Abdelnasser Mohamed Abdou  Abdou'}, {'id': '9329', 'value': 'Mennatallah Ahmed Aly Moussa Moussa'}, {'id': '9406', 'value': 'Mennatallah Ahmed Elsayed Zaki Abouelatta'}, {'id': '8275', 'value': 'Mennatallah Khaled Mohamed Gamal Ibrahim Helmy'}, {'id': '6514', 'value': 'Mennatallah Mohamed Ahmed Abdallah'}, {'id': '7246', 'value': 'Mennatallah Reyad Abdelazim Seliman'}, {'id': '7604', 'value': 'Mennatallah Wael Mahmoud Mohamed Abd Elrahman'}, {'id': '8928', 'value': 'Mennatullah Nasser Omar Ahmed'}, {'id': '8967', 'value': 'Mennatullah Sayed Kamal Sayed Mohamed Kamal'}, {'id': '6800', 'value': 'Menrit Hanna Mosaad Moawad'}, {'id': '6909', 'value': 'Merhan Mostafa ElSayed Mohamed'}, {'id': '8222', 'value': 'Meriam Ahmed Mahmoud Khalifa'}, {'id': '7279', 'value': 'Merihan Ali Ibrahim Ali Attia'}, {'id': '5683', 'value': 'Merna Hany Sabry'}, {'id': '7642', 'value': 'Merna Saleh Shokry Emam Elawamry'}, {'id': '8571', 'value': 'Merna Tamer Essam Eltersawy'}, {'id': '7196', 'value': 'Mervat Mustafa Fahmy Abuelkheir'}, {'id': '8060', 'value': 'Meryem Kübra Uluç Tolba'}, {'id': '6325', 'value': 'Michael . Eichner'}, {'id': '6832', 'value': 'Michael Emad George Moawad'}, {'id': '7899', 'value': 'Milad Michel Ghantous'}, {'id': '9280', 'value': 'Mina Maher Shawky Lazeem'}, {'id': '8908', 'value': 'Mina Sherif Mourice Abdelkodous Saeed Elnomeir'}, {'id': '3288', 'value': 'Minar Abbas El-Aasser'}, {'id': '9212', 'value': 'Minora Mohamed Abdelrahman Ibrahim Hafez'}, {'id': '7332', 'value': 'Mirna Bassem Emile Sadek'}, {'id': '6912', 'value': 'Mirna Maged Sobhi Andrawes'}, {'id': '6067', 'value': 'Mirna Victor Azmy'}, {'id': '9075', 'value': 'Moaaz Nasser Ibrahim Eldessouky Shaban'}, {'id': '9395', 'value': 'Moaz Ayman Aref Abdelghany'}, {'id': '9071', 'value': 'Mohab  Gehad Ibrahim Abdelrahman Ahmed '}, {'id': '8849', 'value': 'Mohab Assem Mohamed Elsayed Deyab'}, {'id': '9069', 'value': 'Mohamad . Dakak'}, {'id': '5161', 'value': 'Mohamad Hesham  Zamzam'}, {'id': '8887', 'value': 'Mohamed  Ahmed Mohamed Zaki Abdelgawad'}, {'id': '9073', 'value': 'Mohamed  Alaa Eldeen  Salah Ali'}, {'id': '7300', 'value': 'Mohamed  Magdy Mohamed Hassan Nafea'}, {'id': '6276', 'value': 'Mohamed  Mohamed Borhaneldin Gamal Hammad'}, {'id': '4145', 'value': 'Mohamed  Shawky Hafez'}, {'id': '6904', 'value': 'Mohamed Abdalhameed Mohamed Ahmed Soliman'}, {'id': '2491', 'value': 'Mohamed Abdel Ghany Ahmed Salem'}, {'id': '9247', 'value': 'Mohamed Abdelrahman Mahmoud Elrachidy'}, {'id': '9437', 'value': 'Mohamed Abdelraouf Ali Mohamed'}, {'id': '8926', 'value': 'Mohamed Adel mohamed Elshafei '}, {'id': '8857', 'value': 'Mohamed Ahmed Awaad Ibrahim'}, {'id': '2341', 'value': 'Mohamed Ahmed El-Azizi'}, {'id': '7384', 'value': 'Mohamed Ahmed Mohamed  Hussein'}, {'id': '8914', 'value': 'Mohamed Ahmed Salaheldin Mohamed Ibrahim'}, {'id': '9027', 'value': 'Mohamed Alaa El Din Ahmed El Sayed Aly  Iskandar El Assyouty'}, {'id': '8656', 'value': 'Mohamed Amr Anwar Mahmoud Elsheshtawy'}, {'id': '8539', 'value': 'Mohamed Ayman Abuelmagd Mohamed'}, {'id': '9204', 'value': 'Mohamed Ayman Fathy Hassan Ahmed'}, {'id': '2425', 'value': 'Mohamed Ehsan Ashour'}, {'id': '8575', 'value': 'Mohamed Elsayed Shaaban Ali Ali'}, {'id': '6040', 'value': 'Mohamed Elwi Mitwally'}, {'id': '6079', 'value': 'Mohamed Emam Mohamed'}, {'id': '8730', 'value': 'Mohamed Hamed Fahmy'}, {'id': '7383', 'value': 'Mohamed Hassan Solayman'}, {'id': '6658', 'value': 'Mohamed Hemieda Abdelaziz'}, {'id': '4135', 'value': 'Mohamed Kamel Gabr'}, {'id': '8274', 'value': 'Mohamed Magdy Hamed Elsayed Shalma'}, {'id': '7719', 'value': 'Mohamed Mahmoud Elsayed Bedier'}, {'id': '9179', 'value': 'Mohamed Medhat Adel Helal'}, {'id': '7774', 'value': 'Mohamed Mohamed El-Sayed Atteya'}, {'id': '9381', 'value': 'Mohamed Mohsen Ahmed Shehata'}, {'id': '9238', 'value': 'Mohamed Mohsen Mohy Eldin Mahmoud'}, {'id': '7755', 'value': 'Mohamed Okasha Abd Elaal'}, {'id': '9093', 'value': 'Mohamed Omar Saad Abdelwahab'}, {'id': '3453', 'value': 'Mohamed Saad Mohamed'}, {'id': '8171', 'value': 'Mohamed Salman Mohamed Salama'}, {'id': '6628', 'value': 'Mohamed Samir Midani'}, {'id': '8935', 'value': 'Mohamed Sherif Mohamed Samir Mohamed Ali'}, {'id': '9059', 'value': 'Mohamed Tarek Ahmed Mohamed Helmy'}, {'id': '6439', 'value': 'Mohamed Tarek Salah Eldin  Ahmed Abdelmawgoud'}, {'id': '9211', 'value': 'Mohamed Walid Elsayed Gomaa Abdelaziz'}, {'id': '2638', 'value': 'Mohamed Zakaria Gad'}, {'id': '519', 'value': 'Mohammad Abdel Halim Abdel Naby'}, {'id': '5734', 'value': 'Mohammed Abdel Megeed Salem'}, {'id': '3042', 'value': 'Mohammed Abdelkhalek Youseef'}, {'id': '3615', 'value': 'Mohammed Abdelmoomen Elshehawy'}, {'id': '9293', 'value': 'Mohammed Abdelraouf Abdeen'}, {'id': '3559', 'value': 'Mohammed Salama Abdelhady Mohamed'}, {'id': '7091', 'value': 'Mohsen Shokry Elshamaa'}, {'id': '8524', 'value': 'Moira Adel Abdelsalam Ali'}, {'id': '5792', 'value': 'Mona Abdelaziz Mohamed'}, {'id': '3640', 'value': 'Mona Abdelmoniem  Marie'}, {'id': '5281', 'value': 'Mona Abdelsalam Sayed hassan  Elbannan'}, {'id': '4436', 'value': 'Mona Ahmed Hassan'}, {'id': '445', 'value': 'Mona Ahmed Hassan Rady'}, {'id': '9246', 'value': 'Mona Ali Abdelmoomen Ali Mosa'}, {'id': '8403', 'value': 'Mona Ali Ekram  Ali '}, {'id': '5580', 'value': 'Mona Badr Eldin Anwar'}, {'id': '8918', 'value': 'Mona Wagdy Abdelghaffar Hussien '}, {'id': '7322', 'value': 'Monica Boulos Francois Badie Sakla'}, {'id': '6901', 'value': 'Monica Mohsen Fahmy Armanious'}, {'id': '6069', 'value': 'Monica Mosaad Abdel Malek'}, {'id': '5999', 'value': 'Mostafa Abdelaziz Elwaily'}, {'id': '9176', 'value': 'Mostafa Ahmed Mostafa Mohamed ElBahloul'}, {'id': '9026', 'value': 'Mostafa Mahdy Mohamed Abdellatif'}, {'id': '9404', 'value': 'Mostafa Moataz Abdelmagid Mahmoud Abdelmagid'}, {'id': '4800', 'value': 'Moustafa Ahmed Baraka'}, {'id': '9208', 'value': 'Moustafa Ashraf Abdeltawab Ibrahim Khattab'}, {'id': '8608', 'value': 'Mustafa Ismail Mohammed Ismail'}, {'id': '8463', 'value': 'Nabil  Mohamed Yehia Aly  Abotaleb'}, {'id': '2565', 'value': 'Nabila . Hamdi'}, {'id': '8434', 'value': 'Nada  Ashraf Mohamed Kamel  Emam '}, {'id': '8745', 'value': 'Nada  Ayman Abdelraouf Mohamed  Mahran '}, {'id': '8997', 'value': 'Nada  Mohamed Hammad  Mansour'}, {'id': '8273', 'value': 'Nada  Walid Elsayed  Abdelaziz'}, {'id': '4106', 'value': 'Nada Ahmed Hamed'}, {'id': '5330', 'value': 'Nada Alaa Bahaa'}, {'id': '4203', 'value': 'Nada Ammar Mohamed Abouelftoh Ezzatlo'}, {'id': '8246', 'value': 'Nada Ashraf Abdelazim Elsayed Mahmoud Rostum'}, {'id': '8526', 'value': 'Nada Ashraf Youssef Elhag Ahmed '}, {'id': '9276', 'value': 'Nada Hani Abdulhadi Ibrahim Mettwalli'}, {'id': '7733', 'value': 'Nada Hesham Abd Elfattah Ibrahim'}, {'id': '8836', 'value': 'Nada Khaled Mostafa Ali Khalaf'}, {'id': '7307', 'value': 'Nada Mohamed Mohamed Aboelyazid Ragab'}, {'id': '8942', 'value': 'Nada Mohyeldin Naguib  Bakeer'}, {'id': '7694', 'value': 'Nada Nasser Fawzy Ramadan'}, {'id': '7610', 'value': 'Nada Omar Mahmoud Abdel Ghany Zoher'}, {'id': '6459', 'value': 'Nada Osama Mohamed Hussein Elmossier'}, {'id': '9237', 'value': 'Nada Sayed Sayed Salheen  Sayed'}, {'id': '9194', 'value': 'Nada Tamer Abdelhay Abdellatif'}, {'id': '8640', 'value': 'Nadeen Amged Elamir Tadros Michael'}, {'id': '8591', 'value': 'Nadeen Elsayed Hamza Elboraey'}, {'id': '7284', 'value': 'Nadeen Emadeldin Mohamed Omar'}, {'id': '8827', 'value': 'Nadeen Tarek Zayan Abdelhafez'}, {'id': '331', 'value': 'Nadia . Yassin'}, {'id': '443', 'value': 'Nadia Mohamed Sharaf'}, {'id': '6125', 'value': 'Nadia Raafat Elmasry'}, {'id': '7939', 'value': 'Nadine Omar Youssef Ismail'}, {'id': '8191', 'value': 'Nadine Tarek Abdelrahman Mohamed Garir'}, {'id': '449', 'value': 'Nadja . Badran'}, {'id': '9215', 'value': 'Nagat Seifeldin Ali Marzouk'}, {'id': '669', 'value': 'Naglaa Abdel Wahed Mohamed'}, {'id': '8277', 'value': 'Naglaa Abozeid Abdelaziz Abdelwahab'}, {'id': '5035', 'value': 'Nagy Fouad Hanna'}, {'id': '798', 'value': 'Nahed Abdel Hamid El Mahallawy'}, {'id': '9186', 'value': 'Nahla Atef Abdelhai Mostafa'}, {'id': '4577', 'value': 'Nahla Hosny Ahmed Osman Elgizawy'}, {'id': '5539', 'value': 'Nancy Osama Turky'}, {'id': '8855', 'value': 'Nawraz Saeed Mohamed Saeed'}, {'id': '2348', 'value': 'Nazem Adib Behnam'}, {'id': '8924', 'value': 'Nermeen  Abobakr Omar  Mohamed'}, {'id': '8837', 'value': 'Nermeen Ashraf Fathi Abdelwahab Kansowah'}, {'id': '285', 'value': 'Nermeen Mohamed Serag El-Din'}, {'id': '2567', 'value': 'Nermien Mamdouh Fouaad'}, {'id': '8515', 'value': 'Nermin Ezzy Sems  Aziz'}, {'id': '4682', 'value': 'Nermin Hosny Ibrahim'}, {'id': '431', 'value': 'Nermin Salah Ahmed'}, {'id': '4059', 'value': 'Nervana Nabil Adeeb'}, {'id': '7746', 'value': 'Nesma Sayed Abdel Moneim'}, {'id': '2393', 'value': 'Nesrine Abd El-Rehim El-Gohary'}, {'id': '6759', 'value': 'Neveen Farag Ayoub Farag'}, {'id': '8576', 'value': 'Neveen Mahfouz Mabrouk Abdelhady'}, {'id': '8962', 'value': 'Nevine Mahmoud Saeed Hammouda'}, {'id': '8550', 'value': 'Nihal  Mohamed Anwar Saleh  Elhawary '}, {'id': '8325', 'value': 'Nihal Ali Metwally'}, {'id': '8981', 'value': 'Nike . Dieterich'}, {'id': '3707', 'value': 'Nikolai . Burger'}, {'id': '7684', 'value': 'Noha Abdellatif Hamid Abdellatif'}, {'id': '5998', 'value': 'Noha Ali Atta Hassan'}, {'id': '4352', 'value': 'Noha Desouky Abdelmoaty'}, {'id': '8261', 'value': 'Noha Ehab Mohamed Sobhy Ali Elshafiea'}, {'id': '8605', 'value': 'Noha Fathy Ibrahim  Hafez '}, {'id': '3579', 'value': 'Noha Foad Elhadary'}, {'id': '152', 'value': 'Noha Hesham El-Bassiouny'}, {'id': '6562', 'value': 'Noha Mohamed Abdeltawab Elsaid Saleh'}, {'id': '470', 'value': 'Noha Omaya Shabana'}, {'id': '3215', 'value': 'Noha Samir Farag'}, {'id': '7706', 'value': 'Noha Sherif Sayed Kamel Mohamed Hassan Elzeny'}, {'id': '9080', 'value': 'Noha Tarek Amin Mohamed Amer'}, {'id': '6546', 'value': 'Nora Adel Salem'}, {'id': '6796', 'value': 'Noran Khaled Nabil Darwish'}, {'id': '8865', 'value': 'Nour Ahmed Abdelaleem Elajiry'}, {'id': '8854', 'value': 'Nour Amr Amin Idris'}, {'id': '8955', 'value': 'Nour Amr Hamed Abdelhamid Helaly'}, {'id': '7011', 'value': 'Noura Abdel Shafi Ali Abdel Shafi'}, {'id': '5506', 'value': 'Noura Ayman Ahmed'}, {'id': '7953', 'value': 'Nouran Adel Hassan Ahmed'}, {'id': '9169', 'value': 'Nouran Amr Abdelaziz Ahmed Elfaramawy'}, {'id': '5001', 'value': 'Nouran Mohamed Arafat'}, {'id': '6411', 'value': 'Nouran Zakaria Abdelhamid Mohamed'}, {'id': '9379', 'value': 'Noureldin Yehia Mohamed Amin Abdelazem Elsehimy'}, {'id': '9398', 'value': 'Nourhan Abdelmonem Saad Mohamed Elgendy'}, {'id': '7924', 'value': 'Nourhan Ahmed Kamal El Fransawy'}, {'id': '5447', 'value': 'Nourhan Ehab Azab'}, {'id': '8915', 'value': 'Nourhan Hani Rezq Afifi Ali'}, {'id': '7293', 'value': 'Nourhan Ramadan Ibrahim Ramadan'}, {'id': '7753', 'value': 'Ola Ahmed Fouad Ahmed'}, {'id': '8850', 'value': 'Ola Emad Eldeen Zakaria Abdelsalam'}, {'id': '7428', 'value': 'Ola Farouk Ahmed Zaher'}, {'id': '8538', 'value': 'Omar Abdelmonem Mohammed Mohammed Hassan'}, {'id': '9304', 'value': 'Omar Amir Taher Abdullah Arafa  Elsheikh'}, {'id': '3775', 'value': 'Omar Mahmoud Mohamed Shehata'}, {'id': '7267', 'value': 'Omar Medhat khairy Ahmed'}, {'id': '8257', 'value': 'Omar Mohamed Sakr Eshak  Alsaad'}, {'id': '8882', 'value': 'Omar Osama Mahmoud  Elnagar'}, {'id': '7940', 'value': 'Omar Sameh Mohammed Salem'}, {'id': '8843', 'value': 'Omar Tarek Adly Abdella Ali'}, {'id': '8992', 'value': 'Omnia  Abdelwahab Mohamed  Elqaramany '}, {'id': '9295', 'value': 'Omnia Gamal Abdo Abdallah Eissa'}, {'id': '6493', 'value': 'Omnia Mohamed Nabil Osman Mohamed Elsayed'}, {'id': '8562', 'value': 'Omnia Sherif Elsaid  Elhosseiny '}, {'id': '7468', 'value': 'Omniya Mahmoud abdel Aty Mahmoud Serry'}, {'id': '9410', 'value': 'Osama Khairy Saleh Eraky'}, {'id': '8580', 'value': 'Pardis Helmy Youssef Ardash'}, {'id': '8549', 'value': 'Parthiena  Maher Keddis  Ibrahim '}, {'id': '4463', 'value': 'Passant  Khaled  Abbassy'}, {'id': '7777', 'value': 'Paula Fayez Fouad Tanaghy'}, {'id': '5038', 'value': 'Peter . Blodau'}, {'id': '8540', 'value': 'Peter Nabil Thabet  Wassef '}, {'id': '8271', 'value': 'Pillara Mohamed Abdelmoniem Moawad Moftah'}, {'id': '3825', 'value': 'Radwa Abdelmoniem Mohamed'}, {'id': '8898', 'value': 'Radwa Amr Mohamed Rabie Abdelsalam Elsayed'}, {'id': '7987', 'value': 'Radwa Mohamed Eid Mohamed Rostom'}, {'id': '6876', 'value': 'Radwa Mohamed Ramzy Refaat Abouel Enein'}, {'id': '7678', 'value': 'Radwa Mokhtar Mohamed Ahmed'}, {'id': '8885', 'value': 'Raghda  Essam Taha Othman  Hussien '}, {'id': '9236', 'value': 'Raghda  Mohamed Baioumy Mohamed  Hassan'}, {'id': '6031', 'value': 'Raghda Abdelmonem Elsabbagh'}, {'id': '8760', 'value': 'Raghda Fawzy Mohamed Ahmed Hassan'}, {'id': '780', 'value': 'Raghda M. Mamdouh Elebrashi'}, {'id': '6020', 'value': 'Raghda Sayed Abdelaziz'}, {'id': '429', 'value': 'Ragwa Mansour Abdelghany'}, {'id': '8447', 'value': 'Rahma  Shehab Eldin Mohamed Abu Taleb'}, {'id': '9209', 'value': 'Rahma Mohamed Fawzy Seliman'}, {'id': '8661', 'value': 'Rama  Mohamed Elsayed Mohamed  Elmor '}, {'id': '8505', 'value': 'Ramez Mohamed Ibrahim Elmasry'}, {'id': '8610', 'value': 'Rana Ahmed Sayed Hassan El-Sayed'}, {'id': '9168', 'value': 'Rana Elsayed Shahat Mahmoud'}, {'id': '9180', 'value': 'Rana Emad Nahas Ahmed'}, {'id': '8672', 'value': 'Rana Emadeldin Mohammed Mahmoud'}, {'id': '3540', 'value': 'Rana Hassan Abdallah Hassan Alnahal'}, {'id': '7233', 'value': 'Rana Mohamed Mohsen Mohamed Yousef Amer'}, {'id': '5985', 'value': 'Rana Mostafa Ismail'}, {'id': '8600', 'value': 'Rana Salah Morsy Sayed'}, {'id': '8874', 'value': 'Rana Samer Massoud  Adly '}, {'id': '9065', 'value': 'Rana Wassef Abdelkader Youssef'}, {'id': '4459', 'value': 'Randa Mohamed Elkhosht'}, {'id': '8919', 'value': 'Raneem  Ahmed Mohamed Ahmed Elsanhory '}, {'id': '2471', 'value': 'Rania Mamdouh Metwally Zidan'}, {'id': '9251', 'value': 'Ranim Mahmoud Mohamed Mahmoud Mohamed'}, {'id': '8305', 'value': 'Rasha Magdy Wahieb'}, {'id': '2775', 'value': 'Rasha Mohamed Emara'}, {'id': '6609', 'value': 'Rasha Saad Elsayed Mohamed Hassan'}, {'id': '137', 'value': 'Rasha Sayed Hanafi'}, {'id': '9384', 'value': 'Rawan Hesham Abdelwahed Abbas Elmasry'}, {'id': '9397', 'value': 'Rawan Mohsen Abdelhalim Ibrahim'}, {'id': '9177', 'value': 'Rawan Mostafa Ahmed Ahmed Mohamed'}, {'id': '9173', 'value': 'Reem  Amr Ahmed Abdelmeguid '}, {'id': '8651', 'value': 'Reem  Refaat Elsayed  Abdelhamid'}, {'id': '8514', 'value': 'Reem Adel Mohamad Alansary'}, {'id': '5505', 'value': 'Reem Ahmed Wagdy'}, {'id': '6910', 'value': 'Reem Anas Haroun Abdel Meguid'}, {'id': '7668', 'value': 'Reem Baheyeddin Mohammed Moursy'}, {'id': '9429', 'value': 'Reem Fayez Messeha Philips'}, {'id': '8440', 'value': 'Reem Gamal Awad Aboelgheit'}, {'id': '6873', 'value': 'Reem Hassan Ibrahim'}, {'id': '6794', 'value': 'Reem Hisham Ibrahim Allam'}, {'id': '9319', 'value': 'Reem Hossam Eldin Ahmed Awad'}, {'id': '7839', 'value': 'Reem Magdy Badawy Abdel Razik'}, {'id': '7088', 'value': 'Reem Mohamed Sameh Ali Hassan Saleh'}, {'id': '9144', 'value': 'Reem Mohyeldin Gawaby Mahmoud'}, {'id': '6630', 'value': 'Reem Osama Ahmed Ashour'}, {'id': '3650', 'value': 'Regine Eugenie Ritz'}, {'id': '7626', 'value': 'Reham Ayman Saad Mohamed Hassan'}, {'id': '8832', 'value': 'Reham Hatem Mohamed Mohamed Harraz'}, {'id': '9223', 'value': 'Reham Khaled Mohamed Elsayed  Gomaa'}, {'id': '72', 'value': 'Reham Mahmoud Abdel Kader'}, {'id': '6879', 'value': 'Reham Waheed Ezzat Mahmoud Al Ajami'}, {'id': '9107', 'value': 'Remon Gerges Hakim Gerges'}, {'id': '9175', 'value': 'Remonda Ayman Abdou Awadallah'}, {'id': '8511', 'value': 'Renad  Nabil Ahmed  Abdelhaleem '}, {'id': '5444', 'value': 'Riham Essam Khattab'}, {'id': '795', 'value': 'Rimon . Elias'}, {'id': '9225', 'value': 'Rinada Mohamed Abdelaziz'}, {'id': '5740', 'value': 'Rita Pinto De Freitas'}, {'id': '8952', 'value': 'Rodaina Khaled Abdelmoniem Taha Mohamed Koraa'}, {'id': '5156', 'value': 'Rodayna Mohamed Hossni'}, {'id': '8532', 'value': 'Rola  Sameh Mahmoud  Abdelmoneim '}, {'id': '8834', 'value': 'Roqaya Emad Fahmy Mohamed Hussin '}, {'id': '6862', 'value': 'Ruairi Desmond O`brien'}, {'id': '5978', 'value': 'Ryan Vincente Lee Grees'}, {'id': '3611', 'value': 'Sabine . Muller'}, {'id': '2346', 'value': 'Sahar Mohamed Abdel Maksoud'}, {'id': '2518', 'value': 'Sahar Mohamed Abouzaid'}, {'id': '3951', 'value': 'Sally . Hanna'}, {'id': '6661', 'value': 'Sally Ann Skerrett'}, {'id': '3290', 'value': 'Sally Mahmoud Salah Eldin Hassan Nafie'}, {'id': '4435', 'value': 'Sally Mostafa Hamad'}, {'id': '8921', 'value': 'Salma  Osama Kamel Mostafa  Kishk '}, {'id': '9193', 'value': 'Salma  Sherif Elsayed Emam'}, {'id': '7594', 'value': 'Salma  Yasser Tosson Mohamed Helmy'}, {'id': '8581', 'value': 'Salma Alaa Eldeen Abouelyazeed Kotb'}, {'id': '8838', 'value': 'Salma Ashraf Mostafa Shehata Awad'}, {'id': '8596', 'value': 'Salma Elsayed Abdelmouniem Yousef'}, {'id': '8936', 'value': 'Salma Hesham Zakaria Ibrahim Darwish'}, {'id': '9172', 'value': 'Salma Hossam Abdallah  Salem'}, {'id': '8276', 'value': 'Salma Hossam Eldin Helmy Ali Attia'}, {'id': '9164', 'value': 'Salma Ismail Hassan Sabry Ismail'}, {'id': '9403', 'value': 'Salma Khaled Abdelmoniem  Khashaba'}, {'id': '9391', 'value': 'Salma Maher Mohamed Elsherif'}, {'id': '8541', 'value': 'Salma Mahmoud Ahmed  Haggag '}, {'id': '5183', 'value': 'Salma Mahmoud Sadek'}, {'id': '8826', 'value': 'Salma Mohamed Hassan Hamdy Mohamed Swidan'}, {'id': '6518', 'value': 'Salma Mohamed Shaaban Elshamy'}, {'id': '7027', 'value': 'Salma Waheed Mohamed Mahmoud Abdel Atif'}, {'id': '9312', 'value': 'Sama  Hussien Aboelola Abdelmwla'}, {'id': '8943', 'value': 'Sama Alaa Gamaleldin Ahmed Elgedda'}, {'id': '8852', 'value': 'Sama Ashraf Elsayed Ahmed Wahb'}, {'id': '7265', 'value': 'Samaa Khaled Abdelkawy Abdelrahman'}, {'id': '8991', 'value': 'Samaa Soliman Hussein Ahmed Shalaby '}, {'id': '2784', 'value': 'Samah Adel Ibrahim'}, {'id': '4166', 'value': 'Samar Mansour Holayel'}, {'id': '5061', 'value': 'Samar Mohamed Shukry'}, {'id': '9201', 'value': 'Samar SalahEldin Abdalaziz Abdalwahab Ghoneim'}, {'id': '9392', 'value': 'Sameh Mohamed Reda Reyad  Ahmed'}, {'id': '8886', 'value': 'Samer  Walaa Gabra Abdallah '}, {'id': '6733', 'value': 'Samia Ali Kara Mohamed'}, {'id': '4072', 'value': 'Samuel Safwat Hanna'}, {'id': '8262', 'value': 'Samuel Yaser Jacob Basta'}, {'id': '336', 'value': 'Saneya . El-Galaly'}, {'id': '8614', 'value': 'Sara  Ahmed Farouk  Hammad '}, {'id': '9105', 'value': 'Sara  Fathi Mohamed  Ibrahim'}, {'id': '7255', 'value': 'Sara  Samy Abdelhakim Elfaramawy'}, {'id': '7597', 'value': 'Sara Araby Ramadan Aly Mosa'}, {'id': '4653', 'value': 'Sara Kamel Girgis Aboelkhair'}, {'id': '6519', 'value': 'Sara Mahmoud Atwa'}, {'id': '2739', 'value': 'Sara Mohamed El-Deeb'}, {'id': '6777', 'value': 'Sara Mohamed Mohamed Elasy'}, {'id': '6063', 'value': 'Sara Refaat Elmasry'}, {'id': '5632', 'value': 'Sara Samy Khalifa'}, {'id': '4823', 'value': 'Sara Shohdy Thabit'}, {'id': '6606', 'value': 'Sara Tarek Ali Shehata'}, {'id': '7077', 'value': 'Sarah Abdallah Mahmoud Alghorab'}, {'id': '5646', 'value': 'Sarah Ismail Mohamed'}, {'id': '6055', 'value': 'Sarah Mohamed Imam'}, {'id': '4683', 'value': 'Sarah Mohamed Magdy'}, {'id': '9109', 'value': 'Sarah Mohamed Yehia Aly Elhawary'}, {'id': '4044', 'value': 'Sarah Mostafa Azzam'}, {'id': '8844', 'value': 'Sarah Samir Elsayed Hassan Hassan Hussien '}, {'id': '8750', 'value': 'Sarah Tarek Tahseen Shannon'}, {'id': '9213', 'value': 'Sarah Yasser Mohsen Abdelaziz'}, {'id': '9306', 'value': 'Seifeldin Hatem Hafez  Zeidan'}, {'id': '3225', 'value': 'Shahir Ashraf Wagdy Aziz'}, {'id': '8960', 'value': 'Shahira Mohamed Bilal Moahmed Saed Hatahet'}, {'id': '9192', 'value': 'Shaimaa  Farid Mostafa  Abdelaziz'}, {'id': '6121', 'value': 'Shaimaa Ali Zein Elabedin '}, {'id': '5250', 'value': 'Shaimaa Reda Fayed'}, {'id': '8598', 'value': 'Shehabedeen  Ashraf Ali Ibrahim '}, {'id': '2831', 'value': 'Shereen Ekram Mohamed'}, {'id': '8519', 'value': 'Shereen Moataz Mahmoud Mohamed Afifi'}, {'id': '9007', 'value': 'Shereen Wael Mohamed Abdelrazeq'}, {'id': '6529', 'value': 'Sherif Nasser Aref'}, {'id': '9401', 'value': 'Sherifa Khaled Mursi Mohamed  Fawzy Ibrahim Hammoud'}, {'id': '6853', 'value': 'Sherihan Omar Abou Zeid Mohamed Abou Zeid'}, {'id': '8929', 'value': 'Shorook Mohamed Said Mostafa Soliman'}, {'id': '7625', 'value': 'Silvana Ehab Anis Elkomos Tadros'}, {'id': '3922', 'value': 'Silvia . Burger'}, {'id': '7559', 'value': 'Silvia . Covarino'}, {'id': '6841', 'value': 'Simone Sami Guergues Khalil'}, {'id': '18', 'value': 'Slim . Abdennadher'}, {'id': '8759', 'value': 'Soha Ahmed Elsayed  Abutaleb'}, {'id': '9294', 'value': 'Sohaila Ahmed Mahmoud Hassanien'}, {'id': '9428', 'value': 'Soheila Ossama Sobhi Ahmed'}, {'id': '9305', 'value': 'Somaya  Mohamed Ali Amer Ahmed'}, {'id': '7964', 'value': 'Sondoss Magdy Abd El Sattar Abdel Maboud'}, {'id': '8994', 'value': 'Sonia Youssef Mohamed Hosny El Serafy'}, {'id': '4656', 'value': 'Steffen . Scholl'}, {'id': '142', 'value': 'Suzanne Ali Salah El-Din'}, {'id': '8995', 'value': 'Suzi  Maher Wissa kaldas'}, {'id': '7204', 'value': 'Sylvana Spiro Raphael Rizk'}, {'id': '3778', 'value': 'Taher Mohamed Salaheldin'}, {'id': '621', 'value': 'Tallal Osama Elshabrawy'}, {'id': '8258', 'value': 'Tameem Mohamed Elshaheer Ahmed Hassan Mohamed Alghazaly'}, {'id': '4475', 'value': 'Tamer Abbas Awad'}, {'id': '9433', 'value': 'Tamer Ahmed Mostafa Mohammed Abdelkader'}, {'id': '8633', 'value': 'Tarek  Safwat Aly  Mohamed '}, {'id': '36', 'value': 'Tarek Abbas Metwally Khalil'}, {'id': '119', 'value': 'Tarek Fouad Riad'}, {'id': '8958', 'value': 'Taslima Ayman Mousa Mohamed Ismaiel'}, {'id': '8631', 'value': 'Tasneem Nabil Mohammed Elghobashy'}, {'id': '7168', 'value': 'Thanaa Hosni Hussein Awwad'}, {'id': '6761', 'value': 'Theodora Ramzy Hanna Morkous'}, {'id': '6953', 'value': 'Tobias Ulrich Wenig'}, {'id': '8630', 'value': 'Toka Ossama Youssef Mahmoud Barghash'}, {'id': '9285', 'value': 'Toqa Alaaeldeen Mahmoud Ahmed'}, {'id': '9287', 'value': 'Toya Ahmed Hisham Mousa'}, {'id': '9344', 'value': 'Tuuli Anna Miranda Saarelainen'}, {'id': '2486', 'value': 'Ulrike . Breitinger'}, {'id': '9248', 'value': 'Wael  Mohamed Hassan Shams Eldin'}, {'id': '8727', 'value': 'Wael Zakaria Abdallah'}, {'id': '4502', 'value': 'Wafaa Hussein Nadim'}, {'id': '8497', 'value': 'Wagdy Anis Aziz'}, {'id': '5644', 'value': 'Walaa Ahmed Saad Mahmoud'}, {'id': '6542', 'value': 'Waled Sayed Abdelzaher Emam'}, {'id': '4364', 'value': 'Walid Atef Hafez Omran'}, {'id': '4984', 'value': 'Walid Mohamed Elshamy'}, {'id': '3765', 'value': 'Wassim Joseph  Alexan'}, {'id': '9198', 'value': 'Yahia  Eladl Elhendawi Mohamed Moussa Omran'}, {'id': '7082', 'value': 'Yamen Hazem ElGamal'}, {'id': '6776', 'value': 'Yara Abdelkhalek Mohamed Abdelsamad Elsehaimy'}, {'id': '8862', 'value': 'Yara Ahmed Abdelhamid Emam Abobakr '}, {'id': '7707', 'value': 'Yara Ayman Mahmoud Abdel Rahman'}, {'id': '6749', 'value': 'Yara Galal Mohamed Shaban'}, {'id': '7415', 'value': 'Yara Hany Mahmoud Abdelsalam Elsherif'}, {'id': '6527', 'value': 'Yara Mohamed Taher Ismail'}, {'id': '7709', 'value': 'Yara Talaat Hussein Ibrahim ElHarouni'}, {'id': '8621', 'value': 'Yasmeen Sameh Abdeltawab  Abdelmoaty '}, {'id': '8421', 'value': 'Yasmin  Hazem Abdelhafiz Mahmoud Elmously '}, {'id': '8566', 'value': 'Yasmin  Zenhom Hesbelnaby Mohamed  Elshoura '}, {'id': '7730', 'value': 'Yasmin Ahmed Mahmoud Amer Elbehiery'}, {'id': '9335', 'value': 'Yasmin Mabrouk Saad Ahmed'}, {'id': '4985', 'value': 'Yasmina Sherif Sarhan'}, {'id': '8563', 'value': 'Yasmine  Hossam Khairy  Abouelhossein '}, {'id': '6440', 'value': 'Yasser Mohamed Mahmoud Ahmed Ghandour Waly'}, {'id': '8762', 'value': 'Yassin Mohamed Tharwat Elshazly Mohamed Elshazly'}, {'id': '8876', 'value': 'Yassmine  Mohamed Abdalla Darwish  Haggag'}, {'id': '8219', 'value': 'Yomna  Essam Abbas Halim Sorour '}, {'id': '8877', 'value': 'Yomna Islam Youssef Alayary'}, {'id': '9396', 'value': 'Yomna Mahmoud Ibrahim Hassan'}, {'id': '9383', 'value': 'Yomna Mohamed Ali Mohamed Ali Moussa'}, {'id': '6765', 'value': 'Yosra Adel Zaki Hassan Malek'}, {'id': '8239', 'value': 'Youmna Abo Elfotouh Abdelmoaz Mabrouk'}, {'id': '7645', 'value': 'Youmna Atef Abdel Azim Afifi Shehata'}, {'id': '8059', 'value': 'Youmna Soliman Abd El-Hamid Soliman El-Sherbiny'}, {'id': '9090', 'value': 'Yousef  Saeed Mohamed  Taha'}, {'id': '9079', 'value': 'Yousef  Waleed Hassan Mohamed Abdelshafy'}, {'id': '8866', 'value': 'Yousef Mohamed Adel Ebrahem Mahmoud Said'}, {'id': '8920', 'value': 'Youssef  Ahmed Kamal Ismail  Abulyousr'}, {'id': '8655', 'value': 'Youssef Abdelrahman Ahmed Abdelrazek'}, {'id': '9196', 'value': 'Youssef George Albert Tawfilis'}, {'id': '6521', 'value': 'Youssef Helmi Youssef Mohamed'}, {'id': '8845', 'value': 'Youssef Ihab Awad Ismail  Ahmed '}, {'id': '6899', 'value': 'Youssef Mohamed Ahmed Abou Shady'}, {'id': '9269', 'value': 'Youssef Mohamed Ghazaly Sayed Ahmed'}, {'id': '8521', 'value': 'Youstina  Ashraf Philip Botros '}, {'id': '6496', 'value': 'Youstina Megalli Kamal'}, {'id': '7331', 'value': 'youstina Samir Malak Mansour'}, {'id': '9288', 'value': 'Zahraa Mohamed Abdelfatah Hassan Ali'}, {'id': '5645', 'value': 'Zaynab Abdalaziz Awad Sadiq'}, {'id': '8861', 'value': 'Zeina Ibrahim Ahmed Ahmed Ali Eissa'}, {'id': '8667', 'value': 'Ziad  Mahmoud Mahmoud Elsayed  Gabr '}, {'id': '9249', 'value': 'Ziad Ayman Salem Ali Hegazy'}];</script></form>
    
        <script type="text/javascript" src="/CSS/architectui-html-pro-1.4.0/main.87c0748b313a1dda75f5.js"></script>
        <script src="/CSS/architectui-html-pro-1.4.0/jqueryV3.6.3.min.js"></script>
        <script>
            $(document).ready(function () {
    
                var x = $(location).attr('search');
                $('li.mm-active').closest('a.mm-active').removeClass('mm-active');
                $('li.mm-active').removeClass('mm-active');
                $('a[href="' + location.pathname + x + '"]').closest('li').addClass('mm-active');
                $('a[href="' + location.pathname + x + '"]').addClass('mm-active');
            });
        </script>
    
        <script>
            $('.app-container').addClass('closed-sidebar');
            //maintain postion scroll
            $(document).ready(function () {
                var div_position = $("#div_position");
                if (div_position.val() != 0) {
                    $(window).scrollTop(div_position.val());
                }
                $(window).scroll(function () {
                    div_position.val($(window).scrollTop());
                });
            });
    
    
            //hide fields from table
            $(document).ready(function () {
                $(".modal").each(function () {
                    $(this).attr("data-backdrop", "static");
                    $(this).attr("data-keyboard", "false");
                });
                $(".table").each(function () {
                    var ownerIndex = $(this).find('th:contains("ID")').index() + 1;
                    $(this).find('td:nth-child(' + ownerIndex + ')').hide();
                    $(this).find("tbody tr, thead tr").children(":nth-child(" + ownerIndex + ")").hide()
    
                    var ownerIndex2 = $(this).find('th:contains("URL")').index() + 1;
                    $(this).find('td:nth-child(' + ownerIndex2 + ')').hide();
                    $(this).find("tbody tr, thead tr").children(":nth-child(" + ownerIndex2 + ")").hide()
    
                    var ownerIndex3 = $(this).find('th:contains("SeasonId")').index() + 1;
                    $(this).find('td:nth-child(' + ownerIndex3 + ')').hide();
                    $(this).find("tbody tr, thead tr").children(":nth-child(" + ownerIndex3 + ")").hide()
    
                });
    
                $(".table").each(function () {
                    var newul = $(this).parent().attr("Style", "overflow-x: auto; ")
                });
    
                $(".dropdown-menu-header").click(function () {
    
                    $(this).next(".card-body").toggle("slow");
    
    
                });
                $('.app-container').addClass('closed-sidebar');
    
            });
        </script>
    
    
        
    
        
        <script type="text/javascript" src="/CSS/oldJavaScript/home.js"></script>
    
    
        <script>
            $(document).ready(function () {
                var expanded = false;
                var menu = $('#menu').html();
    
                $('#search-input').on('input', function () {
                    var searchQuery = $(this).val();
                    searchMenu(searchQuery);
                    if (expanded == false) {
                        $('#menu li').each(function () {
                            if (!$(this).eq(0).hasClass('mm-active')) {
                                $(this).find('a').attr("aria-expanded", "true");
                                $(this).find('ul').addClass("mm-show");
                            }
                        });
                        expanded = true;
                    }
                    if (searchQuery == "" && expanded == true) {
                        $('#menu li').each(function () {
                            if (!$(this).eq(0).hasClass('mm-active')) {
                                $(this).find('a').attr("aria-expanded", "false");
                                $(this).find('ul').removeClass("mm-show");
                            }
                            expanded = false;
                        });
                    }
                });
            });      
           
    
            function searchMenu(query) {
               
                // Loop through all menu items
                $('#menu li').each(function () {
                   
                    // Get the text content of the menu item
                    var text = $(this).text().toLowerCase();
                   
                    // If the text content contains the search query, show the menu item
                    if (text.indexOf(query.toLowerCase()) >= 0) {
                        
                        $(this).show();
                    } else {
                        
                        $(this).hide();
                    }
                });
            }
    
        </script>
    
    
        <script type="text/javascript" language="javascript">
            $(document).ready(function () {
                $(document).on("keydown", disableF5);
            });
            function disableF5(e) { if ((e.which || e.keyCode) == 116) e.preventDefault(); };
        </script>
    
    
    <svg id="SvgjsSvg1001" width="2" height="0" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs" style="overflow: hidden; top: -100%; left: -100%; position: absolute; opacity: 0;"><defs id="SvgjsDefs1002"></defs><polyline id="SvgjsPolyline1003" points="0,0"></polyline><path id="SvgjsPath1004" d="M0 0 "></path></svg><veepn-lock-screen><style>@font-face{font-family:FigtreeVF;src:url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2 supports variations"),url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2-variations");font-weight:100 1000;font-display:swap}</style></veepn-lock-screen><veepn-guard-alert><style>@font-face{font-family:FigtreeVF;src:url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2 supports variations"),url(chrome-extension://majdfhpaihoncoakbjgbdhglocklcgno/fonts/FigtreeVF.woff2) format("woff2-variations");font-weight:100 1000;font-display:swap}</style></veepn-guard-alert><div class="jvectormap-tip"></div></body><grammarly-desktop-integration data-grammarly-shadow-root="true"></grammarly-desktop-integration></html>