attendance_mockData = {
    "Spring 2025  - CSEN 202 - Introduction to Computer Programming": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG P038 Practical @2025.02.26 - Regular  - Slot5 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG P038 Practical @2025.03.05 - Regular  - Slot5 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG P038 Practical @2025.03.12 - Regular  - Slot5 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG P038 Practical @2025.03.19 - Regular  - Slot5 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG P038 Practical @2025.03.26 - Regular  - Slot5 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CS<PERSON> 202 - Introduction to Computer Programming - 2ENG T038 Tutorial @2025.02.26 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG T038 Tutorial @2025.03.05 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG T038 Tutorial @2025.03.12 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG T038 Tutorial @2025.03.19 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - CSEN 202 - Introduction to Computer Programming - 2ENG T038 Tutorial @2025.03.26 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
        ],
    },
    "Spring 2025  - DE 202 - Basic German 2": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.02.23 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.02.27 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.02 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.06 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.09 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.13 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.16 - Compensation  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.20 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.23 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - DE 202 - Basic German 2 - 2ENG IX+X 2G T031 Tutorial @2025.03.27 - Compensation  - Slot4 - 2h",
                "status": "Absent",
            },
        ],
    },
    "Spring 2025  - ELCT 201 - Digital Logic Design": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - ELCT 201 - Digital Logic Design - 2ENG T038 Tutorial @2025.02.22 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ELCT 201 - Digital Logic Design - 2ENG T038 Tutorial @2025.03.01 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ELCT 201 - Digital Logic Design - 2ENG T038 Tutorial @2025.03.08 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ELCT 201 - Digital Logic Design - 2ENG T038 Tutorial @2025.03.15 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ELCT 201 - Digital Logic Design - 2ENG T038 Tutorial @2025.03.22 - Regular  - Slot1 - 2h",
                "status": "Attended",
            },
        ],
    },
    "Spring 2025  - ENGD 301 - Engineering Drawing & Design": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - ENGD 301 - Engineering Drawing & Design - 2ENG T038 Tutorial @2025.02.22 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ENGD 301 - Engineering Drawing & Design - 2ENG T038 Tutorial @2025.03.01 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ENGD 301 - Engineering Drawing & Design - 2ENG T038 Tutorial @2025.03.08 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ENGD 301 - Engineering Drawing & Design - 2ENG T038 Tutorial @2025.03.15 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - ENGD 301 - Engineering Drawing & Design - 2ENG T038 Tutorial @2025.03.22 - Regular  - Slot4 - 2h",
                "status": "Attended",
            },
        ],
    },
    "Spring 2025  - MATH 203 - Mathematics I": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.02.23 - Regular  - Slot2 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.02.26 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.02 - Regular  - Slot2 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.05 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.09 - Regular  - Slot2 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.12 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.16 - Regular  - Slot2 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.19 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.23 - Regular  - Slot2 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - MATH 203 - Mathematics I - 2ENG T038 Tutorial @2025.03.26 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
        ],
    },
    "Spring 2025  - PHYS 202 - Physics II": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - PHYS 202 - Physics II - 2ENG T038 Tutorial @2025.02.27 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - PHYS 202 - Physics II - 2ENG T038 Tutorial @2025.03.06 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - PHYS 202 - Physics II - 2ENG T038 Tutorial @2025.03.13 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - PHYS 202 - Physics II - 2ENG T038 Tutorial @2025.03.20 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - PHYS 202 - Physics II - 2ENG T038 Tutorial @2025.03.27 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
        ],
    },
    "Spring 2025  - SM 101 - Scientific Methods (A1)": {
        "absence_level": "No Warning Level",
        "sessions": [
            {
                "session": "S25 - SM 101 - Scientific Methods (A1) - 2 ENG IX SM T028 Tutorial @2025.02.23 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - SM 101 - Scientific Methods (A1) - 2 ENG IX SM T028 Tutorial @2025.03.02 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - SM 101 - Scientific Methods (A1) - 2 ENG IX SM T028 Tutorial @2025.03.09 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - SM 101 - Scientific Methods (A1) - 2 ENG IX SM T028 Tutorial @2025.03.16 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
            {
                "session": "S25 - SM 101 - Scientific Methods (A1) - 2 ENG IX SM T028 Tutorial @2025.03.23 - Regular  - Slot3 - 2h",
                "status": "Attended",
            },
        ],
    },
}

grades_mockData = {
    "detailed_grades": {
        "Engineering 2nd Semester - CSEN202 Introduction to Computer Programming": {
            "Question1_0": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Lab test 1",
                "grade": "8/10",
                "out_of": 10,
                "percentage": 8,
            },
            "Question1_1": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 1",
                "grade": "6/10",
                "out_of": 10,
                "percentage": 6,
            },
        },
        "Engineering 2nd Semester - ELCT201 Digital Logic Design": {
            "Quiz 1 Grade_0": {
                "Element Name": "Quiz 1 Grade",
                "Quiz/Assignment": "Quiz 1",
                "grade": "12/15",
                "out_of": 15,
                "percentage": 12,
            },
            "Quiz 2 Grade_1": {
                "Element Name": "Quiz 2 Grade",
                "Quiz/Assignment": "Quiz 2",
                "grade": "7/10",
                "out_of": 10,
                "percentage": 7,
            },
        },
        "Engineering 2nd Semester - ENGD301 Engineering Drawing & Design": {
            "Question1_0": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 7",
                "grade": "9/10",
                "out_of": 10,
                "percentage": 9,
            },
            "Question1_1": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 2",
                "grade": "7/10",
                "out_of": 10,
                "percentage": 7,
            },
            "Question1_10": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 8",
                "grade": "5/10",
                "out_of": 10,
                "percentage": 5,
            },
            "Question1_11": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 3",
                "grade": "8.5/10",
                "out_of": 10,
                "percentage": 8.5,
            },
            "Question1_12": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 10",
                "grade": "6/10",
                "out_of": 10,
                "percentage": 6,
            },
            "Question1_13": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Activity 1",
                "grade": "3/5",
                "out_of": 5,
                "percentage": 3,
            },
            "Question1_2": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 1",
                "grade": "20/25",
                "out_of": 25,
                "percentage": 20,
            },
            "Question1_3": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 3",
                "grade": "15/25",
                "out_of": 25,
                "percentage": 15,
            },
            "Question1_4": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 4",
                "grade": "7.5/10",
                "out_of": 10,
                "percentage": 7.5,
            },
            "Question1_5": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 6",
                "grade": "4/10",
                "out_of": 10,
                "percentage": 4,
            },
            "Question1_6": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 2",
                "grade": "18/25",
                "out_of": 25,
                "percentage": 18,
            },
            "Question1_7": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 1",
                "grade": "9/10",
                "out_of": 10,
                "percentage": 9,
            },
            "Question1_8": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 9",
                "grade": "8/10",
                "out_of": 10,
                "percentage": 8,
            },
            "Question1_9": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Tutorial 5",
                "grade": "10/10",
                "out_of": 10,
                "percentage": 10,
            },
        },
        "Engineering 2nd Semester - MATH203 Mathematics I": {
            "Question1_0": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Assignment 1",
                "grade": "85/100",
                "out_of": 100,
                "percentage": 85,
            },
            "Question1_1": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 2",
                "grade": "10/15",
                "out_of": 15,
                "percentage": 10,
            },
            "Question1_2": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Assignment 4",
                "grade": "75/100",
                "out_of": 100,
                "percentage": 75,
            },
            "Question1_3": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Assignment 5",
                "grade": "90/100",
                "out_of": 100,
                "percentage": 90,
            },
            "Question1_4": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 1",
                "grade": "8/15",
                "out_of": 15,
                "percentage": 8,
            },
            "Question1_5": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Assignment 3",
                "grade": "95/100",
                "out_of": 100,
                "percentage": 95,
            },
            "Question1_6": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 3",
                "grade": "12/15",
                "out_of": 15,
                "percentage": 12,
            },
            "Question1_7": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Assignment 2",
                "grade": "65/100",
                "out_of": 100,
                "percentage": 65,
            },
        },
        "Engineering 2nd Semester - PHYS202 Physics II": {
            "Question1_1": {
                "Element Name": "Question1",
                "Quiz/Assignment": "Quiz 1",
                "grade": "7/12",
                "out_of": 12,
                "percentage": 7,
            },
            "WBA1_0": {
                "Element Name": "WBA1",
                "Quiz/Assignment": "WBA1",
                "grade": "10/12",
                "out_of": 12,
                "percentage": 10,
            },
        },
        "General - SM101 Scientific Methods (A1)": {
            "Problem Solving_5": {
                "Element Name": "Problem Solving",
                "Quiz/Assignment": "",
                "grade": "3/5",
                "out_of": 5,
                "percentage": 3,
            },
            "SM Quiz 2_3": {
                "Element Name": "SM Quiz 2",
                "Quiz/Assignment": "SM Quiz 2",
                "grade": "2/4",
                "out_of": 4,
                "percentage": 2,
            },
            "SM quiz 1_2": {
                "Element Name": "SM quiz 1",
                "Quiz/Assignment": "SM Quiz 1",
                "grade": "5/5",
                "out_of": 5,
                "percentage": 5,
            },
            "SM quiz 3_9": {
                "Element Name": "SM quiz 3",
                "Quiz/Assignment": "SM Quiz 3",
                "grade": "3/4",
                "out_of": 4,
                "percentage": 3,
            },
            "bonus_4": {
                "Element Name": "bonus",
                "Quiz/Assignment": "SM Assignments",
                "grade": "1/1.5",
                "out_of": 1.5,
                "percentage": 1,
            },
            "discussion 1_6": {
                "Element Name": "discussion 1",
                "Quiz/Assignment": "",
                "grade": "4/5",
                "out_of": 5,
                "percentage": 4,
            },
            "discussion 2_1": {
                "Element Name": "discussion 2",
                "Quiz/Assignment": "",
                "grade": "5/5",
                "out_of": 5,
                "percentage": 5,
            },
            "discussion 3_8": {
                "Element Name": "discussion 3",
                "Quiz/Assignment": "",
                "grade": "2/5",
                "out_of": 5,
                "percentage": 2,
            },
            "questionnaire_0": {
                "Element Name": "questionnaire",
                "Quiz/Assignment": "SM Assignments",
                "grade": "4/5",
                "out_of": 5,
                "percentage": 4,
            },
            "report_7": {
                "Element Name": "report",
                "Quiz/Assignment": "",
                "grade": "3/5",
                "out_of": 5,
                "percentage": 3,
            },
        },
    },
    "midterm_results": {"General - Scientific Methods (A1) SM101": "78"},
    "subject_codes": {
        "Engineering 2nd Semester - CSEN202 Introduction to Computer Programming": "101",
        "Engineering 2nd Semester - ELCT201 Digital Logic Design": "202",
        "Engineering 2nd Semester - ENGD301 Engineering Drawing & Design": "303",
        "Engineering 2nd Semester - MATH203 Mathematics I": "404",
        "Engineering 2nd Semester - PHYS202 Physics II": "505",
        "General - DE202 Basic German 2": "606",
        "General - SM101 Scientific Methods (A1)": "707",
    },
}

guc_mockData = {
    "notifications": [
        {
            "body": "Dear Students,\n\nPlease note that next Wednesday will be a holiday. Our class will be moved to Tuesday during the first slot in Room A1.101.\n\nBest,\nDr. John Doe\n\nDr. John Doe\nDepartment: Computer Science",
            "date": "4/17/2025",
            "email_time": "2025-04-17T09:00:00",
            "id": "000001",
            "importance": "",
            "staff": "John Doe",
            "subject": "Mock: Lecture Schedule Change",
            "title": "Notification System: Mock Lecture Schedule Change",
        },
        {
            "body": "Hello everyone,\n\nWe will hold a make‑up tutorial on Monday 30.4.2025 in Room B2.202 during the second slot.\n\nRegards,\nDr. Jane Smith\n\nDr. Jane Smith\nDepartment: Physics",
            "date": "4/16/2025",
            "email_time": "2025-04-16T14:30:00",
            "id": "000002",
            "importance": "High",
            "staff": "Jane Smith",
            "subject": "Mock: Tutorial Compensation",
            "title": "Notification System: Mock Tutorial Compensation",
        },
        {
            "body": "Hi all,\n\nNo labs or tutorials on April 23 and 24. Normal schedule resumes April 25.\n\nThanks,\nDr. Alan Brown\n\nDr. Alan Brown\nDepartment: Engineering",
            "date": "4/16/2025",
            "email_time": "2025-04-16T08:15:00",
            "id": "000003",
            "importance": "High",
            "staff": "Alan Brown",
            "subject": "Mock: Labs and Tutorials Cancelled",
            "title": "Notification System: Mock Labs and Tutorials Cancelled",
        },
    ],
    "student_info": {
        "fullname": "Jane Q. Public",
        "mail": "<EMAIL>",
        "sg": "Mock Program",
        "uniqappno": "00-1234",
        "usercode": "U0001234",
    },
}

schedule_mockData = [
    {
        "Saturday": {
            "Fifth Period": {"Course_Name": "Free", "Location": "Free", "Type": "Free"},
            "First Period": {
                "Course_Name": "2ENG T038 ELCT 201",
                "Location": "D4.108",
                "Type": "Tut",
            },
            "Fourth Period": {
                "Course_Name": "2ENG T038 ENGD 301",
                "Location": "D4.309",
                "Type": "Tut",
            },
            "Second Period": {
                "Course_Name": "MATH 203  (2ENG L009)",
                "Location": "H16",
                "Type": "Lecture",
            },
            "Third Period": {
                "Course_Name": "ELCT 201  (2ENG L009)",
                "Location": "H16",
                "Type": "Lecture",
            },
        },
        "Sunday": {
            "Fifth Period": {"Course_Name": "Free", "Location": "Free", "Type": "Free"},
            "First Period": {
                "Course_Name": "2ENG IX+X 2G T031 DE 202",
                "Location": "C2.107",
                "Type": "Tut",
            },
            "Fourth Period": {
                "Course_Name": "MATH 203  (2ENG L009)",
                "Location": "H13",
                "Type": "Lecture",
            },
            "Second Period": {
                "Course_Name": "2ENG T038 MATH 203",
                "Location": "C5.106",
                "Type": "Tut",
            },
            "Third Period": {
                "Course_Name": "2 ENG IX SM T028 SM 101",
                "Location": "B4.107",
                "Type": "Tut",
            },
        },
        "Thursday": {
            "Fifth Period": {"Course_Name": "Free", "Location": "Free", "Type": "Free"},
            "First Period": {
                "Course_Name": "PHYS 202  (2ENG L009)",
                "Location": "H13",
                "Type": "Lecture",
            },
            "Fourth Period": {
                "Course_Name": "2ENG IX+X 2G T031 DE 202",
                "Location": "C2.205",
                "Type": "Tut",
            },
            "Second Period": {
                "Course_Name": "CSEN 202  (2ENG L009)",
                "Location": "H12",
                "Type": "Lecture",
            },
            "Third Period": {
                "Course_Name": "2ENG T038 PHYS 202",
                "Location": "C5.204",
                "Type": "Tut",
            },
        },
        "Wednesday": {
            "Fifth Period": {
                "Course_Name": "2ENG P038 CSEN 202",
                "Location": "D3.105",
                "Type": "Lab",
            },
            "First Period": {"Course_Name": "Free", "Location": "Free", "Type": "Free"},
            "Fourth Period": {
                "Course_Name": "2ENG T038 CSEN 202",
                "Location": "D3.104",
                "Type": "Tut",
            },
            "Second Period": {
                "Course_Name": "PHYS 202  (2ENG L009)",
                "Location": "H13",
                "Type": "Lecture",
            },
            "Third Period": {
                "Course_Name": "2ENG T038 MATH 203",
                "Location": "C5.101",
                "Type": "Tut",
            },
        },
    },
    {
        "0": "8:15AM-9:45AM",
        "1": "10:00AM-11:30AM",
        "2": "11:45AM-1:15PM",
        "3": "1:45PM-3:15PM",
        "4": "3:45PM-5:15PM",
    },
]

exam_mockData = [
    {
        "course": "-MOCK 100 Fundamentals of Testing - Fall 2025",
        "date": "1 - May - 2025",
        "end_time": "11:00:00 AM",
        "exam_day": "Friday",
        "hall": "M2.101",
        "season": "Fall 2025",
        "seat": "A12",
        "start_time": "9:00:00 AM",
        "type": "Normal",
    },
    {
        "course": "-MOCK 200 Data Simulation - Fall 2025",
        "date": "3 - May - 2025",
        "end_time": "2:30:00 PM",
        "exam_day": "Sunday",
        "hall": "M2.202",
        "season": "Fall 2025",
        "seat": "B07",
        "start_time": "12:00:00 PM",
        "type": "Normal",
    },
    {
        "course": "-MOCK 300 Lab Techniques - Fall 2025",
        "date": "5 - May - 2025",
        "end_time": "1:00:00 PM",
        "exam_day": "Tuesday",
        "hall": "L1.305",
        "season": "Fall 2025",
        "seat": "C18",
        "start_time": "11:00:00 AM",
        "type": "Normal",
    },
    {
        "course": "-MOCK 400 Systems Design - Fall 2025",
        "date": "7 - May - 2025",
        "end_time": "3:00:00 PM",
        "exam_day": "Thursday",
        "hall": "D3.102",
        "season": "Fall 2025",
        "seat": "D22",
        "start_time": "1:30:00 PM",
        "type": "Normal",
    },
    {
        "course": "-MOCK 500 Network Fundamentals - Fall 2025",
        "date": "9 - May - 2025",
        "end_time": "12:30:00 PM",
        "exam_day": "Saturday",
        "hall": "N4.210",
        "season": "Fall 2025",
        "seat": "E05",
        "start_time": "10:30:00 AM",
        "type": "Normal",
    },
    {
        "course": "-MOCK 600 Algorithm Workshop - Fall 2025",
        "date": "11 - May - 2025",
        "end_time": "2:00:00 PM",
        "exam_day": "Monday",
        "hall": "A1.112",
        "season": "Fall 2025",
        "seat": "F14",
        "start_time": "12:00:00 PM",
        "type": "Normal",
    },
]

# --- Mock Data ---
cmsdata_mockData = [
    {
        "course_name": "(|ADB101|) Introduction to English Literature (111)",
        "course_url": "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=111&sid=99",
        "season_name": "Fall 2025",
    },
    {
        "course_name": "(|CSEN401|) Algorithms and Data Structures (222)",
        "course_url": "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=222&sid=99",
        "season_name": "Spring 2026",
    },
    {
        "course_name": "(|DMET501|) Digital Media Concepts (333)",
        "course_url": "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=333&sid=99",
        "season_name": "Fall 2025",
    },
    {
        "course_name": "(|MGMT101|) Intro to Management (444)",
        "course_url": "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=444&sid=99",
        "season_name": "Spring 2026",
    },
]

cmscontent_mockData_111 = [
    # ... (original cmscontent_mockData list) ...
    {
        "announcement": "",
        "contents": [],
        "description": "Placeholder for layout",
        "week_name": "Mock Week",
    },
    {
        "announcement": "Use sample Mid-term Exam as a practice for for Midterm exam to be administered in week starting 3rd of April till 9th of April",
        "contents": [
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",
                "title": "1 - Inferences ppt. (Lecture slides)",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "2 - Inferences (VoD)",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "3 - Sample Midterm Exam (Assignment )",
            },
        ],
        "description": "Week 5 Sat 22nd March – till Thu 27th March",
        "week_name": "Week: 2025-3-22",
    },
    # ... (rest of original content) ...
]

# Define content for the second course (Algorithms)
cmscontent_mockData_222 = [
    {
        "announcement": "",
        "contents": [],
        "description": "Placeholder for layout",
        "week_name": "Mock Week",
    },
    {
        "announcement": "Reminder: Assignment 1 is due next week.",
        "contents": [
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "1 - Lecture 3: Sorting Algorithms (Slides)",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "2 - Tutorial 3: Sorting Practice Problems",
            },
        ],
        "description": "Week 3: Feb 10 - Feb 16, 2026",
        "week_name": "Week: 2026-2-10",
    },
    {
        "announcement": "Welcome to Algorithms!",
        "contents": [
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "1 - Course Syllabus Spring 2026",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "2 - Lecture 1: Introduction & Big O (Slides)",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "3 - Lecture 1: Recording (VoD)",
            },
        ],
        "description": "Week 1: Jan 27 - Feb 2, 2026",
        "week_name": "Week: 2026-1-27",
    },
]

# Define content for the third course (Digital Media)
cmscontent_mockData_333 = [
    {
        "announcement": "",
        "contents": [],
        "description": "Placeholder for layout",
        "week_name": "Mock Week",
    },
    {
        "announcement": "Project proposals due Week 4.",
        "contents": [
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "1 - Intro to Digital Storytelling (PDF)",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "2 - Examples of Digital Stories (Link Collection)",
            },
        ],
        "description": "Week 2: Sep 15 - Sep 21, 2025",
        "week_name": "Week: 2025-9-15",
    },
    {
        "announcement": "",
        "contents": [
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "1 - DMET501 Course Outline Fall 2025",
            },
        ],
        "description": "Week 1: Sep 8 - Sep 14, 2025",
        "week_name": "Week: 2025-9-8",
    },
]

# Define content for the fourth course (Management) - maybe only one week for variety
cmscontent_mockData_444 = [
    {
        "announcement": "",
        "contents": [],
        "description": "Placeholder for layout",
        "week_name": "Mock Week",
    },
    {
        "announcement": "First quiz next Tuesday covering Chapter 1.",
        "contents": [
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "1 - Chapter 1: Introduction to Management (Slides)",
            },
            {
                "download_url": "https://cms.guc.edu.eg/Uploads/62/16/41909/GUC_16_62_41909_2024-04-16T11_37_40.pdf",  # Same URL
                "title": "2 - Reading List",
            },
        ],
        "description": "Week 1: Jan 27 - Feb 2, 2026",
        "week_name": "Week: 2026-1-27",
    },
]


# --- Helper to map normalized URLs to content ---
# Make sure normalize_course_url is accessible or defined here/imported
# from utils.helpers import normalize_course_url # Assuming it's here


# Use the *actual* normalization function you use elsewhere
def normalize_course_url_local(url: str | None) -> str | None:
    """Placeholder: Use your actual normalize_course_url function"""
    if not url:
        return None
    # Example simple normalization (replace with your real one)
    return url.strip().replace("http://", "https://").lower()


# Create the mapping dictionary
mock_content_map = {
    normalize_course_url_local(
        "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=111&sid=99"
    ): cmscontent_mockData_111,
    normalize_course_url_local(
        "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=222&sid=99"
    ): cmscontent_mockData_222,
    normalize_course_url_local(
        "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=333&sid=99"
    ): cmscontent_mockData_333,
    normalize_course_url_local(
        "https://mock.guc.edu.eg/apps/student/CourseViewStn.aspx?id=444&sid=99"
    ): cmscontent_mockData_444,
}

# Filter out None keys in case normalization failed for some reason
mock_content_map = {k: v for k, v in mock_content_map.items() if k is not None}
