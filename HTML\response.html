<!DOCTYPE html>
<html>
    <head>
        <title>Student Portal SIS
</title>
        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta http-equiv="Content-Language" content="en"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no"/>
        <meta name="description" content="This is an example dashboard created using build-in elements and components."/>
        <meta name="msapplication-tap-highlight" content="no"/>
        <link href="/CSS/architectui-html-pro-1.4.0/main.87c0748b313a1dda75f5.css" rel="stylesheet"/>
        <style>
            .app-header__logo .logo-src {
                background: url(/CSS/architectui-html-pro-1.4.0/assets/images/guc_logo_og.png);
                background-repeat: no-repeat;
                width: 118px;
                height: 50px;
            }

            .app-sidebar {
                background-image: linear-gradient(to right, #00665c, #00665c ) !important;
            }

            .hamburger-inner, .hamburger-inner::before, .hamburger-inner::after {
                background-color: #00665c !important;
            }

            .main-menu {
                color: rgba(255,255,255,0.7) !important;
            }

            .main-menu:hover {
                background: rgba(255,255,255,0.15) !important;
                color: #fff !important;
            }

            .vertical-nav-menu ul > li > a.mm-active {
                color: #fff;
                background: rgba(255,255,255,0.15);
                font-weight: normal;
            }

            .vertical-nav-menu ul > li > a:hover {
                background: rgba(255,255,255,0.15) !important;
                color: #fff !important;
            }
        </style>
        <style>
            .datepicker-container.datepicker-dropdown.datepicker-top-left {
                z-index: 1500 !important;
            }
        </style>
        <style>
            .modalx {
                display: none;
                position: fixed;
                z-index: 1000;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                background: rgba( 255, 255, 255, .7 ) /*url('http://i.stack.imgur.com/FhHRx.gif')*/ 50% 50% no-repeat;
            }

            /* When the body has the loading class, we turn
               the scrollbar off with overflow:hidden */
            body.loading .modalx {
                overflow: hidden;
            }

            /* Anytime the body has the loading class, our
               modal element will be visible */
            body.loading .modalx {
                display: block;
            }
        </style>
        <style>
            .app-header__content {
                height: 90px !important;
            }

            .vertical-nav-menu ul > li > a {
                color: white !important;
            }
        </style>
        <style>
            .icon_application-form {
                background-image: url('/CSS/Icons/icons8-list-30.png');
                height: 30px !important;
                width: 30px !important;
                display: block;
                background-repeat: no-repeat;
                /* Other styles here */
            }

            .icon_double-down {
                background: url('/CSS/Icons/icons8-collapse-arrow-35.png');
                /*height: 25px !important;
                width: 25px !important;*/
                display: block;
                background-repeat: no-repeat;
                opacity: 0.7;
                /* Other styles here */
            }

            .icon_terms-and-conditions {
                background: url('/CSS/Icons/terms-and-conditions.png');
                height: 40px;
                width: 40px;
                display: block;
                background-repeat: no-repeat;
                opacity: 0.7;
                /* Other styles here */
            }

            .vertical-nav-menu i.metismenu-state-icon {
                height: 50%;
            }
        </style>
    </head>
    <body>
        <form method="post" action="./SearchAcademicScheduled_001.aspx" id="form1">
            <div class="aspNetHidden">
                <input type="hidden" name="__EVENTTARGET" id="__EVENTTARGET" value=""/>
                <input type="hidden" name="__EVENTARGUMENT" id="__EVENTARGUMENT" value=""/>
                <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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"/>
            </div>
            <script type="text/javascript">
                //<![CDATA[
                var theForm = document.forms['form1'];
                if (!theForm) {
                    theForm = document.form1;
                }
                function __doPostBack(eventTarget, eventArgument) {
                    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                        theForm.__EVENTTARGET.value = eventTarget;
                        theForm.__EVENTARGUMENT.value = eventArgument;
                        theForm.submit();
                    }
                }
                //]]>
            </script>
            <div class="aspNetHidden">
                <input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="6BCE89D6"/>
                <input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="4YnECSlZXTjcb4X8l57Z48EeUw+RiHC8MOjhexZVqbYUI7f3q2pK9LYpwsS+N9Gc4OvUCy6yO/xsI427hxFa5UCzB90a5L7IkSEhWoXvTnNKQgrJVH8awh52Zxt8JgmLyj4kbndS1Tr/2RYnaQU7vKeaBybkDoDaPbEOXaqVWLc="/>
            </div>
            <div class="app-container app-theme-white body-tabs-shadow fixed-header fixed-sidebar">
                <div class="app-header header-shadow d-print-none ">
                    <div class="app-header__logo">
                        <div class="logo-src"></div>
                        <div class="header__pane ml-auto">
                            <div>
                                <button type="button" class="hamburger close-sidebar-btn hamburger--elastic" data-class="closed-sidebar">
                                    <span class="hamburger-box">
                                        <span class="hamburger-inner"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="app-header__mobile-menu">
                        <div>
                            <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                                <span class="hamburger-box">
                                    <span class="hamburger-inner"></span>
                                </span>
                            </button>
                        </div>
                    </div>
                    <div class="app-header__menu">
                        <span>
                            <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                                <span class="btn-icon-wrapper">
                                    <i class="fa fa-ellipsis-v fa-w-6"></i>
                                </span>
                            </button>
                        </span>
                    </div>
                    <div class="app-header__content">
                        <div class="app-header-left text-center timeline-title">
                            <h4>Student Portal - SIS</h4>
                        </div>
                        <div class="app-header-right">
                            <div class="header-dots"></div>
                            <div class="widget-content-right ml-3 header-user-info ">
                                <h5>
                                    <span id="ContentPlaceHolderperson_LabelPageInactive" style="color:Red;"></span>
                                </h5>
                            </div>
                            <div class="header-btn-lg pr-0">
                                <div class="widget-content p-0">
                                    <div class="widget-content-wrapper">
                                        <div class="widget-content-left">
                                            <div class="btn-group">
                                                <a data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="p-0 btn">
                                                    Hello,
                            <span>GUC\mohamed.elsaadi</span>
                                                    <a id="ContentPlaceHolderperson_LinkButtonactor" href="javascript:__doPostBack(&#39;ctl00$ctl00$ContentPlaceHolderperson$LinkButtonactor&#39;,&#39;&#39;)"></a>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="widget-content-left  ml-3 header-user-info">
                                            <div class="widget-heading">
                                                <img width="42" class="rounded-circle" src="/CSS/architectui-html-pro-1.4.0/assets/images/images.png" alt="">
                                            </div>
                                            <div class="widget-subheading"></div>
                                        </div>
                                        <div class="widget-content-right header-user-info ml-3"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="header-btn-lg"></div>
                        </div>
                    </div>
                </div>
                <div class="app-main">
                    <div class="app-sidebar sidebar-shadow">
                        <div class="app-header__logo">
                            <div class="logo-src"></div>
                            <div class="header__pane ml-auto">
                                <div>
                                    <button type="button" class="hamburger close-sidebar-btn hamburger--elastic" data-class="closed-sidebar">
                                        <span class="hamburger-box">
                                            <span class="hamburger-inner"></span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="app-header__mobile-menu">
                            <div>
                                <button type="button" class="hamburger hamburger--elastic mobile-toggle-nav">
                                    <span class="hamburger-box">
                                        <span class="hamburger-inner"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                        <div class="app-header__menu">
                            <span>
                                <button type="button" class="btn-icon btn-icon-only btn btn-primary btn-sm mobile-toggle-header-nav">
                                    <span class="btn-icon-wrapper">
                                        <i class="fa fa-ellipsis-v fa-w-6"></i>
                                    </span>
                                </button>
                            </span>
                        </div>
                        <div class="scrollbar-sidebar" style="overflow-y: scroll !important; overflow-x:hidden !important;">
                            <div class="app-sidebar__inner">
                                <div class="p-3">
                                    <input type="text" id="search-input" placeholder="Menu Search..." class="form-control">
                                </div>
                                <ul class="vertical-nav-menu" id="menu">
                                    <li class="app-sidebar__heading"></li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Main
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/index.aspx">DashBoard
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Main/Notifications.aspx">Your Notification
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Main/StudentFeedback.aspx">System FeedBack
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/UserProfile/UserProfileSearch.aspx">Search for staff
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Attendance
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Attendance/ClassAttendance_ViewStudentAttendance_001.aspx">View Attendance
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Berlin
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Berlin/Default.aspx">Study Abroad
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Course
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/CourseWork/Upload_001.aspx">Upload Course Work

                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Evaluation
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Evaluation/EvaluateCourse.aspx">Evaluate Course
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Evaluation/EvaluateStaff.aspx">Evaluate Staff
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Exam
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Exam/ExamExcuseFinal.aspx">Final Exam Excuse 
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Exam/MakeupRegistration.aspx">Makeup Registration
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Exam/ViewExamSeat_01.aspx">Exam Seats
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Financial
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Financial/BalanceView_001.aspx">Financial Balance
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Grade
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Grade/CheckGrade_01.aspx">Check Grades
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Grade/CheckGradePerviousSemester_01.aspx">Check Previous Grades
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Grade/Transcript_001.aspx">Transcript
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Registration
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Registration/ChoosePharmacyProject_001.aspx">Pharmacy Project
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/ChoosePharmacyTrainingGroup_001.aspx">Pharmacy Training
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/CourseRegistrationView_001.aspx">Registered Courses 
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/ElectiveNavigation.aspx">Declare Elective/Seminar/Track
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/ExtraCourseReg.aspx">Placement Test Registration
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/LawRegistration.aspx">Law Registration
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/MajorNavigation.aspx">Declare Faculty/Major

                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/SummerDashboard_001.aspx">Summer System
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Registration/SummerLanguageCoursesRegistration_001.aspx">Summer Language
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Reservation
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Reservations/Bus/Reservation.aspx">Bus Reservation
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Reservations/GermanPlacementReservation.aspx">German Placement Reservation
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Reservations/Session/SessionReservation_001.aspx">Session Reservation
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Reservations/TripReservation_01.aspx">Trip Reservation
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Scheduling
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Scheduling/ChangeGroupRequest.aspx">Change Group
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Scheduling/GeneralGroupSchedule.aspx">General Group Schedule
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Scheduling/GroupSchedule.aspx">Your Schedule
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Scheduling/SearchAcademicScheduled_001.aspx">Academic Schedule
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#" class="main-menu ">
                                            <i class="metismenu-icon icon_application-form"></i>
                                            Thesis
                    <i class="metismenu-state-icon caret-left icon_double-down"></i>
                                        </a>
                                        <ul>
                                            <li>
                                                <a href="/student_ext/Thesis/Internship.aspx">Internship
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Thesis/ThesisNavigation.aspx">Select Thesis
                            </a>
                                            </li>
                                            <li>
                                                <a href="/student_ext/Thesis/UploadThesisTopicFile_01.aspx">Upload Thesis Topic File
                            </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="app-main__outer">
                        <div class="app-main__inner">
                            <div class="">
                                <div class="">
                                    <div class=""></div>
                                    <div class="">
                                        <div class="app-inner-layout__header bg-heavy-rain">
                                            <div class="app-page-title">
                                                <div class="page-title-wrapper">
                                                    <div class="page-title-heading p-2">
                                                        <div class="page-title-icon ">
                                                            <i class="icon_terms-and-conditions"></i>
                                                        </div>
                                                        <div>
                                                            <span id="ContentPlaceHolderright_LabelPageName" class="h5">Academic Schedule</span>
                                                            <br/>
                                                            <span id="ContentPlaceHolderright_Labelmasterpagemessage" style="font-size:16px;color:red;"></span>
                                                            <div class="page-title-subheading"></div>
                                                        </div>
                                                    </div>
                                                    <div class="page-title-actions d-print-none">
                                                        <div class="p-2">
                                                            <a href="https://apps.guc.edu.eg/www/BRM/UserEntries.aspx" data-toggle="tooltip" title="" data-placement="bottom" class="btn-shadow mr-3 btn btn-dark" data-original-title="Please contribute with us to enhance your experience">
                                                                <i class="fa fa-stack fa-star"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="row">
                                                    <div class="col-xl-12  col-lg-12 col-md-12 col-sm-12">
                                                        <div class="main-card mb-3 card text-left">
                                                            <div class="card-header">Usage Guide Lines (Searching and/or Adding Course(s)):</div>
                                                            <div class="card-body">
                                                                <h3>View All Course/Staff Schedules (Compensation)</h3>
                                                                <br/>
                                                                <ul>
                                                                    <li>
                                                                        To add courses (if needed): please Click 
                                                  the <span class="add_link icon_link">+</span>
                                                                        symbol next to <b>Courses</b>
                                                                        <br>
                                                                        Note that filtering is optional, you can use 
                                                  it to find quickly a course by typing a part from its name or code 
                                                  into the white box provided. 
                                                  
                                                                        <li>
                                                                            To add instructor or TA, Click the <span class="add_link icon_link">+</span>
                                                                            symbol 
                                                  next to <b>Staff</b>
                                                                            (if needed), also you 
                                                  can use the provided filter to quickly find the instructor 
                                                  
                                                                        <li>
                                                                            If you change your mind, simply click 
                                                  <span class=" icon_link">x</span>
                                                                            symbol 
                                                  preceding the filter to remove this choice 
                                                  
                                                                        <li>
                                                                            Finally, Click on Show Schedule to View 
                                                  all selected courses and instructor’s scheduled slots
                                                      <br>
                                                                            e.g., to view slots of course A taught by 
                                                  staff X and slots of course B taught by staff Y, add the courses A 
                                                  and B, and staff members X and Y 
                                                  
                                                                            <li>
                                                                                Move your mouse cursor over any slot to 
                                                  see all slots for that course and all slots assigned to that 
                                                  instructor. Courses will be highlighted in <span class="active-course">red</span>
                                                                                while the 
                                                  instructor’s will be in <span class="active-staff">yellow</span>
                                                                            </li>
                                                                </ul>
                                                                <p></p>
                                                                <p>Filtering tips: 
                                                
                                                                <ul>
                                                                    <li>To select a specific course by code, 
                                                  simply enter its code in the filter and immediately the Courses 
                                                  list will be updated 
                                                  
                                                                    <li>
                                                                        This system filters are case 
                                                  insensitive<br>
                                                                        e.g., if you typed: CSEN 202 
                                                  is just like typing csen 202 
                                                  
                                                                        <li>
                                                                            Wildcard symbol: <b>(.*)</b>
                                                                        <li>Protip: you can use regular 
                                                  expressions</li>
                                                                </ul>
                                                                <br/>
                                                                <div class="text-center">
                                                                    <p>
                                                                        Courses: <a class="icon_link add_link" href="javascript: generate_dropdown(courses, '#courses_list', 'course[]')">+</a>
                                                                    </p>
                                                                    <div id="courses_list" class="to_add"></div>
                                                                    <p>
                                                                        Staff: <a class="icon_link add_link" href="javascript: generate_dropdown(tas, '#teaching_assistants', 'ta[]')">+</a>
                                                                    </p>
                                                                    <div id="teaching_assistants"></div>
                                                                    <p>
                                                                        <input type="submit" name="ctl00$ctl00$ContentPlaceHolderright$ContentPlaceHoldercontent$B_ShowSchedule" value="Show Schedule" id="ContentPlaceHolderright_ContentPlaceHoldercontent_B_ShowSchedule" class="btn btn-primary" style="z-index: 0"/>
                                                                        <br>
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <div class="d-block text-right card-footer"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <br/>
                                                <div class="row">
                                                    <div class="col-xl-12  col-lg-12 col-md-12 col-sm-12">
                                                        <div class="main-card mb-3 card text-left">
                                                            <div class="card-header">
                                                                Schedule:
							
                                                                <h6>
                                                                    <span id="ContentPlaceHolderright_ContentPlaceHoldercontent_LblStaffName">Abbas Yehia</span>
                                                                </h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <table id="ContentPlaceHolderright_ContentPlaceHoldercontent_schedule" class="schedule table table-bordered">
                                                                    <tr>
                                                                        <th>&nbsp;</th>
                                                                        <th>
                                                                            1<sup>st</sup>
                                                                            First
                                                                        </th>
                                                                        <th>
                                                                            2<sup>nd</sup>
                                                                            Second
                                                                        </th>
                                                                        <th>
                                                                            3<sup>rd</sup>
                                                                            Third
                                                                        </th>
                                                                        <th>
                                                                            4<sup>th</sup>
                                                                            Fourth
                                                                        </th>
                                                                        <th>
                                                                            5<sup>th</sup>
                                                                            Fifth
                                                                        </th>
                                                                        <th>
                                                                            6<sup>th</sup>
                                                                            Sixth
                                                                        </th>
                                                                        <th>
                                                                            7<sup>th</sup>
                                                                            Seventh
                                                                        </th>
                                                                        <th>
                                                                            8<sup>th</sup>
                                                                            Eighth
                                                                        </th>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Saturday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>
                                                                            &nbsp;
                                                                            <div class="slot" data-staff-id='2560' data-course-id='1523'>
                                                                                <dl>
                                                                                    <dt>Group</dt>
                                                                                    <dd class='course-1523'>MATS  911 - 10EMS-EL1 (Lecture)</dd>
                                                                                    <dt>Location</dt>
                                                                                    <dd>D4.301</dd>
                                                                                    <dt>Staff</dt>
                                                                                    <dd class='staff-2560'>Abbas Yehia</dd>
                                                                                </dl>
                                                                                <hr/>
                                                                            </div>
                                                                        </td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Sunday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Monday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Tuesday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Wednesday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Thursday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Friday</th>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                        <td>&nbsp;</td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                            <div class="d-block text-right card-footer"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <br/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="app-drawer-wrapper">
                <div class="drawer-nav-btn">
                    <button type="button" class="hamburger hamburger--elastic is-active">
                        <span class="hamburger-box">
                            <span class="hamburger-inner"></span>
                        </span>
                    </button>
                </div>
                <div class="drawer-content-wrapper">
                    <div class="scrollbar-container">
                        <h3 class="drawer-heading">ShortCuts</h3>
                        <div class="drawer-section">
                            <div class="row"></div>
                            <div class="divider"></div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" name="ctl00$ctl00$div_position" id="div_position" value="0"/>
            <script type=text/javascript>
                var courses = [{
                    'id': '1155',
                    'value': 'ABSK 901: Academic Business Skills I'
                }, {
                    'id': '1156',
                    'value': 'ABSK 902: Academic Business Skills II'
                }, {
                    'id': '13',
                    'value': 'AE 101: Introduction to Academic English (B)'
                }, {
                    'id': '2090',
                    'value': 'ARAB 201: Arabic For Law II (A)'
                }, {
                    'id': '2091',
                    'value': 'ARAB 202: Arabic For Law II (B)'
                }, {
                    'id': '2954',
                    'value': 'ARAB 203: Arabic For Law I (A)'
                }, {
                    'id': '2955',
                    'value': 'ARAB 204: Arabic For Law I (B)'
                }, {
                    'id': '1886',
                    'value': 'ARCH  800: Architecture Bachelor Thesis'
                }, {
                    'id': '1864',
                    'value': 'ARCH 1001: Design Studio VII - International Urban Design and Landscaping Project'
                }, {
                    'id': '1865',
                    'value': 'ARCH 1002: Design Studio VII - Working Drawings'
                }, {
                    'id': '1866',
                    'value': 'ARCH 1003: Advanced Theory of Architecture and Urban Design'
                }, {
                    'id': '2927',
                    'value': 'ARCH 1048: Place Making Play in Cities'
                }, {
                    'id': '2928',
                    'value': 'ARCH 1049: Emergency Architecture - Displacement Urbanism'
                }, {
                    'id': '2930',
                    'value': 'ARCH 1050: SPATIAL INFUSIONS-Ephemeral impact in permanent environments'
                }, {
                    'id': '3026',
                    'value': 'ARCH 1051: The Aura and The Skin'
                }, {
                    'id': '3027',
                    'value': 'ARCH 1052: Live Cycle Earth Constructions'
                }, {
                    'id': '3028',
                    'value': 'ARCH 1053: Visual Storytelling in Cairo'
                }, {
                    'id': '1504',
                    'value': 'ARCH 202: History of Architecture I'
                }, {
                    'id': '1505',
                    'value': 'ARCH 203: Visual Design'
                }, {
                    'id': '1506',
                    'value': 'ARCH 204: Introduction to Architectural Design'
                }, {
                    'id': '1507',
                    'value': 'ARCH 205: Building Technology I(Building Construction, Building Physics, Building Materials, Lighting, Acoustics, Technical Installations)'
                }, {
                    'id': '1554',
                    'value': 'ARCH 206: 3D Model-Making Studio'
                }, {
                    'id': '1550',
                    'value': 'ARCH 303: Theory of Architecture I'
                }, {
                    'id': '1609',
                    'value': 'ARCH 401: History of Architecture III'
                }, {
                    'id': '1610',
                    'value': 'ARCH 402: CAD / CAM II'
                }, {
                    'id': '1611',
                    'value': 'ARCH 404: Building Technology III'
                }, {
                    'id': '1612',
                    'value': 'ARCH 405: Design Studio II'
                }, {
                    'id': '1613',
                    'value': 'ARCH 406: Concrete Structures Design'
                }, {
                    'id': '1629',
                    'value': 'ARCH 601: CAAD/CAM IV'
                }, {
                    'id': '1630',
                    'value': 'ARCH 602: Design Studio IV (selected Urban and Architectural Design Projects)'
                }, {
                    'id': '1631',
                    'value': 'ARCH 603: Surveying'
                }, {
                    'id': '1858',
                    'value': 'ARCH 901: Design Studio VI - Urban Design and Landscaping'
                }, {
                    'id': '2099',
                    'value': 'ARCH 918: Introduction to Interior Design'
                }, {
                    'id': '14',
                    'value': 'AS 102: English for Academic Purposes (A1)'
                }, {
                    'id': '199',
                    'value': 'BINF 201: Introduction to business informatics'
                }, {
                    'id': '486',
                    'value': 'BINF 405: Information and Communication Architecture II'
                }, {
                    'id': '2708',
                    'value': 'BINF 406: Digital Transformation'
                }, {
                    'id': '597',
                    'value': 'BINF 506: Research Methodology for BINF'
                }, {
                    'id': '626',
                    'value': 'BINF 609: Enterprise System Applications'
                }, {
                    'id': '808',
                    'value': 'BINF 720: Seminar in Business Informatics'
                }, {
                    'id': '1008',
                    'value': 'BINF 820: Bachelor Project'
                }, {
                    'id': '1186',
                    'value': 'BINF 901: Business Process Management'
                }, {
                    'id': '2963',
                    'value': 'BIOT 1001: Selected Advanced Specialized Topics I'
                }, {
                    'id': '2964',
                    'value': 'BIOT 1002: Selected Advanced Specialized Topics II'
                }, {
                    'id': '2965',
                    'value': 'BIOT 1003: Selected Advanced Specialized Topics III'
                }, {
                    'id': '896',
                    'value': 'BIOT 601: Introduction to Management'
                }, {
                    'id': '895',
                    'value': 'BIOT 611: Biophysics'
                }, {
                    'id': '892',
                    'value': 'BIOT 651: Introduction to Biosafety'
                }, {
                    'id': '891',
                    'value': 'BIOT 681: Bioinformatics'
                }, {
                    'id': '2501',
                    'value': 'BIOT 693: Technical Chemistry & Process Engineering'
                }, {
                    'id': '2938',
                    'value': 'BIOT 810: Bioinformatics II'
                }, {
                    'id': '902',
                    'value': 'BIOT 899: Bachelor Thesis'
                }, {
                    'id': '2503',
                    'value': 'BIOTp 633: Genetics & Genetic Engineering I'
                }, {
                    'id': '2502',
                    'value': 'BIOTp 643: Cell Biology'
                }, {
                    'id': '2500',
                    'value': 'BIOTt 633: Genetics & Genetic Engineering I'
                }, {
                    'id': '2499',
                    'value': 'BIOTt 643: Cell Biology'
                }, {
                    'id': '1868',
                    'value': 'BTECH 1001: Design Studio VII - Architecture'
                }, {
                    'id': '1869',
                    'value': 'BTECH 1002: Design Studio VII - Working Drawings'
                }, {
                    'id': '1870',
                    'value': 'BTECH 1003: Renewable Energy Technologies in Buildings'
                }, {
                    'id': '2317',
                    'value': 'BTECH 1008: Building Information Modeling'
                }, {
                    'id': '2929',
                    'value': 'BTECH 1018: Bio-Structures'
                }, {
                    'id': '1190',
                    'value': 'CBD 201: Digital Media II'
                }, {
                    'id': '1192',
                    'value': 'CBD 402: Web Design'
                }, {
                    'id': '1193',
                    'value': 'CGD 401: Computer Tools Graphics'
                }, {
                    'id': '457',
                    'value': 'CHEM 401: Physical Chemistry'
                }, {
                    'id': '2480',
                    'value': 'CICO 801: Enforcement'
                }, {
                    'id': '1795',
                    'value': 'CIG  602: Construction Management I'
                }, {
                    'id': '1799',
                    'value': 'CIG  604: Building and High-rise Construction'
                }, {
                    'id': '1839',
                    'value': 'CIG 1001: Numerical Methods and Advanced Statistics'
                }, {
                    'id': '1838',
                    'value': 'CIG 1002: GIS for Civil Engineering Applications'
                }, {
                    'id': '1908',
                    'value': 'CIG 1004: Environmental Impact Assessment'
                }, {
                    'id': '1843',
                    'value': 'CIG 1005: Construction Management III'
                }, {
                    'id': '3014',
                    'value': 'CIG 1018: Claims and Dispute Management in Construction Project'
                }, {
                    'id': '1555',
                    'value': 'CIG 201: Civil Engineering Drawing'
                }, {
                    'id': '1558',
                    'value': 'CIG 403: Plane Surveying'
                }, {
                    'id': '2084',
                    'value': 'CILA 202: Legal Research'
                }, {
                    'id': '2085',
                    'value': 'CILA 203: Legal Research and Drafting Legal Opinion'
                }, {
                    'id': '2175',
                    'value': 'CILA 301: Civil Law (Theory of Contract)'
                }, {
                    'id': '2497',
                    'value': 'CILA 603: Law of Civil and Commercial Procedures'
                }, {
                    'id': '2403',
                    'value': 'CILA 701: Social Insurance Law'
                }, {
                    'id': '2483',
                    'value': 'CILA 801: International Intellectual Property Rights'
                }, {
                    'id': '2484',
                    'value': 'CILA 802: Civil Law (Ownership & Derived rights)'
                }, {
                    'id': '1844',
                    'value': 'CIS 1003: Special Topics in Reinforced Concrete Design'
                }, {
                    'id': '1603',
                    'value': 'CIS 401: Properties and Testing of Materials II'
                }, {
                    'id': '1602',
                    'value': 'CIS 402: Structural Analysis II'
                }, {
                    'id': '1644',
                    'value': 'CIS 601: Steel Structures Design II'
                }, {
                    'id': '1640',
                    'value': 'CIS 603: Design of Reinforced Concrete Structures II'
                }, {
                    'id': '1710',
                    'value': 'CIS 606: Geotechnical and Foundation Engineering'
                }, {
                    'id': '1711',
                    'value': 'CIS 607: Matrix Analysis in Structural Engineering'
                }, {
                    'id': '1608',
                    'value': 'CIT 401: Transportation Systems'
                }, {
                    'id': '1712',
                    'value': 'CIT 603: Highway and Railway Design'
                }, {
                    'id': '1637',
                    'value': 'CIW 402: Fluid Mechanics'
                }, {
                    'id': '1647',
                    'value': 'CIW 601: Hydraulics'
                }, {
                    'id': '2911',
                    'value': 'CIW 903: River Engineering'
                }, {
                    'id': '3033',
                    'value': 'CLPH  1202: Pharmacotherapy II'
                }, {
                    'id': '3035',
                    'value': 'CLPH  1204: Clinical Nutrition'
                }, {
                    'id': '2933',
                    'value': 'CLPH 1032: Pharmacoepidemiology & Economy'
                }, {
                    'id': '2650',
                    'value': 'CLPH 106: Clinical Nutrition'
                }, {
                    'id': '3032',
                    'value': 'CLPH 1201: Health Informatics II'
                }, {
                    'id': '3034',
                    'value': 'CLPH 1203: Selected Clinical Topics II'
                }, {
                    'id': '2647',
                    'value': 'CLPH 301: Pharmacotherapy III'
                }, {
                    'id': '2936',
                    'value': 'CLPHp 1023: Pharmacotherapy'
                }, {
                    'id': '2776',
                    'value': 'CLPHp 811: Clinical Pharmacy Practice I'
                }, {
                    'id': '2932',
                    'value': 'CLPHt 1023: Pharmacotherapy'
                }, {
                    'id': '2775',
                    'value': 'CLPHt 811: Clinical Pharmacy Practice I'
                }, {
                    'id': '1194',
                    'value': 'CMD 401: Computer Tools Media'
                }, {
                    'id': '2034',
                    'value': 'CMLA 101: Introduction to Common Law'
                }, {
                    'id': '2079',
                    'value': 'CMLA 201: Political Systems'
                }, {
                    'id': '2324',
                    'value': 'CMLA 401: Administrative Law'
                }, {
                    'id': '2384',
                    'value': 'CMLA 601: Administrative Law II'
                }, {
                    'id': '2322',
                    'value': 'COLA 401: The Legal Aspects of International Trade Law and WTO'
                }, {
                    'id': '2380',
                    'value': 'COLA 601: Commercial Law (Companies and Investment Law)'
                }, {
                    'id': '2479',
                    'value': 'COLA 801: International Commercial Arbitration'
                }, {
                    'id': '2485',
                    'value': 'COLA 803: Securities regulation in a Comparative perspective'
                }, {
                    'id': '3029',
                    'value': 'COLA 804: Commercial Law (Banking Operations & Financial Law)'
                }, {
                    'id': '634',
                    'value': 'COMM 1001: Modulation and Coding'
                }, {
                    'id': '635',
                    'value': 'COMM 1002: Adaptive Antennas'
                }, {
                    'id': '636',
                    'value': 'COMM 1003: Information Theory'
                }, {
                    'id': '637',
                    'value': 'COMM 1005: Advanced Communication Lab'
                }, {
                    'id': '2566',
                    'value': 'COMM 1018: Future Mobile Technologies'
                }, {
                    'id': '401',
                    'value': 'COMM 401: Signal & System Theory'
                }, {
                    'id': '76',
                    'value': 'COMM 402: Electromagnetics'
                }, {
                    'id': '182',
                    'value': 'COMM 601: Modulation I'
                }, {
                    'id': '181',
                    'value': 'COMM 602: Digital Signal Processing'
                }, {
                    'id': '183',
                    'value': 'COMM 603: Radio Frequency Engineering'
                }, {
                    'id': '184',
                    'value': 'COMM 604: Channel Coding'
                }, {
                    'id': '186',
                    'value': 'COMM 606: Communication Lab'
                }, {
                    'id': '1195',
                    'value': 'CPD 401: Computer Tools Product'
                }, {
                    'id': '101',
                    'value': 'CPS 402: Communication & Presentation Skills (A2)'
                }, {
                    'id': '2082',
                    'value': 'CRLA 201: Criminology and Penology'
                }, {
                    'id': '2325',
                    'value': 'CRLA 401: Administrative Law (Constitutional & Legal Framework)'
                }, {
                    'id': '2326',
                    'value': 'CRLA 402: Criminal Law (General Rules)'
                }, {
                    'id': '2498',
                    'value': 'CRLA 603: Moot Court Including visiting courts and Legal Institutions'
                }, {
                    'id': '1699',
                    'value': 'CSEN  1034: Seminar on Rational Belief Change'
                }, {
                    'id': '628',
                    'value': 'CSEN 1001: Computer and Network Security'
                }, {
                    'id': '629',
                    'value': 'CSEN 1002: Advanced Computer Lab'
                }, {
                    'id': '632',
                    'value': 'CSEN 1003: Compiler'
                }, {
                    'id': '1157',
                    'value': 'CSEN 1008: Seminar on Intelligent Transportation Systems'
                }, {
                    'id': '350',
                    'value': 'CSEN 102: Introduction to computer science'
                }, {
                    'id': '1766',
                    'value': 'CSEN 1038: Advanced Data structures and Algorithms'
                }, {
                    'id': '2093',
                    'value': 'CSEN 1076: Natural Language Processing and Information Retrieval'
                }, {
                    'id': '2368',
                    'value': 'CSEN 1088: Seminar in Smart Cities'
                }, {
                    'id': '2676',
                    'value': 'CSEN 1118: Seminar in Machine Learning and AI for Art'
                }, {
                    'id': '2748',
                    'value': 'CSEN 1126: Seminar in Hand-drawn Sketches in Artificial Intelligence'
                }, {
                    'id': '2749',
                    'value': 'CSEN 1127: Seminar in Healthcare Technology'
                }, {
                    'id': '2750',
                    'value': 'CSEN 1128: Seminar in Machine Learning Applications in Bioinformatics'
                }, {
                    'id': '2918',
                    'value': 'CSEN 1134: Seminar on Unsupervised Classification'
                }, {
                    'id': '2919',
                    'value': 'CSEN 1135: Seminar on Brain Tumor Detection Out of MRI Scans'
                }, {
                    'id': '3021',
                    'value': 'CSEN 1139: Seminar in Leveraging Large Language Models for Practical Applications'
                }, {
                    'id': '3022',
                    'value': 'CSEN 1140: Seminar in Graph Neural Networks: Theory and Applications'
                }, {
                    'id': '3023',
                    'value': 'CSEN 1141: Seminar on Remote Sensing Image Analysis'
                }, {
                    'id': '3024',
                    'value': 'CSEN 1142: Seminar in Innovations in Medical Imaging'
                }, {
                    'id': '3025',
                    'value': 'CSEN 1143: Seminar on XR in Healthcare and Therapy'
                }, {
                    'id': '19',
                    'value': 'CSEN 202: Introduction to Computer Programming'
                }, {
                    'id': '402',
                    'value': 'CSEN 401: Computer Programming Lab'
                }, {
                    'id': '717',
                    'value': 'CSEN 402: Computer Organization and System Programming'
                }, {
                    'id': '80',
                    'value': 'CSEN 403: Concepts of Programming Languages'
                }, {
                    'id': '510',
                    'value': 'CSEN 404: Introduction to Networks'
                }, {
                    'id': '174',
                    'value': 'CSEN 601: Computer System Architecture'
                }, {
                    'id': '175',
                    'value': 'CSEN 602: Operating Systems'
                }, {
                    'id': '178',
                    'value': 'CSEN 603: Software Engineering'
                }, {
                    'id': '436',
                    'value': 'CSEN 603: Software Engineering'
                }, {
                    'id': '437',
                    'value': 'CSEN 604: Data Bases II'
                }, {
                    'id': '563',
                    'value': 'CSEN 907: Knowledge Representation And Reasoning'
                }, {
                    'id': '26',
                    'value': 'CSIS 202: Introduction to Computer Science II'
                }, {
                    'id': '77',
                    'value': 'CSIS 402: Computer Organization and System Programming'
                }, {
                    'id': '2947',
                    'value': 'CSTH 1202: Cosmetics Business Essentials'
                }, {
                    'id': '3037',
                    'value': 'CSTH 1205: Cosmetics Formulation Technology II'
                }, {
                    'id': '3038',
                    'value': 'CSTH 1206: Marketing Innovations with AI Integration'
                }, {
                    'id': '3039',
                    'value': 'CSTH 1207:  Graduation Project'
                }, {
                    'id': '1877',
                    'value': 'CTRL 1901: Advanced Accounting'
                }, {
                    'id': '142',
                    'value': 'CTRL 202: Financial Accounting II'
                }, {
                    'id': '93',
                    'value': 'CTRL 402: Management Accounting II'
                }, {
                    'id': '160',
                    'value': 'CTRL 606: Taxation'
                }, {
                    'id': '221',
                    'value': 'CTRL 705: Seminar in Management Control'
                }, {
                    'id': '223',
                    'value': 'CTRL 707: Internal Auditing and Risk Management I'
                }, {
                    'id': '1703',
                    'value': 'CTRL 710: Cost Accounting'
                }, {
                    'id': '1953',
                    'value': 'CTRL 712: Financial Reporting and Analysis'
                }, {
                    'id': '1952',
                    'value': 'CTRL 714: Intermediate Accounting II'
                }, {
                    'id': '286',
                    'value': 'CTRL 720: Seminar in Accounting (1st Major seminar)'
                }, {
                    'id': '1581',
                    'value': 'CTRL 801: Management Control Thesis'
                }, {
                    'id': '509',
                    'value': 'CTRL 820: Seminar in Accounting (2nd Major seminar)'
                }, {
                    'id': '37',
                    'value': 'CTRL 901: Advanced Accounting'
                }, {
                    'id': '1112',
                    'value': 'DD 201: Bionic and Perception'
                }, {
                    'id': '15',
                    'value': 'DE 101: Basic German 1'
                }, {
                    'id': '33',
                    'value': 'DE 202: Basic German 2'
                }, {
                    'id': '64',
                    'value': 'DE 303: Basic German 3'
                }, {
                    'id': '73',
                    'value': 'DE 404: Basic German 4'
                }, {
                    'id': '630',
                    'value': 'DMET 1001: Image Processing'
                }, {
                    'id': '631',
                    'value': 'DMET 1002: Advanced Media Lab'
                }, {
                    'id': '633',
                    'value': 'DMET 1003: Audio and Acoustics'
                }, {
                    'id': '1890',
                    'value': 'DMET 1042: Fundamentals of VoIP'
                }, {
                    'id': '2069',
                    'value': 'DMET 1057: Seminar in Selected Applications of Media Processing'
                }, {
                    'id': '2299',
                    'value': 'DMET 1061: Seminar in Computer Vision and Image Understanding'
                }, {
                    'id': '2461',
                    'value': 'DMET 1067: Deep Learning in Computer Vision'
                }, {
                    'id': '2672',
                    'value': 'DMET 1072: Computer Animation'
                }, {
                    'id': '3013',
                    'value': 'DMET 1075: Introduction to Augmented and Virtual Reality'
                }, {
                    'id': '3017',
                    'value': 'DMET 1076: Seminar on Deep Learning for Computer Vision'
                }, {
                    'id': '3019',
                    'value': 'DMET 1077: Seminar on Advanced Applications of LL and Vision Models'
                }, {
                    'id': '438',
                    'value': 'DMET 601: Web Technologies and Usability'
                }, {
                    'id': '403',
                    'value': 'DMET 602: Network & Media lab'
                }, {
                    'id': '1071',
                    'value': 'DMET 603: Digital Signal Processing'
                }, {
                    'id': '2656',
                    'value': 'DTSC 104: Advanced Methods in Data Science '
                }, {
                    'id': '2657',
                    'value': 'DTSC 105: Capstone Project '
                }, {
                    'id': '2659',
                    'value': 'DTSC 107: Text Mining '
                }, {
                    'id': '2660',
                    'value': 'DTSC 108: Advanced Big Data Analytics Technologies  '
                }, {
                    'id': '3031',
                    'value': 'DTSC 110: Financial Technologies'
                }, {
                    'id': '1878',
                    'value': 'ECON 1901: Business Economics'
                }, {
                    'id': '57',
                    'value': 'ECON 403: Macroeconomics'
                }, {
                    'id': '159',
                    'value': 'ECON 605: Money & Banking'
                }, {
                    'id': '229',
                    'value': 'ECON 701: International Trade & Trade Policy'
                }, {
                    'id': '469',
                    'value': 'ECON 713: International Trade'
                }, {
                    'id': '470',
                    'value': 'ECON 714: Introduction to Econometrics'
                }, {
                    'id': '502',
                    'value': 'ECON 820: Seminar in Economics (2nd Major seminar)'
                }, {
                    'id': '38',
                    'value': 'ECON 901: Business Economics'
                }, {
                    'id': '617',
                    'value': 'ECON 920: Advanced Microeconomics'
                }, {
                    'id': '2311',
                    'value': 'EDPT  1017: Introduction to Lean Manufacturing'
                }, {
                    'id': '709',
                    'value': 'EDPT 1010: Design of Jigs, Fixtures'
                }, {
                    'id': '710',
                    'value': 'EDPT 1011: Facility Planning'
                }, {
                    'id': '711',
                    'value': 'EDPT 1012: Manufacturing System Design and Simulation'
                }, {
                    'id': '712',
                    'value': 'EDPT 1013: Materials and Process Selection in Design'
                }, {
                    'id': '432',
                    'value': 'EDPT 201: Production Technology'
                }, {
                    'id': '322',
                    'value': 'EDPT 401: CAD- Lab'
                }, {
                    'id': '693',
                    'value': 'EDPT 402: Material Removal Processes and Machines'
                }, {
                    'id': '694',
                    'value': 'EDPT 403: Machine Drawing With CAD'
                }, {
                    'id': '452',
                    'value': 'EDPT 601: Materials Manufacturing Technology'
                }, {
                    'id': '196',
                    'value': 'EDPT 602: Engineering Design II'
                }, {
                    'id': '713',
                    'value': 'EDPT 603: Theory Of Metal Cutting'
                }, {
                    'id': '714',
                    'value': 'EDPT 604: Metal Forming Processes and Machines'
                }, {
                    'id': '638',
                    'value': 'ELCT 1001: Optoelectronic Devices and Circuits'
                }, {
                    'id': '639',
                    'value': 'ELCT 1002: Systems on a Chip'
                }, {
                    'id': '640',
                    'value': 'ELCT 1003: High Speed Electronic Circuits'
                }, {
                    'id': '641',
                    'value': 'ELCT 1005: Advanced Microelectronics Lab'
                }, {
                    'id': '2458',
                    'value': 'ELCT 1018: Introduction to Quantum Technologies & Computing'
                }, {
                    'id': '2794',
                    'value': 'ELCT 1021: Cosmetic Technology'
                }, {
                    'id': '2795',
                    'value': 'ELCT 1041: Clinical Nutrition'
                }, {
                    'id': '2796',
                    'value': 'ELCT 1051: Applied Toxicology'
                }, {
                    'id': '2923',
                    'value': 'ELCT 1052: Inorganic Pharmaceuticals and Diagnostics'
                }, {
                    'id': '2924',
                    'value': 'ELCT 1053: Recent Trends in Medicinal Chemistry and Drug Discovery'
                }, {
                    'id': '2797',
                    'value': 'ELCT 1111: Pharmacogenomics and Pharmacogenetics'
                }, {
                    'id': '79',
                    'value': 'ELCT 201: Digital Logic Design'
                }, {
                    'id': '47',
                    'value': 'ELCT 401: Electrical Circuits II'
                }, {
                    'id': '465',
                    'value': 'ELCT 601: Digital System Design'
                }, {
                    'id': '594',
                    'value': 'ELCT 601: Electrical Engineering'
                }, {
                    'id': '189',
                    'value': 'ELCT 602: Solid State Electronics'
                }, {
                    'id': '190',
                    'value': 'ELCT 603: Optoelectronics'
                }, {
                    'id': '191',
                    'value': 'ELCT 604: Electronic Circuits'
                }, {
                    'id': '192',
                    'value': 'ELCT 605: Microelectronics Lab'
                }, {
                    'id': '1101',
                    'value': 'ELCT 609: Electronic Circuits'
                }, {
                    'id': '1219',
                    'value': 'ELCT 910: Integrated Circuit Design for Wireless Communications'
                }, {
                    'id': '549',
                    'value': 'ENG 800: Engineering Bachelor Thesis'
                }, {
                    'id': '49',
                    'value': 'ENGD 301: Engineering Drawing & Design'
                }, {
                    'id': '83',
                    'value': 'ENME 401: Strength of Materials I'
                }, {
                    'id': '459',
                    'value': 'ENME 402: Mechanics II'
                }, {
                    'id': '84',
                    'value': 'ENME 602: Numerical Analysis'
                }, {
                    'id': '595',
                    'value': 'ENME 603: Heat and Mass Transfer'
                }, {
                    'id': '1882',
                    'value': 'FINC 1901: Corporate Finance'
                }, {
                    'id': '2613',
                    'value': 'FINC 2101: Advanced Topics in Accounting '
                }, {
                    'id': '2625',
                    'value': 'FINC 2201: Financial Markets & Institutions '
                }, {
                    'id': '2615',
                    'value': 'FINC 2301: Advanced Topics in Finance'
                }, {
                    'id': '2624',
                    'value': 'FINC 2302: Contemporary Issues in Finance'
                }, {
                    'id': '2620',
                    'value': 'FINC 2401: Financial Analysis & Security Valuation'
                }, {
                    'id': '2629',
                    'value': 'FINC 2500: Finance Comprehensive Exam'
                }, {
                    'id': '2630',
                    'value': 'FINC 2600: Finance Dissertation'
                }, {
                    'id': '89',
                    'value': 'FINC 403: Managerial Finance'
                }, {
                    'id': '108',
                    'value': 'FINC 702: Portfolio Management and Investement Analysis'
                }, {
                    'id': '212',
                    'value': 'FINC 704: Advanced Corporate Finance'
                }, {
                    'id': '214',
                    'value': 'FINC 705: Finance Seminar'
                }, {
                    'id': '475',
                    'value': 'FINC 713: Banking Management and Credit Analysis'
                }, {
                    'id': '272',
                    'value': 'FINC 714: Derivatives'
                }, {
                    'id': '273',
                    'value': 'FINC 720: Seminar in Finance (1st Major seminar)'
                }, {
                    'id': '1580',
                    'value': 'FINC 801: Finance Thesis'
                }, {
                    'id': '504',
                    'value': 'FINC 820: Seminar in Finance (2nd Major seminar)'
                }, {
                    'id': '67',
                    'value': 'FINC 901: Corporate Finance'
                }, {
                    'id': '1187',
                    'value': 'GD 202: Grid Structure and Color Systems'
                }, {
                    'id': '1196',
                    'value': 'GD 402: GD Project'
                }, {
                    'id': '1201',
                    'value': 'GD 613: Sign Systems'
                }, {
                    'id': '1202',
                    'value': 'GD 614: Font Design II (Arabic)'
                }, {
                    'id': '1530',
                    'value': 'GD 615: Global Communication I'
                }, {
                    'id': '1529',
                    'value': 'GD 616: Interaction Design I'
                }, {
                    'id': '1205',
                    'value': 'GD 801: Bachelor Project - Graphic Design'
                }, {
                    'id': '1153',
                    'value': 'GMAT 902: Management Aptitude Enhancement- Verbal'
                }, {
                    'id': '1154',
                    'value': 'GMAT 903: Management Aptitude Enhancement- Quantitative'
                }, {
                    'id': '1880',
                    'value': 'HROB 1901: Introduction to Human Resource Management'
                }, {
                    'id': '88',
                    'value': 'HROB 201: Human Resources Management'
                }, {
                    'id': '2488',
                    'value': 'HROB 203: Human Resources Management for BI'
                }, {
                    'id': '226',
                    'value': 'HROB 703: Organizational Change'
                }, {
                    'id': '228',
                    'value': 'HROB 705: Seminar In Human Resources & Organizational Behavior'
                }, {
                    'id': '513',
                    'value': 'HROB 706: Compensation Management'
                }, {
                    'id': '1961',
                    'value': 'HROB 713: Compensation and Performance Management'
                }, {
                    'id': '1950',
                    'value': 'HROB 714: Staffing & Development'
                }, {
                    'id': '278',
                    'value': 'HROB 720: Seminar in Human Resources (1st Major seminar)'
                }, {
                    'id': '1584',
                    'value': 'HROB 801: Human Resources & Organizational Behavior Thesis'
                }, {
                    'id': '506',
                    'value': 'HROB 820: Seminar in Human Resources (2nd Major seminar)'
                }, {
                    'id': '40',
                    'value': 'HROB 901: Introduction to Human Resource Management'
                }, {
                    'id': '754',
                    'value': 'HUMA 1001: Project Management'
                }, {
                    'id': '683',
                    'value': 'HUMA 1001: Project Management'
                }, {
                    'id': '2592',
                    'value': 'HUMA 416: Human Rights '
                }, {
                    'id': '715',
                    'value': 'HUMA 601: Introduction to Management'
                }, {
                    'id': '232',
                    'value': 'IBUS 703: Transnational Management'
                }, {
                    'id': '233',
                    'value': 'IBUS 705: Seminar in International Business'
                }, {
                    'id': '2858',
                    'value': 'IBUS 710: Contemporary Issues in International Business'
                }, {
                    'id': '1954',
                    'value': 'IBUS 713: Intercultural Aspects'
                }, {
                    'id': '1586',
                    'value': 'IBUS 801: International Business Thesis'
                }, {
                    'id': '508',
                    'value': 'IBUS 820: Seminar in International Business (2nd Major seminar)'
                }, {
                    'id': '624',
                    'value': 'IBUS 920: Advanced International Business'
                }, {
                    'id': '1879',
                    'value': 'INNO 1901: Entrepreneurship'
                }, {
                    'id': '246',
                    'value': 'INNO 701: Managing the Innovation Process'
                }, {
                    'id': '249',
                    'value': 'INNO 705: Seminar In Innovation & Technology Management'
                }, {
                    'id': '987',
                    'value': 'INNO 714: Project Management'
                }, {
                    'id': '2857',
                    'value': 'INNO 715: Emerging Technologies and Business Model Innovation'
                }, {
                    'id': '767',
                    'value': 'INNO 720: Seminar in Innovation (1st Major seminar)'
                }, {
                    'id': '1588',
                    'value': 'INNO 801: Innovation and Technology Thesis'
                }, {
                    'id': '988',
                    'value': 'INNO 820: Seminar in Innovation (2nd Major seminar)'
                }, {
                    'id': '39',
                    'value': 'INNO 901: Entrepreneurship'
                }, {
                    'id': '100',
                    'value': 'INSY 402: Information Systems II'
                }, {
                    'id': '234',
                    'value': 'INSY 701: Information Management'
                }, {
                    'id': '238',
                    'value': 'INSY 705: Seminar in Information Systems'
                }, {
                    'id': '2716',
                    'value': 'INSY 708: Digital Transformation'
                }, {
                    'id': '477',
                    'value': 'INSY 714: IT Project Management'
                }, {
                    'id': '2874',
                    'value': 'INSY 715: Digital Transformation'
                }, {
                    'id': '281',
                    'value': 'INSY 720: Seminar in Information Systems (1st Major seminar)'
                }, {
                    'id': '1585',
                    'value': 'INSY 801: Information Systems Thesis'
                }, {
                    'id': '505',
                    'value': 'INSY 820: Seminar in Information Systems (2nd Major seminar)'
                }, {
                    'id': '2081',
                    'value': 'ISSH 201: Principles of Islamic Sharia'
                }, {
                    'id': '2378',
                    'value': 'ISSH 601: Islamic Sharia (Inheritance, Will and Trust)'
                }, {
                    'id': '2490',
                    'value': 'LAW 801: Environmental Law: International & National Standards'
                }, {
                    'id': '2778',
                    'value': 'LAW 804: Sports Law'
                }, {
                    'id': '2487',
                    'value': 'LAW 805: Bachelor Thesis'
                }, {
                    'id': '31',
                    'value': 'LAWS 201: Principles of Law'
                }, {
                    'id': '2',
                    'value': 'MATH 103: Maths'
                }, {
                    'id': '29',
                    'value': 'MATH 201: Math & Statistics II'
                }, {
                    'id': '17',
                    'value': 'MATH 203: Mathematics I'
                }, {
                    'id': '200',
                    'value': 'Math 204: Mathematics for business informatics II'
                }, {
                    'id': '74',
                    'value': 'MATH 401: Math IV Probability and Statistics'
                }, {
                    'id': '1072',
                    'value': 'MATH 404: Math IV'
                }, {
                    'id': '36',
                    'value': 'MATH 901: Math & Statistics'
                }, {
                    'id': '1523',
                    'value': 'MATS  911: Manufacturing of Plastic and Rubber Products'
                }, {
                    'id': '646',
                    'value': 'MATS 1001: Surface Engineering'
                }, {
                    'id': '647',
                    'value': 'MATS 1002: Destructive and Non Destructive Testing of materials'
                }, {
                    'id': '1471',
                    'value': 'MATS 1010: Failure analysis of mechanical components'
                }, {
                    'id': '2302',
                    'value': 'MATS 1019: New product Development and Innovation Management'
                }, {
                    'id': '2567',
                    'value': 'MATS 1023: Soft Matter Nanotechnology'
                }, {
                    'id': '81',
                    'value': 'MATS 401: Ferrous and Non-Ferrous Alloys'
                }, {
                    'id': '460',
                    'value': 'MATS 402: Materials Lab I'
                }, {
                    'id': '695',
                    'value': 'MATS 403: Materials Engineering I: Metallic Materials'
                }, {
                    'id': '718',
                    'value': 'MATS 404: Introduction to Materials Engineering'
                }, {
                    'id': '1104',
                    'value': 'MATS 405: Destructive and Non Destructive Testing Of Materials'
                }, {
                    'id': '593',
                    'value': 'MATS 601: Composites and Ceramic Materials'
                }, {
                    'id': '454',
                    'value': 'MATS 602: Engineering Polymers'
                }, {
                    'id': '1768',
                    'value': 'MCTR  1010: Image processing for Mechatronics'
                }, {
                    'id': '730',
                    'value': 'MCTR 1002: Autonomous System'
                }, {
                    'id': '1460',
                    'value': 'MCTR 1004: Cooling of Electronic Systems'
                }, {
                    'id': '1469',
                    'value': 'MCTR 1006: Renewable/Sustainable Energy Technology'
                }, {
                    'id': '1486',
                    'value': 'MCTR 1007: Modeling and Simulation of Electrohydraulic Systems'
                }, {
                    'id': '1579',
                    'value': 'MCTR 1009: Finite Element Methods'
                }, {
                    'id': '2003',
                    'value': 'MCTR 1017: Industrial Sensors And Applications'
                }, {
                    'id': '2570',
                    'value': 'MCTR 1024: Reinforcement Learning and Optimal Control'
                }, {
                    'id': '721',
                    'value': 'MCTR 601: Mechatronics Engineering'
                }, {
                    'id': '1526',
                    'value': 'MCTR 908: Electric-Drives'
                }, {
                    'id': '1188',
                    'value': 'MD 202: Photography'
                }, {
                    'id': '1198',
                    'value': 'MD 402: Media Design Project'
                }, {
                    'id': '933',
                    'value': 'MD 609: Digital Compositing I'
                }, {
                    'id': '1216',
                    'value': 'MD 612: Montage'
                }, {
                    'id': '1211',
                    'value': 'MD 613: Sound'
                }, {
                    'id': '1212',
                    'value': 'MD 614: Moving image/interactive design'
                }, {
                    'id': '1213',
                    'value': 'MD 615: Media Installation'
                }, {
                    'id': '1204',
                    'value': 'MD 801: Bachelor Project - Media Design'
                }, {
                    'id': '2611',
                    'value': 'MGMT 2101: Advanced Multivariate Statistics'
                }, {
                    'id': '2612',
                    'value': 'MGMT 2102: Advanced Organizational Behavior '
                }, {
                    'id': '2616',
                    'value': 'MGMT 2201: Industry 4.0'
                }, {
                    'id': '2622',
                    'value': 'MGMT 2202: Advanced Strategic Management'
                }, {
                    'id': '2619',
                    'value': 'MGMT 2301: Advanced Research Methodology'
                }, {
                    'id': '2628',
                    'value': 'MGMT 2302: Strategic Human Resource Management '
                }, {
                    'id': '2621',
                    'value': 'MGMT 2401: Qualitative Analysis '
                }, {
                    'id': '2626',
                    'value': 'MGMT 2402: Organizational Performance Management '
                }, {
                    'id': '2618',
                    'value': 'MGMT 2403: Organization Design'
                }, {
                    'id': '2804',
                    'value': 'MGMT 2500: Management Comprehensive Exam'
                }, {
                    'id': '2807',
                    'value': 'MGMT 2600: Management Dissertation'
                }, {
                    'id': '137',
                    'value': 'MGMT 502: Research Methodology'
                }, {
                    'id': '161',
                    'value': 'MGMT 604: Quantitative & Qualitative Analysis'
                }, {
                    'id': '548',
                    'value': 'MGMT 800: Management Bachelor Thesis'
                }, {
                    'id': '176',
                    'value': 'MNGT 601: Introduction to Management'
                }, {
                    'id': '1881',
                    'value': 'MRKT 1901: Marketing'
                }, {
                    'id': '2614',
                    'value': 'MRKT 2201: Marketing Theory'
                }, {
                    'id': '2803',
                    'value': 'MRKT 2500: Marketing Comprehensive Exam'
                }, {
                    'id': '92',
                    'value': 'MRKT 402: Marketing II'
                }, {
                    'id': '158',
                    'value': 'MRKT 602: Consumer Behaviour'
                }, {
                    'id': '71',
                    'value': 'MRKT 704: International Marketing'
                }, {
                    'id': '216',
                    'value': 'MRKT 705: Seminar in Marketing'
                }, {
                    'id': '480',
                    'value': 'MRKT 713: International Marketing'
                }, {
                    'id': '1951',
                    'value': 'MRKT 714: Marketing Channels and Distribution'
                }, {
                    'id': '2862',
                    'value': 'MRKT 717: Sustainability Marketing'
                }, {
                    'id': '217',
                    'value': 'MRKT 718: Strategic Marketing'
                }, {
                    'id': '276',
                    'value': 'MRKT 720: Seminar in Marketing (1st Major seminar)'
                }, {
                    'id': '1664',
                    'value': 'MRKT 730: Special Topics in Marketing'
                }, {
                    'id': '1582',
                    'value': 'MRKT 801: Marketing Thesis'
                }, {
                    'id': '507',
                    'value': 'MRKT 820: Seminar in Marketing (2nd Major seminar)'
                }, {
                    'id': '66',
                    'value': 'MRKT 901: Marketing'
                }, {
                    'id': '2350',
                    'value': 'NETW  1013: Machine Learning'
                }, {
                    'id': '642',
                    'value': 'NETW 1001: Network Management'
                }, {
                    'id': '643',
                    'value': 'NETW 1002: Systems and Network Security'
                }, {
                    'id': '644',
                    'value': 'NETW 1003: Network Planning'
                }, {
                    'id': '645',
                    'value': 'NETW 1005: Advanced Network Lab'
                }, {
                    'id': '1705',
                    'value': 'NETW 1009: Cloud Computing (ISM)'
                }, {
                    'id': '353',
                    'value': 'NETW 501: Communication Networks'
                }, {
                    'id': '185',
                    'value': 'NETW 601: Transmission and Switching'
                }, {
                    'id': '974',
                    'value': 'NETW 602: Network Lab'
                }, {
                    'id': '2709',
                    'value': 'NETW 603: Computer System Architecture'
                }, {
                    'id': '300',
                    'value': 'NETW 703: Network Protocols'
                }, {
                    'id': '304',
                    'value': 'NETW 707: Modelling and Simulation'
                }, {
                    'id': '1883',
                    'value': 'OPER 1901: Operations Management'
                }, {
                    'id': '91',
                    'value': 'OPER 602: Operations II'
                }, {
                    'id': '244',
                    'value': 'OPER 705: Seminar in Operations Management'
                }, {
                    'id': '731',
                    'value': 'OPER 713: Operation in Service Organization'
                }, {
                    'id': '732',
                    'value': 'OPER 714: Supply Chain Management'
                }, {
                    'id': '1665',
                    'value': 'OPER 730: Special Topics in Operation Management'
                }, {
                    'id': '1583',
                    'value': 'OPER 801: Operations & Production Management Thesis'
                }, {
                    'id': '733',
                    'value': 'OPER 820: Seminar in Operations (2nd Major seminar)'
                }, {
                    'id': '105',
                    'value': 'OPER 901: Operations Management'
                }, {
                    'id': '1189',
                    'value': 'PD 202: Form generation'
                }, {
                    'id': '1197',
                    'value': 'PD 402: PD Project'
                }, {
                    'id': '830',
                    'value': 'PD 601: Exhibition Design'
                }, {
                    'id': '832',
                    'value': 'PD 603: Computer Aided Design (CAD)'
                }, {
                    'id': '837',
                    'value': 'PD 608: Material Science'
                }, {
                    'id': '1214',
                    'value': 'PD 609: Lighting Design'
                }, {
                    'id': '1215',
                    'value': 'PD 610: Production Techniques'
                }, {
                    'id': '1152',
                    'value': 'PD 801: Bachelor Project - Product Design'
                }, {
                    'id': '2327',
                    'value': 'PEPF 401: Political Economics'
                }, {
                    'id': '2383',
                    'value': 'PEPF 601: Public Finance'
                }, {
                    'id': '2706',
                    'value': 'PHBCp 622: Biochemistry II'
                }, {
                    'id': '2705',
                    'value': 'PHBCt 622: Biochemistry II'
                }, {
                    'id': '2112',
                    'value': 'PHBLp 202: Pharmaceutical Biology II'
                }, {
                    'id': '2596',
                    'value': 'PHBLp 411: Pharmacognosy II'
                }, {
                    'id': '2701',
                    'value': 'PHBLp 641: Phytochemistry I'
                }, {
                    'id': '2111',
                    'value': 'PHBLt 202: Pharmaceutical Biology II'
                }, {
                    'id': '2591',
                    'value': 'PHBLt 411: Pharmacognosy II'
                }, {
                    'id': '2700',
                    'value': 'PHBLt 641: Phytochemistry I'
                }, {
                    'id': '2935',
                    'value': 'PHBTp 1011: Public Health and Tropical Medicine'
                }, {
                    'id': '2170',
                    'value': 'PHBTp 1022: Biotechnology II'
                }, {
                    'id': '2594',
                    'value': 'PHBTp 402: Pharmaceutical Microbiology'
                }, {
                    'id': '2934',
                    'value': 'PHBTt 1011: Public Health and Tropical Medicine'
                }, {
                    'id': '2160',
                    'value': 'PHBTt 1022: Biotechnology II'
                }, {
                    'id': '2588',
                    'value': 'PHBTt 402: Pharmaceutical Microbiology'
                }, {
                    'id': '2770',
                    'value': 'PHCM 875: Pharmaceutical Chemistry IV'
                }, {
                    'id': '2108',
                    'value': 'PHCMp 223: Pharmaceutical Analytical Chemistry'
                }, {
                    'id': '2114',
                    'value': 'PHCMp 241: Physics for Pharmacy'
                }, {
                    'id': '2593',
                    'value': 'PHCMp 433: Organic Pharmaceutical Chemistry  II'
                }, {
                    'id': '2694',
                    'value': 'PHCMp 663: Instrumental analysis II'
                }, {
                    'id': '2692',
                    'value': 'PHCMp 673: Pharmaceutical Chemistry II'
                }, {
                    'id': '443',
                    'value': 'PHCMp 874: Pharmaceutical Chemistry IV'
                }, {
                    'id': '2768',
                    'value': 'PHCMp 882: Drug Design'
                }, {
                    'id': '2107',
                    'value': 'PHCMt 223: Pharmaceutical Analytical Chemistry'
                }, {
                    'id': '2113',
                    'value': 'PHCMt 241: Physics for Pharmacy'
                }, {
                    'id': '2587',
                    'value': 'PHCMt 433: Organic Pharmaceutical Chemistry  II'
                }, {
                    'id': '2693',
                    'value': 'PHCMt 663: Instrumental analysis II'
                }, {
                    'id': '2691',
                    'value': 'PHCMt 673: Pharmaceutical Chemistry II'
                }, {
                    'id': '2757',
                    'value': 'PHCMt 881: Drug Design'
                }, {
                    'id': '2950',
                    'value': 'PHMK 1211: Strategic Marketing'
                }, {
                    'id': '2951',
                    'value': 'PHMK 1212: Brand Management'
                }, {
                    'id': '2952',
                    'value': 'PHMK 1213: Pharma AI and Digital Marketing'
                }, {
                    'id': '2953',
                    'value': 'PHMK 1214: Graduation Project'
                }, {
                    'id': '2590',
                    'value': 'PHMU 403: Pathology and Histology'
                }, {
                    'id': '2496',
                    'value': 'PHMUp 202: Physiology and Anatomy I'
                }, {
                    'id': '2493',
                    'value': 'PHMUt 202: Physiology and Anatomy I'
                }, {
                    'id': '2785',
                    'value': 'PHPE 1201: Health Technology Assessment  and Market Access'
                }, {
                    'id': '2786',
                    'value': 'PHPE 1202: Pharmacoeconomics Modelling'
                }, {
                    'id': '2787',
                    'value': 'PHPE 1203: Strategic Management in Pharmaceutical industry'
                }, {
                    'id': '2788',
                    'value': 'PHPE 1204: Special Topics and Seminars in Pharmacoeconomics'
                }, {
                    'id': '2666',
                    'value': 'PHRT 101: Selected Advanced Specialized Research Techniques'
                }, {
                    'id': '2495',
                    'value': 'PHTC 203: History of Pharmacy & Biotechnology'
                }, {
                    'id': '2707',
                    'value': 'PHTC 622: Legislation and Pharmacy Laws'
                }, {
                    'id': '2777',
                    'value': 'PHTC 842: Quality Control, Quality Assurance and GMP'
                }, {
                    'id': '2595',
                    'value': 'PHTCp 412: Pharmaceutics II'
                }, {
                    'id': '2772',
                    'value': 'PHTCp 833: Pharmaceutical Technology I'
                }, {
                    'id': '446',
                    'value': 'PHTCp 833: Pharmaceutical Technology II'
                }, {
                    'id': '2589',
                    'value': 'PHTCt 412: Pharmaceutics II'
                }, {
                    'id': '2771',
                    'value': 'PHTCt 833: Pharmaceutical Technology I'
                }, {
                    'id': '445',
                    'value': 'PHTCt 833: Pharmaceutical Technology II'
                }, {
                    'id': '2665',
                    'value': 'PHTP 101: Selected Advanced Specialized Topics'
                }, {
                    'id': '2494',
                    'value': 'PHTX 201: Pharmaceutical & Medical Terminology'
                }, {
                    'id': '2702',
                    'value': 'PHTX 680: Pharmaceutical Marketing and Entrepreneurship'
                }, {
                    'id': '2769',
                    'value': 'PHTX 823: Pharmacology III'
                }, {
                    'id': '447',
                    'value': 'PHTX 831: Toxicology I'
                }, {
                    'id': '2704',
                    'value': 'PHTXp 612: Pharmacology I'
                }, {
                    'id': '2774',
                    'value': 'PHTXp 834: Toxicology'
                }, {
                    'id': '2703',
                    'value': 'PHTXt 612: Pharmacology I'
                }, {
                    'id': '2773',
                    'value': 'PHTXt 834: Toxicology'
                }, {
                    'id': '450',
                    'value': 'PHYS 202: Physics II'
                }, {
                    'id': '1534',
                    'value': 'PR 1001: Advanced Design Project II'
                }, {
                    'id': '2381',
                    'value': 'PRIN 601: Private International Law (Nationality and Status of Foreigners)'
                }, {
                    'id': '2323',
                    'value': 'PUIN 402: Public International Law'
                }, {
                    'id': '2481',
                    'value': 'PUL 801: Tax Law'
                }, {
                    'id': '34',
                    'value': 'RPW 401: Research Paper Writing (A2)'
                }, {
                    'id': '72',
                    'value': 'RSMD 901: Research Methodology'
                }, {
                    'id': '16',
                    'value': 'SM 101: Scientific Methods (A1)'
                }, {
                    'id': '3030',
                    'value': 'STAT 901: Applied  Statistics'
                }, {
                    'id': '1884',
                    'value': 'STRA 1901: Strategic Management'
                }, {
                    'id': '69',
                    'value': 'STRA 701: Corporate Renewal / Change Management'
                }, {
                    'id': '106',
                    'value': 'STRA 702: Business Dynamics'
                }, {
                    'id': '107',
                    'value': 'STRA 703: Strategic Management'
                }, {
                    'id': '70',
                    'value': 'STRA 704: Strategic Management Analysis'
                }, {
                    'id': '218',
                    'value': 'STRA 705: Strategic Management Seminar'
                }, {
                    'id': '1947',
                    'value': 'STRA 713: Strategic Analysis'
                }, {
                    'id': '1949',
                    'value': 'STRA 714: Strategic Decision Making'
                }, {
                    'id': '270',
                    'value': 'STRA 720: Seminar in Strategic Management (1st Major seminar)'
                }, {
                    'id': '1587',
                    'value': 'STRA 801: Strategic Management Thesis'
                }, {
                    'id': '503',
                    'value': 'STRA 820: Seminar in Strategic Management (2nd Major seminar)'
                }, {
                    'id': '1422',
                    'value': 'TH 1001: Design and Organization'
                }, {
                    'id': '1436',
                    'value': 'TH 1002: Design and Economy'
                }, {
                    'id': '1015',
                    'value': 'TH 201: Cultural History'
                }, {
                    'id': '1026',
                    'value': 'TH 401: Design Theory'
                }, {
                    'id': '1191',
                    'value': 'TH 402: Design and Sciences'
                }, {
                    'id': '838',
                    'value': 'TH 601: Communication Theory (Basic)'
                }, {
                    'id': '1203',
                    'value': 'TH 603: Culture Theory'
                }, {
                    'id': '1632',
                    'value': 'UP 601: Theory of Urban Planning'
                }, {
                    'id': '1633',
                    'value': 'UP 602: Housing'
                }, {
                    'id': '1634',
                    'value': 'UP 603: Introduction to Urban Sociology'
                }]
                  , tas = [{
                    'id': '2560',
                    'value': 'Abbas Abdelkarim Yehia'
                }, {
                    'id': '3064',
                    'value': 'Abdel Megid Mahmoud Allam'
                }, {
                    'id': '6851',
                    'value': 'Abdel Rahman Hatem ElSayed Mansour ElBarshoumy'
                }, {
                    'id': '9199',
                    'value': 'Abdelaziz Mahmoud Awadallah Ali Hassan Gohar'
                }, {
                    'id': '8891',
                    'value': 'Abdelrahman  Nagy Bakr Mohamed Ramdan Halawa '
                }, {
                    'id': '6416',
                    'value': 'Abdelrahman Islam Ahmed Mahmoud'
                }, {
                    'id': '9171',
                    'value': 'Abdelrahman Khaled Ali Asran Ahmed'
                }, {
                    'id': '9188',
                    'value': 'Abdelrahman Mohamed Nabil Eldesoky Ali Shoman'
                }, {
                    'id': '8283',
                    'value': 'Abdelrahman Salah Salama Mohamed Ghoneim'
                }, {
                    'id': '9399',
                    'value': 'Abdullah Mahmoud Khodary Farag'
                }, {
                    'id': '4050',
                    'value': 'Abeer Ahmed Elshahed'
                }, {
                    'id': '5630',
                    'value': 'Abeer Samir Khalifa'
                }, {
                    'id': '5968',
                    'value': 'Abla Mohamed Abdelnaby'
                }, {
                    'id': '2401',
                    'value': 'Adel Ahmed Naiem'
                }, {
                    'id': '4060',
                    'value': 'Adel Kamel Farag'
                }, {
                    'id': '8096',
                    'value': 'Admir . Jukanovic'
                }, {
                    'id': '8572',
                    'value': 'Aesha  Mohammed Tawfik Adam  Mohammed '
                }, {
                    'id': '9259',
                    'value': 'Afaf Saied Shehata Mustafa Darwish'
                }, {
                    'id': '7620',
                    'value': 'Aghathon Mories Bekhit'
                }, {
                    'id': '4480',
                    'value': 'Agnieszka . Michalczyk'
                }, {
                    'id': '9258',
                    'value': 'Ahmad  Fathy Abdelkhalek  Morsy'
                }, {
                    'id': '9185',
                    'value': 'Ahmad Haytham Omar Aboelshwarb Mohamed Ragheb'
                }, {
                    'id': '7220',
                    'value': 'Ahmad Mohammad Mohammad Mohammad Helmy'
                }, {
                    'id': '9085',
                    'value': 'Ahmed  Alaa Eldin Ismail  Mohamed Fawzy Elansary'
                }, {
                    'id': '8528',
                    'value': 'Ahmed  Maged Abdelhady Mohamed  Hashim '
                }, {
                    'id': '8922',
                    'value': 'Ahmed  Mamdoh Ahmed Antar Sayed Ahmed'
                }, {
                    'id': '9108',
                    'value': 'Ahmed  Mohammed Mahmoud Mohammed Ossairy'
                }, {
                    'id': '8941',
                    'value': 'Ahmed  Sameh Alshabrawi  Ahmed'
                }, {
                    'id': '8169',
                    'value': 'Ahmed Abd El-Hamid Mohamed Shafik Maarouf'
                }, {
                    'id': '7670',
                    'value': 'Ahmed Abd Elreheem Tawfik'
                }, {
                    'id': '6540',
                    'value': 'Ahmed Ali Abdelhai Ibrahim Dayhom'
                }, {
                    'id': '8626',
                    'value': 'Ahmed Ali Abdelkader Abdalla Elkahwagy'
                }, {
                    'id': '2334',
                    'value': 'Ahmed Amin Mohamed'
                }, {
                    'id': '3952',
                    'value': 'Ahmed El Moneer'
                }, {
                    'id': '5406',
                    'value': 'Ahmed Elsayed  Elhozayen'
                }, {
                    'id': '3511',
                    'value': 'Ahmed Elsayed Elmahdy'
                }, {
                    'id': '3270',
                    'value': 'Ahmed El-Sayed Wahby'
                }, {
                    'id': '6545',
                    'value': 'Ahmed Fathy Khalifah'
                }, {
                    'id': '9386',
                    'value': 'Ahmed Gamil Mohamed Ahmed'
                }, {
                    'id': '9112',
                    'value': 'Ahmed Ibrahim Ahmed Othman'
                }, {
                    'id': '8023',
                    'value': 'Ahmed Khairy Ahmed Abd Elhamid'
                }, {
                    'id': '5512',
                    'value': 'Ahmed Maher Eltair'
                }, {
                    'id': '134',
                    'value': 'Ahmed Metwali Abdel Aziz'
                }, {
                    'id': '4466',
                    'value': 'Ahmed Mohamed Ali Naiel'
                }, {
                    'id': '8244',
                    'value': 'Ahmed Mohammed Hassan'
                }, {
                    'id': '8659',
                    'value': 'Ahmed Mohammed Hassan Abdelfattah'
                }, {
                    'id': '9230',
                    'value': 'Ahmed Nagy Bakr Mohamed Ramadan'
                }, {
                    'id': '7283',
                    'value': 'Ahmed Sameeh Abdelrady Hassan'
                }, {
                    'id': '8833',
                    'value': 'Ahmed Samy Mohamed Fahmy Ahmed Ouf'
                }, {
                    'id': '8969',
                    'value': 'Ahmed Soliman Abdelhamid Soliman Elsherbiny'
                }, {
                    'id': '8252',
                    'value': 'Ahmed Walid Mahmoud Heussin El-Borgy'
                }, {
                    'id': '6127',
                    'value': 'Ahmed Yehia Shash'
                }, {
                    'id': '4340',
                    'value': 'Aida Nagy Hassan'
                }, {
                    'id': '8860',
                    'value': 'Alaa Ahmed Masoud Mohamed Mohamed'
                }, {
                    'id': '8879',
                    'value': 'Alaa Amr Mohamed Abdelazzem Mohamed'
                }, {
                    'id': '6866',
                    'value': 'Alaa Gamal ElDin Mohamed Saeed Baligh'
                }, {
                    'id': '5536',
                    'value': 'Alaa Mahmoud Selim'
                }, {
                    'id': '4861',
                    'value': 'Alaa Mohamed Darwish'
                }, {
                    'id': '2836',
                    'value': 'Alaa Mohamed Elanssary'
                }, {
                    'id': '8864',
                    'value': 'Alaa Mohamed Sobeih Mohamed Eid '
                }, {
                    'id': '9029',
                    'value': 'Alaa Tarek Khalil'
                }, {
                    'id': '8968',
                    'value': 'Alberta . Carandente'
                }, {
                    'id': '8506',
                    'value': 'Ali  Midhat Ali Elsayed  Suleiman '
                }, {
                    'id': '9385',
                    'value': 'Ali Ahmed Ibrahim Abdelghafar'
                }, {
                    'id': '9407',
                    'value': 'Ali Tamer Ali Reda'
                }, {
                    'id': '8238',
                    'value': 'Ali Tarek Abdelaleem Mohamed Gweely'
                }, {
                    'id': '7921',
                    'value': 'Aliaa Adel Mohamed Ali Abouelhag'
                }, {
                    'id': '2342',
                    'value': 'Aliaa Anis Taha'
                }, {
                    'id': '5115',
                    'value': 'Aliaa Maged Kamal Mohamed'
                }, {
                    'id': '7911',
                    'value': 'Aly Eldin Hazem Atef Mohamed Rezk Sakr'
                }, {
                    'id': '6949',
                    'value': 'Alyaa Hegazy Abdelhamid Ahmed Hegazy'
                }, {
                    'id': '5036',
                    'value': 'Alzahraa Ibrahim Abdelhady'
                }, {
                    'id': '3325',
                    'value': 'Amal Ibrahim Ali'
                }, {
                    'id': '5176',
                    'value': 'Amany Zakaria Abdelmoneim'
                }, {
                    'id': '5478',
                    'value': 'Amina  Ehab  Sobhy'
                }, {
                    'id': '3061',
                    'value': 'Amir Roushdy Abdelhameed'
                }, {
                    'id': '8993',
                    'value': 'Amir Tadros Nabih Abdo'
                }, {
                    'id': '8665',
                    'value': 'Amira Mahmoud Mohammed Mohammed Nematalla'
                }, {
                    'id': '9282',
                    'value': 'Amira Tarek Ibrahim Metwaly Elnoamany'
                }, {
                    'id': '9274',
                    'value': 'Ammar Yehia Abdoulwahab Kasem'
                }, {
                    'id': '7160',
                    'value': 'Amna Youssef Ahmed Ramzy'
                }, {
                    'id': '9067',
                    'value': 'Amr  Alaa Hassan Ibrahim Hassan'
                }, {
                    'id': '5448',
                    'value': 'Amr  Maher Elnemr'
                }, {
                    'id': '8944',
                    'value': 'Amr  Yasser Mohammed Hussein Ahmed Kasber'
                }, {
                    'id': '121',
                    'value': 'Amr Abdallah Abou Shousha'
                }, {
                    'id': '8253',
                    'value': 'Amr Kais Elrai Attia'
                }, {
                    'id': '9217',
                    'value': 'Amr Magdy Hassan Mahmoud Ayoub'
                }, {
                    'id': '2468',
                    'value': 'Amr Talaat Abdel-Hamid'
                }, {
                    'id': '9203',
                    'value': 'Amro Abdullah  Nachef'
                }, {
                    'id': '7407',
                    'value': 'Andrew Medhat Guirguis Faried'
                }, {
                    'id': '95',
                    'value': 'Anja . Bendtschneider'
                }, {
                    'id': '165',
                    'value': 'Anke . Klingner'
                }, {
                    'id': '41',
                    'value': 'Anne Abdel Moneim Hassan'
                }, {
                    'id': '8167',
                    'value': 'Antonio Rodriguez Andres'
                }, {
                    'id': '7400',
                    'value': 'Arig mahmoud Ahmed Farag Eweida'
                }, {
                    'id': '8931',
                    'value': 'Arwa  Kadri Aly  Abdelmageed '
                }, {
                    'id': '6917',
                    'value': 'Ashraf Gaber Sayed Morsy'
                }, {
                    'id': '7990',
                    'value': 'Ashraf Osam Ahmed Elshamy'
                }, {
                    'id': '8181',
                    'value': 'Asmaa  Mahmoud Abdallah Saleh'
                }, {
                    'id': '8846',
                    'value': 'Asmaa Mohammed Abdelsalam Abas Eltaweel'
                }, {
                    'id': '6499',
                    'value': 'Asmaa Mostafa Hussien Abdelkhalek'
                }, {
                    'id': '3049',
                    'value': 'Atef Afifi Mohamed'
                }, {
                    'id': '9273',
                    'value': 'Aya  Salama Abdelhady Mohamed'
                }, {
                    'id': '8200',
                    'value': 'Aya Alaa Eldin Hassan Hassan Saleh'
                }, {
                    'id': '8302',
                    'value': 'Aya Amir Mohamed Mousa Sharaf'
                }, {
                    'id': '6902',
                    'value': 'Aya Ashraf Ezzat Ali Ismail'
                }, {
                    'id': '9161',
                    'value': 'Aya Ayman Mohamed Kamal Anwar'
                }, {
                    'id': '8939',
                    'value': 'Aya Hesham Helmy Abdelhalim'
                }, {
                    'id': '7688',
                    'value': 'Aya Hesham Soliman Mohamed Refaat'
                }, {
                    'id': '5518',
                    'value': 'Aya Mohammed Ashraf Shawkat'
                }, {
                    'id': '8625',
                    'value': 'Aya Moustpha Fathi Hussein Negm'
                }, {
                    'id': '8629',
                    'value': 'Aya Muhammad Attia Ahmad Elnahas'
                }, {
                    'id': '9187',
                    'value': 'Aya Tamer Shawky Ibrahim'
                }, {
                    'id': '6920',
                    'value': 'Aya Yasser Mohamed Hussein Ahmed Kasber'
                }, {
                    'id': '4056',
                    'value': 'Aya Zakaria Rashed'
                }, {
                    'id': '8371',
                    'value': 'Ayat  Osama Said Abdelsalam Abdallah'
                }, {
                    'id': '7205',
                    'value': 'Ayatallah Ahmed Hamdy Mohammed Abd Al-Aziz Hashem'
                }, {
                    'id': '8741',
                    'value': 'Ayham Marwan Mohamed Saleh Dalal'
                }, {
                    'id': '784',
                    'value': 'Ayman Aly Elbadawy'
                }, {
                    'id': '5442',
                    'value': 'Ayman Hamdy Nassar'
                }, {
                    'id': '8174',
                    'value': 'Ayman Mounir  Alserafi'
                }, {
                    'id': '7354',
                    'value': 'Ayman Raafat Ismail Elgndy'
                }, {
                    'id': '3157',
                    'value': 'Bakr Mohamed Rabeeh'
                }, {
                    'id': '9281',
                    'value': 'Basel Ahmed Abdelraouf Ahmed  Elkarety'
                }, {
                    'id': '8512',
                    'value': 'Basma Elsayed Ibrahim Ahmed Elghobashy '
                }, {
                    'id': '4446',
                    'value': 'Basma Mohamed  El-Shenawy'
                }, {
                    'id': '7949',
                    'value': 'Basma Mohamed Afifi Ibrahim'
                }, {
                    'id': '9409',
                    'value': 'Bassant  Essam Mahmoud Ali  Elnaggar '
                }, {
                    'id': '9300',
                    'value': 'Bassant Adel Hamza Ahmed'
                }, {
                    'id': '7619',
                    'value': 'Bassant Ahmed Mohamed Abd Elsamiea Keshta'
                }, {
                    'id': '5099',
                    'value': 'Bassant Mostafa Salah'
                }, {
                    'id': '6771',
                    'value': 'Carina Mylene Beyer'
                }, {
                    'id': '7734',
                    'value': 'Carol Tarek Farouk Doos'
                }, {
                    'id': '6413',
                    'value': 'Catherine Malak Noshy Ibrahim Elias'
                }, {
                    'id': '7079',
                    'value': 'Christian . Schubert'
                }, {
                    'id': '7558',
                    'value': 'Christian Bernhard Schmitt'
                }, {
                    'id': '6898',
                    'value': 'Christine Adel Sedky Youssef'
                }, {
                    'id': '9301',
                    'value': 'Claudine Hesham Lamei Efram'
                }, {
                    'id': '49',
                    'value': 'Dahlia Hassan Sennara'
                }, {
                    'id': '8509',
                    'value': 'Dalia Abdelwahab Hussien Abdelwahab'
                }, {
                    'id': '6059',
                    'value': 'Dalia Akram Khouzam'
                }, {
                    'id': '7210',
                    'value': 'Dalia Mamdouh Mahfouz Ali Ibrahim'
                }, {
                    'id': '4038',
                    'value': 'Dalia Sherif Elhelw'
                }, {
                    'id': '8516',
                    'value': 'Dalia Walid Mohamed Saeed Mohamed Abdallah Hewedy'
                }, {
                    'id': '4113',
                    'value': 'Danira Ashraf Habashy'
                }, {
                    'id': '3273',
                    'value': 'Darius Paul Zlotos'
                }, {
                    'id': '2609',
                    'value': 'Diana Mostafa Mohamed'
                }, {
                    'id': '5825',
                    'value': 'Dieter . Fritsch'
                }, {
                    'id': '7892',
                    'value': 'Dima Ghassan Tannir'
                }, {
                    'id': '9277',
                    'value': 'Dina  Mohamed Saad  Mohamed'
                }, {
                    'id': '5551',
                    'value': 'Dina Hady Aboushady'
                }, {
                    'id': '5514',
                    'value': 'Dina Ibrahim Ahmed'
                }, {
                    'id': '8853',
                    'value': 'Dina Mohamed Sherif Ali'
                }, {
                    'id': '3193',
                    'value': 'Dina Mohamed Yousri'
                }, {
                    'id': '8517',
                    'value': 'Dina Reda Abdelhay Mohamed Eldamak'
                }, {
                    'id': '8884',
                    'value': 'Dina Waref Lamei  Shalaby'
                }, {
                    'id': '8978',
                    'value': 'Doaa  Elsaid Hassan  Mohamed '
                }, {
                    'id': '2546',
                    'value': 'Doaa Taha El-Shihi'
                }, {
                    'id': '5149',
                    'value': 'Dominique . Mauri'
                }, {
                    'id': '8530',
                    'value': 'Donia  Hisham Mohamed  Elnaggar '
                }, {
                    'id': '4631',
                    'value': 'Donia Mohamed Eyad'
                }, {
                    'id': '2733',
                    'value': 'Ehab Ahmed Yaseen'
                }, {
                    'id': '3533',
                    'value': 'Ehab Kamel Abou-Elkheir'
                }, {
                    'id': '6538',
                    'value': 'Ehab Magdy Salah Noureldin'
                }, {
                    'id': '3155',
                    'value': 'El-Sayed Ibrahim Morgan'
                }, {
                    'id': '6764',
                    'value': 'Eman Ahmed Farouk Mahmoud Hassan'
                }, {
                    'id': '4501',
                    'value': 'Eman Ahmed Hafez'
                }, {
                    'id': '6240',
                    'value': 'Eman Ahmed Hamdy Azab'
                }, {
                    'id': '8628',
                    'value': 'Eman Fikri Hassan Hassan Elezabi'
                }, {
                    'id': '144',
                    'value': 'Engy Mohamed El Sawaf'
                }, {
                    'id': '4474',
                    'value': 'Erwin walter Herzberger'
                }, {
                    'id': '8561',
                    'value': 'Eslam Sabry Abdelrahim Mahmoud Hegazy'
                }, {
                    'id': '5055',
                    'value': 'Esraa Abdelraouf Soliman'
                }, {
                    'id': '9252',
                    'value': 'Esraa Ahmed Elmetwally Elshawaf'
                }, {
                    'id': '8595',
                    'value': 'Esraa Mahmoud Abdullah Ahmed Ewies'
                }, {
                    'id': '9302',
                    'value': 'Esraa Mohamed Gibreen Elsayed'
                }, {
                    'id': '7954',
                    'value': 'Esraa Nasif Wagdy Mohamed Mobasher'
                }, {
                    'id': '8309',
                    'value': 'Esraa Nasser Fawzy Mohamed Ezzat'
                }, {
                    'id': '8663',
                    'value': 'Esraa Wael Saeed  Farag '
                }, {
                    'id': '9197',
                    'value': 'Essraa Essam Mahmoud Ali'
                }, {
                    'id': '7217',
                    'value': 'Ethar Amr Abdelghany Abdelfattah'
                }, {
                    'id': '8201',
                    'value': 'Eyad  Mohammed Farhan  Husni'
                }, {
                    'id': '8603',
                    'value': 'Eyad Mamdouh Gaber Mohamed Ayad'
                }, {
                    'id': '8061',
                    'value': 'Ezzeldin Sobhi Metwalli Ali'
                }, {
                    'id': '2606',
                    'value': 'Fadwa Farouk Foda'
                }, {
                    'id': '6905',
                    'value': 'Farah Ahmed Ahmed Shafik Ahmed Soliman'
                }, {
                    'id': '8554',
                    'value': 'Farah Ahmed Yousry Shams Aldin Kamal Ashraf'
                }, {
                    'id': '8910',
                    'value': 'Farah khaled Mahmoud Moktar Ismail Kamaly'
                }, {
                    'id': '8830',
                    'value': 'Farah Mohamed Hussien Mohamed Abdelalem'
                }, {
                    'id': '9097',
                    'value': 'Farah Mohammed Mamdouh Mostafa Kamel Eldegheidy'
                }, {
                    'id': '8900',
                    'value': 'Farah Saadeldin Nabih Ibrahim Mohamed'
                }, {
                    'id': '9163',
                    'value': 'Farah Tarek Amin Samy Rofail'
                }, {
                    'id': '8983',
                    'value': 'Farah Yasser Hassan Hodhod'
                }, {
                    'id': '9314',
                    'value': 'Fares  Mohamed Aboelfotouh Metwaly Selim'
                }, {
                    'id': '8427',
                    'value': 'Farida  Hassan Ahmed Mostafa  Elnaggar '
                }, {
                    'id': '8551',
                    'value': 'Farida  Kamel Salah Eldin Kamel Shahin '
                }, {
                    'id': '9160',
                    'value': 'Farida Ashraf Mohamed Rafik Mohamed Abdelbaset Selim'
                }, {
                    'id': '8869',
                    'value': 'Farida Hassan Mohamed Ahmed Moubarak'
                }, {
                    'id': '8909',
                    'value': 'Farida Helmy Abdelhalim Eldessouky'
                }, {
                    'id': '8410',
                    'value': 'Farida Mahmoud Yahia Mahmoud Elhusseiny'
                }, {
                    'id': '9240',
                    'value': 'Farida Samer Ahmed  Eldesouky'
                }, {
                    'id': '8733',
                    'value': 'Farida Waleed WaheedEldin Abdelhamid'
                }, {
                    'id': '2808',
                    'value': 'Fathy Saad Helail'
                }, {
                    'id': '8573',
                    'value': 'Fatima  Alaaeldin Kamal Mohamed  Thabet '
                }, {
                    'id': '8546',
                    'value': 'Fatma Alaa Eldin Ali Fahmy Raid'
                }, {
                    'id': '8851',
                    'value': 'Fatma Hassan Mohamed Mohamed Mohamed Hamed'
                }, {
                    'id': '8959',
                    'value': 'Fatma Mohamed Ali Radwan'
                }, {
                    'id': '9313',
                    'value': 'Fatma Mohamed Ibrahim Farag'
                }, {
                    'id': '9239',
                    'value': 'Fatma Sherif Hussein Hassan Mansour'
                }, {
                    'id': '4004',
                    'value': 'Florian Becker Ritterspach'
                }, {
                    'id': '34',
                    'value': 'Frank . Gunzer'
                }, {
                    'id': '2457',
                    'value': 'Gailan Yehya Radwan'
                }, {
                    'id': '4519',
                    'value': 'Gamal Abdel Shafy'
                }, {
                    'id': '6607',
                    'value': 'Gamal Abdelhameed Mohamed Kassem'
                }, {
                    'id': '4155',
                    'value': 'Gamal Mohamed Shehata'
                }, {
                    'id': '9165',
                    'value': 'Gehan Reda Abdallah Hamed Ali'
                }, {
                    'id': '6421',
                    'value': 'George Gadallah Yakout Gadallah'
                }, {
                    'id': '8749',
                    'value': 'George Sameh Fahim Abdelsayed'
                }, {
                    'id': '6638',
                    'value': 'Ghada Salaheldin Ali Sarhan'
                }, {
                    'id': '9117',
                    'value': 'Ghada Sayed Mohamed Sayed Ghazala'
                }, {
                    'id': '7299',
                    'value': 'Ghadir Mostafa mahmoud Mangood Dogim'
                }, {
                    'id': '4822',
                    'value': 'Gilan Hamdi Hussein'
                }, {
                    'id': '7933',
                    'value': 'Gorgena Asaad Anwar Kila'
                }, {
                    'id': '6948',
                    'value': 'Gwendolyn . Kulick'
                }, {
                    'id': '8233',
                    'value': 'Habiba Gamal Aboel Fotouh Mohamed Mohamed'
                }, {
                    'id': '9261',
                    'value': 'Habiba Magdy Rashad Saadeldin Eldabaa'
                }, {
                    'id': '8934',
                    'value': 'Habiba Mahdy Mohamed Flelfel'
                }, {
                    'id': '5590',
                    'value': 'Habiba Mahmoud Shawkat'
                }, {
                    'id': '8839',
                    'value': 'Habiba Mohamed Hamdy Mohamed Elsayed'
                }, {
                    'id': '7239',
                    'value': 'Hadeel Adel Abbas  Mostafa'
                }, {
                    'id': '3236',
                    'value': 'Hadeer Emad Shawky Hammad'
                }, {
                    'id': '5150',
                    'value': 'Hadeer Mounir Ahmed'
                }, {
                    'id': '7673',
                    'value': 'Hadwa Hassan Aziz  Pasha'
                }, {
                    'id': '9205',
                    'value': 'Hagar Amin Ezzeldin Elmaghraby'
                }, {
                    'id': '3237',
                    'value': 'Hagar Samir Adib Said'
                }, {
                    'id': '8592',
                    'value': 'Hager Khaled Ali Muhamed Abuelyazid'
                }, {
                    'id': '8404',
                    'value': 'Haidy  Ashraf Mostafa Kamal  Aziz '
                }, {
                    'id': '6234',
                    'value': 'Haitham Abdelsalam Ahmed Omran'
                }, {
                    'id': '2516',
                    'value': 'Hala Magdy Gaber'
                }, {
                    'id': '799',
                    'value': 'Hamdy Abdallah Kandil'
                }, {
                    'id': '8906',
                    'value': 'Hams  Khaled Farouk  Elhefnawy'
                }, {
                    'id': '8831',
                    'value': 'Hams Wael Elgaraihy Mahmoud Dorgham'
                }, {
                    'id': '9296',
                    'value': 'Hana  Tarek Mahmoud Ahmed  elsayed'
                }, {
                    'id': '6807',
                    'value': 'Hana Medhat Abdelfattah Aly Sharabash'
                }, {
                    'id': '9072',
                    'value': 'Hana Moemen Mohamed  Samy Abdelrahman'
                }, {
                    'id': '9051',
                    'value': 'Hana Nabil Ibrahim Hussein'
                }, {
                    'id': '7759',
                    'value': 'Hana Youssef Madbouly Ibrahim Issa'
                }, {
                    'id': '9231',
                    'value': 'Haneen Ahmed Riad Elbatroukh'
                }, {
                    'id': '8570',
                    'value': 'Hania  Wael Abdelrehim Mohamed Hassan  Elhoshy '
                }, {
                    'id': '7969',
                    'value': 'Hania Waleed Tawfik Aly Elfil'
                }, {
                    'id': '171',
                    'value': 'Hans-Georg . Breitinger'
                }, {
                    'id': '3154',
                    'value': 'Hany Ahmed Ismail'
                }, {
                    'id': '495',
                    'value': 'Hany Fathy Hammad'
                }, {
                    'id': '69',
                    'value': 'Hany Mostafa El Sharkawy'
                }, {
                    'id': '3644',
                    'value': 'Hassan . Ouda'
                }, {
                    'id': '8872',
                    'value': 'Haya Mohamed Olabi'
                }, {
                    'id': '23',
                    'value': 'Haytham Osman Ismail'
                }, {
                    'id': '8961',
                    'value': 'Heba Abdelmohsen Mohamed  Basha '
                }, {
                    'id': '5262',
                    'value': 'Heba Abdullah Mohamed  El Dahshan '
                }, {
                    'id': '8932',
                    'value': 'Heba Awny Abdelhamid Ismail'
                }, {
                    'id': '6507',
                    'value': 'Heba Ehab Mohamed Elnakib'
                }, {
                    'id': '425',
                    'value': 'Heba El Sayed Handoussa'
                }, {
                    'id': '5997',
                    'value': 'Heba Ezzat Dewedar'
                }, {
                    'id': '8650',
                    'value': 'Heba Hesham Abdulhaleem  Mostafa'
                }, {
                    'id': '7714',
                    'value': 'Heba Medhat Abdel Moaz Hegazy'
                }, {
                    'id': '8211',
                    'value': 'Heba Mohamed Abdelwahab Mohamed'
                }, {
                    'id': '6061',
                    'value': 'Heba Mohammed Nafea'
                }, {
                    'id': '6275',
                    'value': 'Heba Sabry Mohamadin  Qenawy'
                }, {
                    'id': '8923',
                    'value': 'Hebaallah  Samir Tohamy Eid  Amin'
                }, {
                    'id': '87',
                    'value': 'Hebatallah Abdel Fattah Ghoneim'
                }, {
                    'id': '9228',
                    'value': 'Hebatallah Gamal Abdelnasser Mohamed  Kenawy'
                }, {
                    'id': '7111',
                    'value': 'Hebatullah Ashraf Alieldin Ibrahim Megahed'
                }, {
                    'id': '7242',
                    'value': 'Hebatullah Hamdy Mohamed Saeed Elsharawy'
                }, {
                    'id': '3426',
                    'value': 'Hebatullah Hamed Mohamed'
                }, {
                    'id': '5289',
                    'value': 'Hebatullah Samir El Gamal'
                }, {
                    'id': '8499',
                    'value': 'Hedaya Hamdi Abdelsattar Mohamed ElDamanhory'
                }, {
                    'id': '4865',
                    'value': 'Heidi Moahmed Badr'
                }, {
                    'id': '6065',
                    'value': 'Hend Abdalla Zaghloul'
                }, {
                    'id': '8560',
                    'value': 'Hend Adham Hamed Yousef'
                }, {
                    'id': '3435',
                    'value': 'Hend Mohamed Saber'
                }, {
                    'id': '7977',
                    'value': 'Hesham Ahmed Mahmoud Mostafa Hegazi'
                }, {
                    'id': '6869',
                    'value': 'Hesham Hamed Ahmed Ibrahim'
                }, {
                    'id': '6923',
                    'value': 'Hesham Sherif Elsayed Hussein'
                }, {
                    'id': '4802',
                    'value': 'Hisham Hassaballah Othman'
                }, {
                    'id': '2451',
                    'value': 'Hisham Mostafa El Sherif'
                }, {
                    'id': '8073',
                    'value': 'Hisham Sherif Gabr'
                }, {
                    'id': '150',
                    'value': 'Hoda Moustafa El Henaoui'
                }, {
                    'id': '8729',
                    'value': 'Hossam Eldin Hassan Abdelmunim'
                }, {
                    'id': '7324',
                    'value': 'Husam Riad  Husain'
                }, {
                    'id': '5467',
                    'value': 'Hussam Hussein Salama'
                }, {
                    'id': '9235',
                    'value': 'Hussein  Ashraf Hussein  Sallam'
                }, {
                    'id': '115',
                    'value': 'Hussein Raafat Nabil Raafat'
                }, {
                    'id': '251',
                    'value': 'Ihab . Nadim'
                }, {
                    'id': '7836',
                    'value': 'Iman Mostafa Ibrahim El-Batouty'
                }, {
                    'id': '2435',
                    'value': 'Inas Esmat Ezz'
                }, {
                    'id': '9178',
                    'value': 'Ingy Ashraf Herzi Rezkalla Fam'
                }, {
                    'id': '145',
                    'value': 'Ingy Mohamed Abou-Zeid'
                }, {
                    'id': '9380',
                    'value': 'Irina . Goryacheva'
                }, {
                    'id': '8728',
                    'value': 'Islam Ahmed Mahmoud Elmaddah'
                }, {
                    'id': '7408',
                    'value': 'Islam Saher Elsayed Fahmy  Hegazy'
                }, {
                    'id': '2389',
                    'value': 'Islam Tariq Eddiasty'
                }, {
                    'id': '8254',
                    'value': 'Jane Fayez Abdou Marzouk'
                }, {
                    'id': '9324',
                    'value': 'Janne Tuomas Terasvirta'
                }, {
                    'id': '8966',
                    'value': 'Jano Baher Boulos Abdo El Swefy'
                }, {
                    'id': '8285',
                    'value': 'Jasmin Abdel Moneim Wardani'
                }, {
                    'id': '8867',
                    'value': 'Jessica Magdy Gergis Haleem '
                }, {
                    'id': '7760',
                    'value': 'Joy Samuel Labib Maher'
                }, {
                    'id': '9153',
                    'value': 'Jutta Bernadette Wacht'
                }, {
                    'id': '8420',
                    'value': 'Kareem  Elsayed Ahmed Abdelrahman'
                }, {
                    'id': '9183',
                    'value': 'Kareem Ahmed Mohamed Mahmoud Hindi'
                }, {
                    'id': '8841',
                    'value': 'Karen Magdy Anwar Ayoub Girgis'
                }, {
                    'id': '9191',
                    'value': 'Karim Ahmed Abdelaziz Ahmed Mahmoud'
                }, {
                    'id': '9382',
                    'value': 'Karim Hassan Mohamed Soliman Maaly'
                }, {
                    'id': '8870',
                    'value': 'Karim Mohamed Elsayed Abdelsalam'
                }, {
                    'id': '9263',
                    'value': 'Karim Mohamed Ragab Hamed'
                }, {
                    'id': '9241',
                    'value': 'Karim Walid Mohamed Mohamed Mahmoud Hussenin'
                }, {
                    'id': '8376',
                    'value': 'Kathryn  Victoria Best'
                }, {
                    'id': '9227',
                    'value': 'Khaled  Tarek Mohamed Mohamed Kassem'
                }, {
                    'id': '8848',
                    'value': 'Khaled Ahmed Fawzy Mohamed Salem'
                }, {
                    'id': '84',
                    'value': 'Khaled Mohamed Abou Aisha'
                }, {
                    'id': '9436',
                    'value': 'Khaled Mohamed Ibrahim Eladawy Nowara'
                }, {
                    'id': '6460',
                    'value': 'Kholoud Abdelaziz Mahmoud Abdelrehim  Ahmed'
                }, {
                    'id': '7474',
                    'value': 'Ksenia Georgieu Nikolskaya'
                }, {
                    'id': '9216',
                    'value': 'Laila Ahmed Osman  Gohar'
                }, {
                    'id': '7713',
                    'value': 'Laila Sherif Hamdy Bahloul'
                }, {
                    'id': '3209',
                    'value': 'Lamia Ahmed Shihata'
                }, {
                    'id': '8767',
                    'value': 'Lamiaa Mohamed Hamed Ahmed Eltantawi'
                }, {
                    'id': '6822',
                    'value': 'Lise Abdel Masih Fakhri Aziz'
                }, {
                    'id': '6066',
                    'value': 'Liza Samir Botros'
                }, {
                    'id': '133',
                    'value': 'Lobna Abdel Aal Kassem'
                }, {
                    'id': '6891',
                    'value': 'Lobna Ali Onsi Abdel Salam Mohamed'
                }, {
                    'id': '5503',
                    'value': 'Lobna Mohamed Abdelrauf'
                }, {
                    'id': '8970',
                    'value': 'Luis Pedro Costa Gomes'
                }, {
                    'id': '8439',
                    'value': 'Luna  Ahmed Osman Mohamed  Elhifni '
                }, {
                    'id': '8294',
                    'value': 'Magdi Elsayed Mohamed Mohamed Mostafa'
                }, {
                    'id': '3766',
                    'value': 'Maggie Ahmed Ezzat Mashaly'
                }, {
                    'id': '6423',
                    'value': 'Maggie Ihab Maher Shafik  Shamma '
                }, {
                    'id': '5509',
                    'value': 'Magy Maged Wadea'
                }, {
                    'id': '9417',
                    'value': 'Maha Aboubakr Ibrahim Ibrahim'
                }, {
                    'id': '7256',
                    'value': 'Maha Ahmed Hassan  Shawki'
                }, {
                    'id': '3494',
                    'value': 'Maha Ayman Hamdy'
                }, {
                    'id': '332',
                    'value': 'Maha Gamal El-Din Ahmed'
                }, {
                    'id': '6396',
                    'value': 'Maha Hesham Elsayed  Elfeshawy'
                }, {
                    'id': '9307',
                    'value': 'Maha Saeed Helmy Ahmed Mohamed'
                }, {
                    'id': '8220',
                    'value': 'Maher Mohammed Abo Elftoh'
                }, {
                    'id': '8536',
                    'value': 'Mahinar Mohamed Ahmed Reda Mohamed'
                }, {
                    'id': '7740',
                    'value': 'Mahmoud Ahmed Mahmoud Mabrouk Ahmed'
                }, {
                    'id': '4884',
                    'value': 'Mahmoud El Khafif'
                }, {
                    'id': '4482',
                    'value': 'Mahmoud Ibrahim Khalil'
                }, {
                    'id': '8594',
                    'value': 'Mahmoud Saad Rabie Bayoomi Hamza'
                }, {
                    'id': '8433',
                    'value': 'Mai  Mohamed Tarek Abdelaziz Mohamed Ghannoum '
                }, {
                    'id': '7671',
                    'value': 'Mai Adel Mohamed Ibrahim'
                }, {
                    'id': '8951',
                    'value': 'Mai El Motasem Hussein Kamel Masoud'
                }, {
                    'id': '5777',
                    'value': 'Mai Magdy Mohamed Sleim'
                }, {
                    'id': '9206',
                    'value': 'Mai Mahmoud Ahmed Abdeldayem'
                }, {
                    'id': '7900',
                    'value': 'Mai Medhat Mahmoud Momtaz'
                }, {
                    'id': '9200',
                    'value': 'Mai Mohsen Tawfik Kamel Gaweesh'
                }, {
                    'id': '8269',
                    'value': 'Maiada Magdy Abdel Majeed'
                }, {
                    'id': '8218',
                    'value': 'Manar Hatem Elmansy  Mohamed'
                }, {
                    'id': '6747',
                    'value': 'Manar Karam Moawwad Aly'
                }, {
                    'id': '74',
                    'value': 'Manar Mahmoud Soliman'
                }, {
                    'id': '8597',
                    'value': 'Manlio . Michieletto'
                }, {
                    'id': '8835',
                    'value': 'Maria Sherif Gamal  Reiad'
                }, {
                    'id': '9202',
                    'value': 'Mariam  Ahmed Helmy Mahmoud'
                }, {
                    'id': '8963',
                    'value': 'Mariam  Ayman Moustafa  Abdelrahman'
                }, {
                    'id': '9303',
                    'value': 'Mariam  Khaled Mohamed  Aboelnaga Darwesh'
                }, {
                    'id': '9245',
                    'value': 'Mariam  Mahmoud Mostafa  Elsharnouby'
                }, {
                    'id': '8522',
                    'value': 'Mariam  Mohamed  Nabil '
                }, {
                    'id': '8568',
                    'value': 'Mariam  Mohamed Abdelhakem Mahmoud  Mohamed '
                }, {
                    'id': '8990',
                    'value': 'Mariam  Tamer Mohamed  Hamdy sabry Elshakankiry'
                }, {
                    'id': '9214',
                    'value': 'Mariam  Yehia Mahmoud  Abobasha'
                }, {
                    'id': '8507',
                    'value': 'Mariam Ahmed Adel Mahmoud  Elemary '
                }, {
                    'id': '6895',
                    'value': 'Mariam Ahmed Mohamed Azab'
                }, {
                    'id': '8954',
                    'value': 'Mariam Ashraf Abdalla El Sebaie'
                }, {
                    'id': '8875',
                    'value': 'Mariam Ayman Mamdoh Abdelmonem  Eloraby'
                }, {
                    'id': '8863',
                    'value': 'Mariam Ehab Azmy  Malak '
                }, {
                    'id': '4990',
                    'value': 'Mariam Hussein Madkour'
                }, {
                    'id': '8578',
                    'value': 'Mariam Mohamed Elsayed Ibrahim Salem'
                }, {
                    'id': '8180',
                    'value': 'Mariam Mohamed Halim Sabri Mostafa Sabri'
                }, {
                    'id': '8888',
                    'value': 'Mariam Mohamed Hamdy Eissa '
                }, {
                    'id': '7695',
                    'value': 'Mariam Morgan Kamel Morgan'
                }, {
                    'id': '8894',
                    'value': 'Mariam Wael Talaat  Sayed'
                }, {
                    'id': '6503',
                    'value': 'Marian Magdy Ramsis Tadros'
                }, {
                    'id': '9114',
                    'value': 'Marina Ehab Youssef Ghaly'
                }, {
                    'id': '5535',
                    'value': 'Marina Sherif Beshay'
                }, {
                    'id': '8101',
                    'value': 'Martina Yacoub Salides Yacoub Salides'
                }, {
                    'id': '8212',
                    'value': 'Marwa  Mostafa Abdelazim  Khalil'
                }, {
                    'id': '8213',
                    'value': 'Marwa Ali Abdelrafie Youssef Mohamed'
                }, {
                    'id': '7163',
                    'value': 'Marwa Gamal Abdel Hamid Gadallah'
                }, {
                    'id': '8856',
                    'value': 'Marwa Mahmoud Mohamed Mohamed Abla'
                }, {
                    'id': '7060',
                    'value': 'Marwa Mohamed Abdel Halim'
                }, {
                    'id': '8008',
                    'value': 'Marwa Muhammed Abbas Abdelmeguid'
                }, {
                    'id': '7551',
                    'value': 'Marwa Shaaban Abdel Aziz Ahmed'
                }, {
                    'id': '8602',
                    'value': 'Marwan Magdy Abdelaziz Emam Elbeialy'
                }, {
                    'id': '9162',
                    'value': 'Marwan Tarek Sherif Almaraghi'
                }, {
                    'id': '9242',
                    'value': 'Maryam Sayed Zayn Alabdeen Ali'
                }, {
                    'id': '9106',
                    'value': 'Mayar Mohamed Abdalla Mohamed Tawfek'
                }, {
                    'id': '7960',
                    'value': 'Mayar Osama Abdel-Satar Abdel-Wahab'
                }, {
                    'id': '7640',
                    'value': 'Mayar Ossama Sami Foaad Reda'
                }, {
                    'id': '7288',
                    'value': 'Mayar Waleed Salah Eldin Aly'
                }, {
                    'id': '9299',
                    'value': 'Mayssa Abdelhamid Mahmoud Soliman'
                }, {
                    'id': '2484',
                    'value': 'Menat Allah Mohamed Samir Darrag'
                }, {
                    'id': '8660',
                    'value': 'Menatalla Ashraf Ragaey Azab Osman'
                }, {
                    'id': '4489',
                    'value': 'Menatullah Mohsen Anwar'
                }, {
                    'id': '6133',
                    'value': 'Menna Mohamed Ragab'
                }, {
                    'id': '8881',
                    'value': 'Menna Mohamed Saeed Abdelaziz Bedir '
                }, {
                    'id': '6813',
                    'value': 'Mennaallah Yasser elsayed elbadawy Abdelfadel Khalifa'
                }, {
                    'id': '8548',
                    'value': 'Mennatallah  Hassan Saleh Abdo  Mohamed '
                }, {
                    'id': '8531',
                    'value': 'Mennatallah Abdelnasser Mohamed Abdou  Abdou'
                }, {
                    'id': '9329',
                    'value': 'Mennatallah Ahmed Aly Moussa Moussa'
                }, {
                    'id': '9406',
                    'value': 'Mennatallah Ahmed Elsayed Zaki Abouelatta'
                }, {
                    'id': '8275',
                    'value': 'Mennatallah Khaled Mohamed Gamal Ibrahim Helmy'
                }, {
                    'id': '6514',
                    'value': 'Mennatallah Mohamed Ahmed Abdallah'
                }, {
                    'id': '7246',
                    'value': 'Mennatallah Reyad Abdelazim Seliman'
                }, {
                    'id': '7604',
                    'value': 'Mennatallah Wael Mahmoud Mohamed Abd Elrahman'
                }, {
                    'id': '8928',
                    'value': 'Mennatullah Nasser Omar Ahmed'
                }, {
                    'id': '8967',
                    'value': 'Mennatullah Sayed Kamal Sayed Mohamed Kamal'
                }, {
                    'id': '6800',
                    'value': 'Menrit Hanna Mosaad Moawad'
                }, {
                    'id': '6909',
                    'value': 'Merhan Mostafa ElSayed Mohamed'
                }, {
                    'id': '8222',
                    'value': 'Meriam Ahmed Mahmoud Khalifa'
                }, {
                    'id': '7279',
                    'value': 'Merihan Ali Ibrahim Ali Attia'
                }, {
                    'id': '5683',
                    'value': 'Merna Hany Sabry'
                }, {
                    'id': '7642',
                    'value': 'Merna Saleh Shokry Emam Elawamry'
                }, {
                    'id': '8571',
                    'value': 'Merna Tamer Essam Eltersawy'
                }, {
                    'id': '7196',
                    'value': 'Mervat Mustafa Fahmy Abuelkheir'
                }, {
                    'id': '8060',
                    'value': 'Meryem Kübra Uluç Tolba'
                }, {
                    'id': '6325',
                    'value': 'Michael . Eichner'
                }, {
                    'id': '6832',
                    'value': 'Michael Emad George Moawad'
                }, {
                    'id': '7899',
                    'value': 'Milad Michel Ghantous'
                }, {
                    'id': '9280',
                    'value': 'Mina Maher Shawky Lazeem'
                }, {
                    'id': '8908',
                    'value': 'Mina Sherif Mourice Abdelkodous Saeed Elnomeir'
                }, {
                    'id': '3288',
                    'value': 'Minar Abbas El-Aasser'
                }, {
                    'id': '9212',
                    'value': 'Minora Mohamed Abdelrahman Ibrahim Hafez'
                }, {
                    'id': '7332',
                    'value': 'Mirna Bassem Emile Sadek'
                }, {
                    'id': '6912',
                    'value': 'Mirna Maged Sobhi Andrawes'
                }, {
                    'id': '6067',
                    'value': 'Mirna Victor Azmy'
                }, {
                    'id': '9075',
                    'value': 'Moaaz Nasser Ibrahim Eldessouky Shaban'
                }, {
                    'id': '9395',
                    'value': 'Moaz Ayman Aref Abdelghany'
                }, {
                    'id': '9071',
                    'value': 'Mohab  Gehad Ibrahim Abdelrahman Ahmed '
                }, {
                    'id': '8849',
                    'value': 'Mohab Assem Mohamed Elsayed Deyab'
                }, {
                    'id': '9069',
                    'value': 'Mohamad . Dakak'
                }, {
                    'id': '5161',
                    'value': 'Mohamad Hesham  Zamzam'
                }, {
                    'id': '8887',
                    'value': 'Mohamed  Ahmed Mohamed Zaki Abdelgawad'
                }, {
                    'id': '9073',
                    'value': 'Mohamed  Alaa Eldeen  Salah Ali'
                }, {
                    'id': '7300',
                    'value': 'Mohamed  Magdy Mohamed Hassan Nafea'
                }, {
                    'id': '6276',
                    'value': 'Mohamed  Mohamed Borhaneldin Gamal Hammad'
                }, {
                    'id': '4145',
                    'value': 'Mohamed  Shawky Hafez'
                }, {
                    'id': '6904',
                    'value': 'Mohamed Abdalhameed Mohamed Ahmed Soliman'
                }, {
                    'id': '2491',
                    'value': 'Mohamed Abdel Ghany Ahmed Salem'
                }, {
                    'id': '9247',
                    'value': 'Mohamed Abdelrahman Mahmoud Elrachidy'
                }, {
                    'id': '9437',
                    'value': 'Mohamed Abdelraouf Ali Mohamed'
                }, {
                    'id': '8926',
                    'value': 'Mohamed Adel mohamed Elshafei '
                }, {
                    'id': '8857',
                    'value': 'Mohamed Ahmed Awaad Ibrahim'
                }, {
                    'id': '2341',
                    'value': 'Mohamed Ahmed El-Azizi'
                }, {
                    'id': '7384',
                    'value': 'Mohamed Ahmed Mohamed  Hussein'
                }, {
                    'id': '8914',
                    'value': 'Mohamed Ahmed Salaheldin Mohamed Ibrahim'
                }, {
                    'id': '9027',
                    'value': 'Mohamed Alaa El Din Ahmed El Sayed Aly  Iskandar El Assyouty'
                }, {
                    'id': '8656',
                    'value': 'Mohamed Amr Anwar Mahmoud Elsheshtawy'
                }, {
                    'id': '8539',
                    'value': 'Mohamed Ayman Abuelmagd Mohamed'
                }, {
                    'id': '9204',
                    'value': 'Mohamed Ayman Fathy Hassan Ahmed'
                }, {
                    'id': '2425',
                    'value': 'Mohamed Ehsan Ashour'
                }, {
                    'id': '8575',
                    'value': 'Mohamed Elsayed Shaaban Ali Ali'
                }, {
                    'id': '6040',
                    'value': 'Mohamed Elwi Mitwally'
                }, {
                    'id': '6079',
                    'value': 'Mohamed Emam Mohamed'
                }, {
                    'id': '8730',
                    'value': 'Mohamed Hamed Fahmy'
                }, {
                    'id': '7383',
                    'value': 'Mohamed Hassan Solayman'
                }, {
                    'id': '6658',
                    'value': 'Mohamed Hemieda Abdelaziz'
                }, {
                    'id': '4135',
                    'value': 'Mohamed Kamel Gabr'
                }, {
                    'id': '8274',
                    'value': 'Mohamed Magdy Hamed Elsayed Shalma'
                }, {
                    'id': '7719',
                    'value': 'Mohamed Mahmoud Elsayed Bedier'
                }, {
                    'id': '9179',
                    'value': 'Mohamed Medhat Adel Helal'
                }, {
                    'id': '7774',
                    'value': 'Mohamed Mohamed El-Sayed Atteya'
                }, {
                    'id': '9381',
                    'value': 'Mohamed Mohsen Ahmed Shehata'
                }, {
                    'id': '9238',
                    'value': 'Mohamed Mohsen Mohy Eldin Mahmoud'
                }, {
                    'id': '7755',
                    'value': 'Mohamed Okasha Abd Elaal'
                }, {
                    'id': '9093',
                    'value': 'Mohamed Omar Saad Abdelwahab'
                }, {
                    'id': '3453',
                    'value': 'Mohamed Saad Mohamed'
                }, {
                    'id': '8171',
                    'value': 'Mohamed Salman Mohamed Salama'
                }, {
                    'id': '6628',
                    'value': 'Mohamed Samir Midani'
                }, {
                    'id': '8935',
                    'value': 'Mohamed Sherif Mohamed Samir Mohamed Ali'
                }, {
                    'id': '9059',
                    'value': 'Mohamed Tarek Ahmed Mohamed Helmy'
                }, {
                    'id': '6439',
                    'value': 'Mohamed Tarek Salah Eldin  Ahmed Abdelmawgoud'
                }, {
                    'id': '9211',
                    'value': 'Mohamed Walid Elsayed Gomaa Abdelaziz'
                }, {
                    'id': '2638',
                    'value': 'Mohamed Zakaria Gad'
                }, {
                    'id': '519',
                    'value': 'Mohammad Abdel Halim Abdel Naby'
                }, {
                    'id': '5734',
                    'value': 'Mohammed Abdel Megeed Salem'
                }, {
                    'id': '3042',
                    'value': 'Mohammed Abdelkhalek Youseef'
                }, {
                    'id': '3615',
                    'value': 'Mohammed Abdelmoomen Elshehawy'
                }, {
                    'id': '9293',
                    'value': 'Mohammed Abdelraouf Abdeen'
                }, {
                    'id': '3559',
                    'value': 'Mohammed Salama Abdelhady Mohamed'
                }, {
                    'id': '7091',
                    'value': 'Mohsen Shokry Elshamaa'
                }, {
                    'id': '8524',
                    'value': 'Moira Adel Abdelsalam Ali'
                }, {
                    'id': '5792',
                    'value': 'Mona Abdelaziz Mohamed'
                }, {
                    'id': '3640',
                    'value': 'Mona Abdelmoniem  Marie'
                }, {
                    'id': '5281',
                    'value': 'Mona Abdelsalam Sayed hassan  Elbannan'
                }, {
                    'id': '4436',
                    'value': 'Mona Ahmed Hassan'
                }, {
                    'id': '445',
                    'value': 'Mona Ahmed Hassan Rady'
                }, {
                    'id': '9246',
                    'value': 'Mona Ali Abdelmoomen Ali Mosa'
                }, {
                    'id': '8403',
                    'value': 'Mona Ali Ekram  Ali '
                }, {
                    'id': '5580',
                    'value': 'Mona Badr Eldin Anwar'
                }, {
                    'id': '8918',
                    'value': 'Mona Wagdy Abdelghaffar Hussien '
                }, {
                    'id': '7322',
                    'value': 'Monica Boulos Francois Badie Sakla'
                }, {
                    'id': '6901',
                    'value': 'Monica Mohsen Fahmy Armanious'
                }, {
                    'id': '6069',
                    'value': 'Monica Mosaad Abdel Malek'
                }, {
                    'id': '5999',
                    'value': 'Mostafa Abdelaziz Elwaily'
                }, {
                    'id': '9176',
                    'value': 'Mostafa Ahmed Mostafa Mohamed ElBahloul'
                }, {
                    'id': '9026',
                    'value': 'Mostafa Mahdy Mohamed Abdellatif'
                }, {
                    'id': '9404',
                    'value': 'Mostafa Moataz Abdelmagid Mahmoud Abdelmagid'
                }, {
                    'id': '4800',
                    'value': 'Moustafa Ahmed Baraka'
                }, {
                    'id': '9208',
                    'value': 'Moustafa Ashraf Abdeltawab Ibrahim Khattab'
                }, {
                    'id': '8608',
                    'value': 'Mustafa Ismail Mohammed Ismail'
                }, {
                    'id': '8463',
                    'value': 'Nabil  Mohamed Yehia Aly  Abotaleb'
                }, {
                    'id': '2565',
                    'value': 'Nabila . Hamdi'
                }, {
                    'id': '8434',
                    'value': 'Nada  Ashraf Mohamed Kamel  Emam '
                }, {
                    'id': '8745',
                    'value': 'Nada  Ayman Abdelraouf Mohamed  Mahran '
                }, {
                    'id': '8997',
                    'value': 'Nada  Mohamed Hammad  Mansour'
                }, {
                    'id': '8273',
                    'value': 'Nada  Walid Elsayed  Abdelaziz'
                }, {
                    'id': '4106',
                    'value': 'Nada Ahmed Hamed'
                }, {
                    'id': '5330',
                    'value': 'Nada Alaa Bahaa'
                }, {
                    'id': '4203',
                    'value': 'Nada Ammar Mohamed Abouelftoh Ezzatlo'
                }, {
                    'id': '8246',
                    'value': 'Nada Ashraf Abdelazim Elsayed Mahmoud Rostum'
                }, {
                    'id': '8526',
                    'value': 'Nada Ashraf Youssef Elhag Ahmed '
                }, {
                    'id': '9276',
                    'value': 'Nada Hani Abdulhadi Ibrahim Mettwalli'
                }, {
                    'id': '7733',
                    'value': 'Nada Hesham Abd Elfattah Ibrahim'
                }, {
                    'id': '8836',
                    'value': 'Nada Khaled Mostafa Ali Khalaf'
                }, {
                    'id': '7307',
                    'value': 'Nada Mohamed Mohamed Aboelyazid Ragab'
                }, {
                    'id': '8942',
                    'value': 'Nada Mohyeldin Naguib  Bakeer'
                }, {
                    'id': '7694',
                    'value': 'Nada Nasser Fawzy Ramadan'
                }, {
                    'id': '7610',
                    'value': 'Nada Omar Mahmoud Abdel Ghany Zoher'
                }, {
                    'id': '6459',
                    'value': 'Nada Osama Mohamed Hussein Elmossier'
                }, {
                    'id': '9237',
                    'value': 'Nada Sayed Sayed Salheen  Sayed'
                }, {
                    'id': '9194',
                    'value': 'Nada Tamer Abdelhay Abdellatif'
                }, {
                    'id': '8640',
                    'value': 'Nadeen Amged Elamir Tadros Michael'
                }, {
                    'id': '8591',
                    'value': 'Nadeen Elsayed Hamza Elboraey'
                }, {
                    'id': '7284',
                    'value': 'Nadeen Emadeldin Mohamed Omar'
                }, {
                    'id': '8827',
                    'value': 'Nadeen Tarek Zayan Abdelhafez'
                }, {
                    'id': '331',
                    'value': 'Nadia . Yassin'
                }, {
                    'id': '443',
                    'value': 'Nadia Mohamed Sharaf'
                }, {
                    'id': '6125',
                    'value': 'Nadia Raafat Elmasry'
                }, {
                    'id': '7939',
                    'value': 'Nadine Omar Youssef Ismail'
                }, {
                    'id': '8191',
                    'value': 'Nadine Tarek Abdelrahman Mohamed Garir'
                }, {
                    'id': '449',
                    'value': 'Nadja . Badran'
                }, {
                    'id': '9215',
                    'value': 'Nagat Seifeldin Ali Marzouk'
                }, {
                    'id': '669',
                    'value': 'Naglaa Abdel Wahed Mohamed'
                }, {
                    'id': '8277',
                    'value': 'Naglaa Abozeid Abdelaziz Abdelwahab'
                }, {
                    'id': '5035',
                    'value': 'Nagy Fouad Hanna'
                }, {
                    'id': '798',
                    'value': 'Nahed Abdel Hamid El Mahallawy'
                }, {
                    'id': '9186',
                    'value': 'Nahla Atef Abdelhai Mostafa'
                }, {
                    'id': '4577',
                    'value': 'Nahla Hosny Ahmed Osman Elgizawy'
                }, {
                    'id': '5539',
                    'value': 'Nancy Osama Turky'
                }, {
                    'id': '8855',
                    'value': 'Nawraz Saeed Mohamed Saeed'
                }, {
                    'id': '2348',
                    'value': 'Nazem Adib Behnam'
                }, {
                    'id': '8924',
                    'value': 'Nermeen  Abobakr Omar  Mohamed'
                }, {
                    'id': '8837',
                    'value': 'Nermeen Ashraf Fathi Abdelwahab Kansowah'
                }, {
                    'id': '285',
                    'value': 'Nermeen Mohamed Serag El-Din'
                }, {
                    'id': '2567',
                    'value': 'Nermien Mamdouh Fouaad'
                }, {
                    'id': '8515',
                    'value': 'Nermin Ezzy Sems  Aziz'
                }, {
                    'id': '4682',
                    'value': 'Nermin Hosny Ibrahim'
                }, {
                    'id': '431',
                    'value': 'Nermin Salah Ahmed'
                }, {
                    'id': '4059',
                    'value': 'Nervana Nabil Adeeb'
                }, {
                    'id': '7746',
                    'value': 'Nesma Sayed Abdel Moneim'
                }, {
                    'id': '2393',
                    'value': 'Nesrine Abd El-Rehim El-Gohary'
                }, {
                    'id': '6759',
                    'value': 'Neveen Farag Ayoub Farag'
                }, {
                    'id': '8576',
                    'value': 'Neveen Mahfouz Mabrouk Abdelhady'
                }, {
                    'id': '8962',
                    'value': 'Nevine Mahmoud Saeed Hammouda'
                }, {
                    'id': '8550',
                    'value': 'Nihal  Mohamed Anwar Saleh  Elhawary '
                }, {
                    'id': '8325',
                    'value': 'Nihal Ali Metwally'
                }, {
                    'id': '8981',
                    'value': 'Nike . Dieterich'
                }, {
                    'id': '3707',
                    'value': 'Nikolai . Burger'
                }, {
                    'id': '7684',
                    'value': 'Noha Abdellatif Hamid Abdellatif'
                }, {
                    'id': '5998',
                    'value': 'Noha Ali Atta Hassan'
                }, {
                    'id': '4352',
                    'value': 'Noha Desouky Abdelmoaty'
                }, {
                    'id': '8261',
                    'value': 'Noha Ehab Mohamed Sobhy Ali Elshafiea'
                }, {
                    'id': '8605',
                    'value': 'Noha Fathy Ibrahim  Hafez '
                }, {
                    'id': '3579',
                    'value': 'Noha Foad Elhadary'
                }, {
                    'id': '152',
                    'value': 'Noha Hesham El-Bassiouny'
                }, {
                    'id': '6562',
                    'value': 'Noha Mohamed Abdeltawab Elsaid Saleh'
                }, {
                    'id': '470',
                    'value': 'Noha Omaya Shabana'
                }, {
                    'id': '3215',
                    'value': 'Noha Samir Farag'
                }, {
                    'id': '7706',
                    'value': 'Noha Sherif Sayed Kamel Mohamed Hassan Elzeny'
                }, {
                    'id': '9080',
                    'value': 'Noha Tarek Amin Mohamed Amer'
                }, {
                    'id': '6546',
                    'value': 'Nora Adel Salem'
                }, {
                    'id': '6796',
                    'value': 'Noran Khaled Nabil Darwish'
                }, {
                    'id': '8865',
                    'value': 'Nour Ahmed Abdelaleem Elajiry'
                }, {
                    'id': '8854',
                    'value': 'Nour Amr Amin Idris'
                }, {
                    'id': '8955',
                    'value': 'Nour Amr Hamed Abdelhamid Helaly'
                }, {
                    'id': '7011',
                    'value': 'Noura Abdel Shafi Ali Abdel Shafi'
                }, {
                    'id': '5506',
                    'value': 'Noura Ayman Ahmed'
                }, {
                    'id': '7953',
                    'value': 'Nouran Adel Hassan Ahmed'
                }, {
                    'id': '9169',
                    'value': 'Nouran Amr Abdelaziz Ahmed Elfaramawy'
                }, {
                    'id': '5001',
                    'value': 'Nouran Mohamed Arafat'
                }, {
                    'id': '6411',
                    'value': 'Nouran Zakaria Abdelhamid Mohamed'
                }, {
                    'id': '9379',
                    'value': 'Noureldin Yehia Mohamed Amin Abdelazem Elsehimy'
                }, {
                    'id': '9398',
                    'value': 'Nourhan Abdelmonem Saad Mohamed Elgendy'
                }, {
                    'id': '7924',
                    'value': 'Nourhan Ahmed Kamal El Fransawy'
                }, {
                    'id': '5447',
                    'value': 'Nourhan Ehab Azab'
                }, {
                    'id': '8915',
                    'value': 'Nourhan Hani Rezq Afifi Ali'
                }, {
                    'id': '7293',
                    'value': 'Nourhan Ramadan Ibrahim Ramadan'
                }, {
                    'id': '7753',
                    'value': 'Ola Ahmed Fouad Ahmed'
                }, {
                    'id': '8850',
                    'value': 'Ola Emad Eldeen Zakaria Abdelsalam'
                }, {
                    'id': '7428',
                    'value': 'Ola Farouk Ahmed Zaher'
                }, {
                    'id': '8538',
                    'value': 'Omar Abdelmonem Mohammed Mohammed Hassan'
                }, {
                    'id': '9304',
                    'value': 'Omar Amir Taher Abdullah Arafa  Elsheikh'
                }, {
                    'id': '3775',
                    'value': 'Omar Mahmoud Mohamed Shehata'
                }, {
                    'id': '7267',
                    'value': 'Omar Medhat khairy Ahmed'
                }, {
                    'id': '8257',
                    'value': 'Omar Mohamed Sakr Eshak  Alsaad'
                }, {
                    'id': '8882',
                    'value': 'Omar Osama Mahmoud  Elnagar'
                }, {
                    'id': '7940',
                    'value': 'Omar Sameh Mohammed Salem'
                }, {
                    'id': '8843',
                    'value': 'Omar Tarek Adly Abdella Ali'
                }, {
                    'id': '8992',
                    'value': 'Omnia  Abdelwahab Mohamed  Elqaramany '
                }, {
                    'id': '9295',
                    'value': 'Omnia Gamal Abdo Abdallah Eissa'
                }, {
                    'id': '6493',
                    'value': 'Omnia Mohamed Nabil Osman Mohamed Elsayed'
                }, {
                    'id': '8562',
                    'value': 'Omnia Sherif Elsaid  Elhosseiny '
                }, {
                    'id': '7468',
                    'value': 'Omniya Mahmoud abdel Aty Mahmoud Serry'
                }, {
                    'id': '9410',
                    'value': 'Osama Khairy Saleh Eraky'
                }, {
                    'id': '8580',
                    'value': 'Pardis Helmy Youssef Ardash'
                }, {
                    'id': '8549',
                    'value': 'Parthiena  Maher Keddis  Ibrahim '
                }, {
                    'id': '4463',
                    'value': 'Passant  Khaled  Abbassy'
                }, {
                    'id': '7777',
                    'value': 'Paula Fayez Fouad Tanaghy'
                }, {
                    'id': '5038',
                    'value': 'Peter . Blodau'
                }, {
                    'id': '8540',
                    'value': 'Peter Nabil Thabet  Wassef '
                }, {
                    'id': '8271',
                    'value': 'Pillara Mohamed Abdelmoniem Moawad Moftah'
                }, {
                    'id': '3825',
                    'value': 'Radwa Abdelmoniem Mohamed'
                }, {
                    'id': '8898',
                    'value': 'Radwa Amr Mohamed Rabie Abdelsalam Elsayed'
                }, {
                    'id': '7987',
                    'value': 'Radwa Mohamed Eid Mohamed Rostom'
                }, {
                    'id': '6876',
                    'value': 'Radwa Mohamed Ramzy Refaat Abouel Enein'
                }, {
                    'id': '7678',
                    'value': 'Radwa Mokhtar Mohamed Ahmed'
                }, {
                    'id': '8885',
                    'value': 'Raghda  Essam Taha Othman  Hussien '
                }, {
                    'id': '9236',
                    'value': 'Raghda  Mohamed Baioumy Mohamed  Hassan'
                }, {
                    'id': '6031',
                    'value': 'Raghda Abdelmonem Elsabbagh'
                }, {
                    'id': '8760',
                    'value': 'Raghda Fawzy Mohamed Ahmed Hassan'
                }, {
                    'id': '780',
                    'value': 'Raghda M. Mamdouh Elebrashi'
                }, {
                    'id': '6020',
                    'value': 'Raghda Sayed Abdelaziz'
                }, {
                    'id': '429',
                    'value': 'Ragwa Mansour Abdelghany'
                }, {
                    'id': '8447',
                    'value': 'Rahma  Shehab Eldin Mohamed Abu Taleb'
                }, {
                    'id': '9209',
                    'value': 'Rahma Mohamed Fawzy Seliman'
                }, {
                    'id': '8661',
                    'value': 'Rama  Mohamed Elsayed Mohamed  Elmor '
                }, {
                    'id': '8505',
                    'value': 'Ramez Mohamed Ibrahim Elmasry'
                }, {
                    'id': '8610',
                    'value': 'Rana Ahmed Sayed Hassan El-Sayed'
                }, {
                    'id': '9168',
                    'value': 'Rana Elsayed Shahat Mahmoud'
                }, {
                    'id': '9180',
                    'value': 'Rana Emad Nahas Ahmed'
                }, {
                    'id': '8672',
                    'value': 'Rana Emadeldin Mohammed Mahmoud'
                }, {
                    'id': '3540',
                    'value': 'Rana Hassan Abdallah Hassan Alnahal'
                }, {
                    'id': '7233',
                    'value': 'Rana Mohamed Mohsen Mohamed Yousef Amer'
                }, {
                    'id': '5985',
                    'value': 'Rana Mostafa Ismail'
                }, {
                    'id': '8600',
                    'value': 'Rana Salah Morsy Sayed'
                }, {
                    'id': '8874',
                    'value': 'Rana Samer Massoud  Adly '
                }, {
                    'id': '9065',
                    'value': 'Rana Wassef Abdelkader Youssef'
                }, {
                    'id': '4459',
                    'value': 'Randa Mohamed Elkhosht'
                }, {
                    'id': '8919',
                    'value': 'Raneem  Ahmed Mohamed Ahmed Elsanhory '
                }, {
                    'id': '2471',
                    'value': 'Rania Mamdouh Metwally Zidan'
                }, {
                    'id': '9251',
                    'value': 'Ranim Mahmoud Mohamed Mahmoud Mohamed'
                }, {
                    'id': '8305',
                    'value': 'Rasha Magdy Wahieb'
                }, {
                    'id': '2775',
                    'value': 'Rasha Mohamed Emara'
                }, {
                    'id': '6609',
                    'value': 'Rasha Saad Elsayed Mohamed Hassan'
                }, {
                    'id': '137',
                    'value': 'Rasha Sayed Hanafi'
                }, {
                    'id': '9384',
                    'value': 'Rawan Hesham Abdelwahed Abbas Elmasry'
                }, {
                    'id': '9397',
                    'value': 'Rawan Mohsen Abdelhalim Ibrahim'
                }, {
                    'id': '9177',
                    'value': 'Rawan Mostafa Ahmed Ahmed Mohamed'
                }, {
                    'id': '9173',
                    'value': 'Reem  Amr Ahmed Abdelmeguid '
                }, {
                    'id': '8651',
                    'value': 'Reem  Refaat Elsayed  Abdelhamid'
                }, {
                    'id': '8514',
                    'value': 'Reem Adel Mohamad Alansary'
                }, {
                    'id': '5505',
                    'value': 'Reem Ahmed Wagdy'
                }, {
                    'id': '6910',
                    'value': 'Reem Anas Haroun Abdel Meguid'
                }, {
                    'id': '7668',
                    'value': 'Reem Baheyeddin Mohammed Moursy'
                }, {
                    'id': '9429',
                    'value': 'Reem Fayez Messeha Philips'
                }, {
                    'id': '8440',
                    'value': 'Reem Gamal Awad Aboelgheit'
                }, {
                    'id': '6873',
                    'value': 'Reem Hassan Ibrahim'
                }, {
                    'id': '6794',
                    'value': 'Reem Hisham Ibrahim Allam'
                }, {
                    'id': '9319',
                    'value': 'Reem Hossam Eldin Ahmed Awad'
                }, {
                    'id': '7839',
                    'value': 'Reem Magdy Badawy Abdel Razik'
                }, {
                    'id': '7088',
                    'value': 'Reem Mohamed Sameh Ali Hassan Saleh'
                }, {
                    'id': '9144',
                    'value': 'Reem Mohyeldin Gawaby Mahmoud'
                }, {
                    'id': '6630',
                    'value': 'Reem Osama Ahmed Ashour'
                }, {
                    'id': '3650',
                    'value': 'Regine Eugenie Ritz'
                }, {
                    'id': '7626',
                    'value': 'Reham Ayman Saad Mohamed Hassan'
                }, {
                    'id': '8832',
                    'value': 'Reham Hatem Mohamed Mohamed Harraz'
                }, {
                    'id': '9223',
                    'value': 'Reham Khaled Mohamed Elsayed  Gomaa'
                }, {
                    'id': '72',
                    'value': 'Reham Mahmoud Abdel Kader'
                }, {
                    'id': '6879',
                    'value': 'Reham Waheed Ezzat Mahmoud Al Ajami'
                }, {
                    'id': '9107',
                    'value': 'Remon Gerges Hakim Gerges'
                }, {
                    'id': '9175',
                    'value': 'Remonda Ayman Abdou Awadallah'
                }, {
                    'id': '8511',
                    'value': 'Renad  Nabil Ahmed  Abdelhaleem '
                }, {
                    'id': '5444',
                    'value': 'Riham Essam Khattab'
                }, {
                    'id': '795',
                    'value': 'Rimon . Elias'
                }, {
                    'id': '9225',
                    'value': 'Rinada Mohamed Abdelaziz'
                }, {
                    'id': '5740',
                    'value': 'Rita Pinto De Freitas'
                }, {
                    'id': '8952',
                    'value': 'Rodaina Khaled Abdelmoniem Taha Mohamed Koraa'
                }, {
                    'id': '5156',
                    'value': 'Rodayna Mohamed Hossni'
                }, {
                    'id': '8532',
                    'value': 'Rola  Sameh Mahmoud  Abdelmoneim '
                }, {
                    'id': '8834',
                    'value': 'Roqaya Emad Fahmy Mohamed Hussin '
                }, {
                    'id': '6862',
                    'value': 'Ruairi Desmond O`brien'
                }, {
                    'id': '5978',
                    'value': 'Ryan Vincente Lee Grees'
                }, {
                    'id': '3611',
                    'value': 'Sabine . Muller'
                }, {
                    'id': '2346',
                    'value': 'Sahar Mohamed Abdel Maksoud'
                }, {
                    'id': '2518',
                    'value': 'Sahar Mohamed Abouzaid'
                }, {
                    'id': '3951',
                    'value': 'Sally . Hanna'
                }, {
                    'id': '6661',
                    'value': 'Sally Ann Skerrett'
                }, {
                    'id': '3290',
                    'value': 'Sally Mahmoud Salah Eldin Hassan Nafie'
                }, {
                    'id': '4435',
                    'value': 'Sally Mostafa Hamad'
                }, {
                    'id': '8921',
                    'value': 'Salma  Osama Kamel Mostafa  Kishk '
                }, {
                    'id': '9193',
                    'value': 'Salma  Sherif Elsayed Emam'
                }, {
                    'id': '7594',
                    'value': 'Salma  Yasser Tosson Mohamed Helmy'
                }, {
                    'id': '8581',
                    'value': 'Salma Alaa Eldeen Abouelyazeed Kotb'
                }, {
                    'id': '8838',
                    'value': 'Salma Ashraf Mostafa Shehata Awad'
                }, {
                    'id': '8596',
                    'value': 'Salma Elsayed Abdelmouniem Yousef'
                }, {
                    'id': '8936',
                    'value': 'Salma Hesham Zakaria Ibrahim Darwish'
                }, {
                    'id': '9172',
                    'value': 'Salma Hossam Abdallah  Salem'
                }, {
                    'id': '8276',
                    'value': 'Salma Hossam Eldin Helmy Ali Attia'
                }, {
                    'id': '9164',
                    'value': 'Salma Ismail Hassan Sabry Ismail'
                }, {
                    'id': '9403',
                    'value': 'Salma Khaled Abdelmoniem  Khashaba'
                }, {
                    'id': '9391',
                    'value': 'Salma Maher Mohamed Elsherif'
                }, {
                    'id': '8541',
                    'value': 'Salma Mahmoud Ahmed  Haggag '
                }, {
                    'id': '5183',
                    'value': 'Salma Mahmoud Sadek'
                }, {
                    'id': '8826',
                    'value': 'Salma Mohamed Hassan Hamdy Mohamed Swidan'
                }, {
                    'id': '6518',
                    'value': 'Salma Mohamed Shaaban Elshamy'
                }, {
                    'id': '7027',
                    'value': 'Salma Waheed Mohamed Mahmoud Abdel Atif'
                }, {
                    'id': '9312',
                    'value': 'Sama  Hussien Aboelola Abdelmwla'
                }, {
                    'id': '8943',
                    'value': 'Sama Alaa Gamaleldin Ahmed Elgedda'
                }, {
                    'id': '8852',
                    'value': 'Sama Ashraf Elsayed Ahmed Wahb'
                }, {
                    'id': '7265',
                    'value': 'Samaa Khaled Abdelkawy Abdelrahman'
                }, {
                    'id': '8991',
                    'value': 'Samaa Soliman Hussein Ahmed Shalaby '
                }, {
                    'id': '2784',
                    'value': 'Samah Adel Ibrahim'
                }, {
                    'id': '4166',
                    'value': 'Samar Mansour Holayel'
                }, {
                    'id': '5061',
                    'value': 'Samar Mohamed Shukry'
                }, {
                    'id': '9201',
                    'value': 'Samar SalahEldin Abdalaziz Abdalwahab Ghoneim'
                }, {
                    'id': '9392',
                    'value': 'Sameh Mohamed Reda Reyad  Ahmed'
                }, {
                    'id': '8886',
                    'value': 'Samer  Walaa Gabra Abdallah '
                }, {
                    'id': '6733',
                    'value': 'Samia Ali Kara Mohamed'
                }, {
                    'id': '4072',
                    'value': 'Samuel Safwat Hanna'
                }, {
                    'id': '8262',
                    'value': 'Samuel Yaser Jacob Basta'
                }, {
                    'id': '336',
                    'value': 'Saneya . El-Galaly'
                }, {
                    'id': '8614',
                    'value': 'Sara  Ahmed Farouk  Hammad '
                }, {
                    'id': '9105',
                    'value': 'Sara  Fathi Mohamed  Ibrahim'
                }, {
                    'id': '7255',
                    'value': 'Sara  Samy Abdelhakim Elfaramawy'
                }, {
                    'id': '7597',
                    'value': 'Sara Araby Ramadan Aly Mosa'
                }, {
                    'id': '4653',
                    'value': 'Sara Kamel Girgis Aboelkhair'
                }, {
                    'id': '6519',
                    'value': 'Sara Mahmoud Atwa'
                }, {
                    'id': '2739',
                    'value': 'Sara Mohamed El-Deeb'
                }, {
                    'id': '6777',
                    'value': 'Sara Mohamed Mohamed Elasy'
                }, {
                    'id': '6063',
                    'value': 'Sara Refaat Elmasry'
                }, {
                    'id': '5632',
                    'value': 'Sara Samy Khalifa'
                }, {
                    'id': '4823',
                    'value': 'Sara Shohdy Thabit'
                }, {
                    'id': '6606',
                    'value': 'Sara Tarek Ali Shehata'
                }, {
                    'id': '7077',
                    'value': 'Sarah Abdallah Mahmoud Alghorab'
                }, {
                    'id': '5646',
                    'value': 'Sarah Ismail Mohamed'
                }, {
                    'id': '6055',
                    'value': 'Sarah Mohamed Imam'
                }, {
                    'id': '4683',
                    'value': 'Sarah Mohamed Magdy'
                }, {
                    'id': '9109',
                    'value': 'Sarah Mohamed Yehia Aly Elhawary'
                }, {
                    'id': '4044',
                    'value': 'Sarah Mostafa Azzam'
                }, {
                    'id': '8844',
                    'value': 'Sarah Samir Elsayed Hassan Hassan Hussien '
                }, {
                    'id': '8750',
                    'value': 'Sarah Tarek Tahseen Shannon'
                }, {
                    'id': '9213',
                    'value': 'Sarah Yasser Mohsen Abdelaziz'
                }, {
                    'id': '9306',
                    'value': 'Seifeldin Hatem Hafez  Zeidan'
                }, {
                    'id': '3225',
                    'value': 'Shahir Ashraf Wagdy Aziz'
                }, {
                    'id': '8960',
                    'value': 'Shahira Mohamed Bilal Moahmed Saed Hatahet'
                }, {
                    'id': '9192',
                    'value': 'Shaimaa  Farid Mostafa  Abdelaziz'
                }, {
                    'id': '6121',
                    'value': 'Shaimaa Ali Zein Elabedin '
                }, {
                    'id': '5250',
                    'value': 'Shaimaa Reda Fayed'
                }, {
                    'id': '8598',
                    'value': 'Shehabedeen  Ashraf Ali Ibrahim '
                }, {
                    'id': '2831',
                    'value': 'Shereen Ekram Mohamed'
                }, {
                    'id': '8519',
                    'value': 'Shereen Moataz Mahmoud Mohamed Afifi'
                }, {
                    'id': '9007',
                    'value': 'Shereen Wael Mohamed Abdelrazeq'
                }, {
                    'id': '6529',
                    'value': 'Sherif Nasser Aref'
                }, {
                    'id': '9401',
                    'value': 'Sherifa Khaled Mursi Mohamed  Fawzy Ibrahim Hammoud'
                }, {
                    'id': '6853',
                    'value': 'Sherihan Omar Abou Zeid Mohamed Abou Zeid'
                }, {
                    'id': '8929',
                    'value': 'Shorook Mohamed Said Mostafa Soliman'
                }, {
                    'id': '7625',
                    'value': 'Silvana Ehab Anis Elkomos Tadros'
                }, {
                    'id': '3922',
                    'value': 'Silvia . Burger'
                }, {
                    'id': '7559',
                    'value': 'Silvia . Covarino'
                }, {
                    'id': '6841',
                    'value': 'Simone Sami Guergues Khalil'
                }, {
                    'id': '18',
                    'value': 'Slim . Abdennadher'
                }, {
                    'id': '8759',
                    'value': 'Soha Ahmed Elsayed  Abutaleb'
                }, {
                    'id': '9294',
                    'value': 'Sohaila Ahmed Mahmoud Hassanien'
                }, {
                    'id': '9428',
                    'value': 'Soheila Ossama Sobhi Ahmed'
                }, {
                    'id': '9305',
                    'value': 'Somaya  Mohamed Ali Amer Ahmed'
                }, {
                    'id': '7964',
                    'value': 'Sondoss Magdy Abd El Sattar Abdel Maboud'
                }, {
                    'id': '8994',
                    'value': 'Sonia Youssef Mohamed Hosny El Serafy'
                }, {
                    'id': '4656',
                    'value': 'Steffen . Scholl'
                }, {
                    'id': '142',
                    'value': 'Suzanne Ali Salah El-Din'
                }, {
                    'id': '8995',
                    'value': 'Suzi  Maher Wissa kaldas'
                }, {
                    'id': '7204',
                    'value': 'Sylvana Spiro Raphael Rizk'
                }, {
                    'id': '3778',
                    'value': 'Taher Mohamed Salaheldin'
                }, {
                    'id': '621',
                    'value': 'Tallal Osama Elshabrawy'
                }, {
                    'id': '8258',
                    'value': 'Tameem Mohamed Elshaheer Ahmed Hassan Mohamed Alghazaly'
                }, {
                    'id': '4475',
                    'value': 'Tamer Abbas Awad'
                }, {
                    'id': '9433',
                    'value': 'Tamer Ahmed Mostafa Mohammed Abdelkader'
                }, {
                    'id': '8633',
                    'value': 'Tarek  Safwat Aly  Mohamed '
                }, {
                    'id': '36',
                    'value': 'Tarek Abbas Metwally Khalil'
                }, {
                    'id': '119',
                    'value': 'Tarek Fouad Riad'
                }, {
                    'id': '8958',
                    'value': 'Taslima Ayman Mousa Mohamed Ismaiel'
                }, {
                    'id': '8631',
                    'value': 'Tasneem Nabil Mohammed Elghobashy'
                }, {
                    'id': '7168',
                    'value': 'Thanaa Hosni Hussein Awwad'
                }, {
                    'id': '6761',
                    'value': 'Theodora Ramzy Hanna Morkous'
                }, {
                    'id': '6953',
                    'value': 'Tobias Ulrich Wenig'
                }, {
                    'id': '8630',
                    'value': 'Toka Ossama Youssef Mahmoud Barghash'
                }, {
                    'id': '9285',
                    'value': 'Toqa Alaaeldeen Mahmoud Ahmed'
                }, {
                    'id': '9287',
                    'value': 'Toya Ahmed Hisham Mousa'
                }, {
                    'id': '9344',
                    'value': 'Tuuli Anna Miranda Saarelainen'
                }, {
                    'id': '2486',
                    'value': 'Ulrike . Breitinger'
                }, {
                    'id': '9248',
                    'value': 'Wael  Mohamed Hassan Shams Eldin'
                }, {
                    'id': '8727',
                    'value': 'Wael Zakaria Abdallah'
                }, {
                    'id': '4502',
                    'value': 'Wafaa Hussein Nadim'
                }, {
                    'id': '8497',
                    'value': 'Wagdy Anis Aziz'
                }, {
                    'id': '5644',
                    'value': 'Walaa Ahmed Saad Mahmoud'
                }, {
                    'id': '6542',
                    'value': 'Waled Sayed Abdelzaher Emam'
                }, {
                    'id': '4364',
                    'value': 'Walid Atef Hafez Omran'
                }, {
                    'id': '4984',
                    'value': 'Walid Mohamed Elshamy'
                }, {
                    'id': '3765',
                    'value': 'Wassim Joseph  Alexan'
                }, {
                    'id': '9198',
                    'value': 'Yahia  Eladl Elhendawi Mohamed Moussa Omran'
                }, {
                    'id': '7082',
                    'value': 'Yamen Hazem ElGamal'
                }, {
                    'id': '6776',
                    'value': 'Yara Abdelkhalek Mohamed Abdelsamad Elsehaimy'
                }, {
                    'id': '8862',
                    'value': 'Yara Ahmed Abdelhamid Emam Abobakr '
                }, {
                    'id': '7707',
                    'value': 'Yara Ayman Mahmoud Abdel Rahman'
                }, {
                    'id': '6749',
                    'value': 'Yara Galal Mohamed Shaban'
                }, {
                    'id': '7415',
                    'value': 'Yara Hany Mahmoud Abdelsalam Elsherif'
                }, {
                    'id': '6527',
                    'value': 'Yara Mohamed Taher Ismail'
                }, {
                    'id': '7709',
                    'value': 'Yara Talaat Hussein Ibrahim ElHarouni'
                }, {
                    'id': '8621',
                    'value': 'Yasmeen Sameh Abdeltawab  Abdelmoaty '
                }, {
                    'id': '8421',
                    'value': 'Yasmin  Hazem Abdelhafiz Mahmoud Elmously '
                }, {
                    'id': '8566',
                    'value': 'Yasmin  Zenhom Hesbelnaby Mohamed  Elshoura '
                }, {
                    'id': '7730',
                    'value': 'Yasmin Ahmed Mahmoud Amer Elbehiery'
                }, {
                    'id': '9335',
                    'value': 'Yasmin Mabrouk Saad Ahmed'
                }, {
                    'id': '4985',
                    'value': 'Yasmina Sherif Sarhan'
                }, {
                    'id': '8563',
                    'value': 'Yasmine  Hossam Khairy  Abouelhossein '
                }, {
                    'id': '6440',
                    'value': 'Yasser Mohamed Mahmoud Ahmed Ghandour Waly'
                }, {
                    'id': '8762',
                    'value': 'Yassin Mohamed Tharwat Elshazly Mohamed Elshazly'
                }, {
                    'id': '8876',
                    'value': 'Yassmine  Mohamed Abdalla Darwish  Haggag'
                }, {
                    'id': '8219',
                    'value': 'Yomna  Essam Abbas Halim Sorour '
                }, {
                    'id': '8877',
                    'value': 'Yomna Islam Youssef Alayary'
                }, {
                    'id': '9396',
                    'value': 'Yomna Mahmoud Ibrahim Hassan'
                }, {
                    'id': '9383',
                    'value': 'Yomna Mohamed Ali Mohamed Ali Moussa'
                }, {
                    'id': '6765',
                    'value': 'Yosra Adel Zaki Hassan Malek'
                }, {
                    'id': '8239',
                    'value': 'Youmna Abo Elfotouh Abdelmoaz Mabrouk'
                }, {
                    'id': '7645',
                    'value': 'Youmna Atef Abdel Azim Afifi Shehata'
                }, {
                    'id': '8059',
                    'value': 'Youmna Soliman Abd El-Hamid Soliman El-Sherbiny'
                }, {
                    'id': '9090',
                    'value': 'Yousef  Saeed Mohamed  Taha'
                }, {
                    'id': '9079',
                    'value': 'Yousef  Waleed Hassan Mohamed Abdelshafy'
                }, {
                    'id': '8866',
                    'value': 'Yousef Mohamed Adel Ebrahem Mahmoud Said'
                }, {
                    'id': '8920',
                    'value': 'Youssef  Ahmed Kamal Ismail  Abulyousr'
                }, {
                    'id': '8655',
                    'value': 'Youssef Abdelrahman Ahmed Abdelrazek'
                }, {
                    'id': '9196',
                    'value': 'Youssef George Albert Tawfilis'
                }, {
                    'id': '6521',
                    'value': 'Youssef Helmi Youssef Mohamed'
                }, {
                    'id': '8845',
                    'value': 'Youssef Ihab Awad Ismail  Ahmed '
                }, {
                    'id': '6899',
                    'value': 'Youssef Mohamed Ahmed Abou Shady'
                }, {
                    'id': '9269',
                    'value': 'Youssef Mohamed Ghazaly Sayed Ahmed'
                }, {
                    'id': '8521',
                    'value': 'Youstina  Ashraf Philip Botros '
                }, {
                    'id': '6496',
                    'value': 'Youstina Megalli Kamal'
                }, {
                    'id': '7331',
                    'value': 'youstina Samir Malak Mansour'
                }, {
                    'id': '9288',
                    'value': 'Zahraa Mohamed Abdelfatah Hassan Ali'
                }, {
                    'id': '5645',
                    'value': 'Zaynab Abdalaziz Awad Sadiq'
                }, {
                    'id': '8861',
                    'value': 'Zeina Ibrahim Ahmed Ahmed Ali Eissa'
                }, {
                    'id': '8667',
                    'value': 'Ziad  Mahmoud Mahmoud Elsayed  Gabr '
                }, {
                    'id': '9249',
                    'value': 'Ziad Ayman Salem Ali Hegazy'
                }];
            </script>
        </form>
        <script type="text/javascript" src="/CSS/architectui-html-pro-1.4.0/main.87c0748b313a1dda75f5.js"></script>
        <script src="/CSS/architectui-html-pro-1.4.0/jqueryV3.6.3.min.js"></script>
        <script>
            $(document).ready(function() {

                var x = $(location).attr('search');
                $('li.mm-active').closest('a.mm-active').removeClass('mm-active');
                $('li.mm-active').removeClass('mm-active');
                $('a[href="' + location.pathname + x + '"]').closest('li').addClass('mm-active');
                $('a[href="' + location.pathname + x + '"]').addClass('mm-active');
            });
        </script>
        <script>
            $('.app-container').addClass('closed-sidebar');
            //maintain postion scroll
            $(document).ready(function() {
                var div_position = $("#div_position");
                if (div_position.val() != 0) {
                    $(window).scrollTop(div_position.val());
                }
                $(window).scroll(function() {
                    div_position.val($(window).scrollTop());
                });
            });

            //hide fields from table
            $(document).ready(function() {
                $(".modal").each(function() {
                    $(this).attr("data-backdrop", "static");
                    $(this).attr("data-keyboard", "false");
                });
                $(".table").each(function() {
                    var ownerIndex = $(this).find('th:contains("ID")').index() + 1;
                    $(this).find('td:nth-child(' + ownerIndex + ')').hide();
                    $(this).find("tbody tr, thead tr").children(":nth-child(" + ownerIndex + ")").hide()

                    var ownerIndex2 = $(this).find('th:contains("URL")').index() + 1;
                    $(this).find('td:nth-child(' + ownerIndex2 + ')').hide();
                    $(this).find("tbody tr, thead tr").children(":nth-child(" + ownerIndex2 + ")").hide()

                    var ownerIndex3 = $(this).find('th:contains("SeasonId")').index() + 1;
                    $(this).find('td:nth-child(' + ownerIndex3 + ')').hide();
                    $(this).find("tbody tr, thead tr").children(":nth-child(" + ownerIndex3 + ")").hide()

                });

                $(".table").each(function() {
                    var newul = $(this).parent().attr("Style", "overflow-x: auto; ")
                });

                $(".dropdown-menu-header").click(function() {

                    $(this).next(".card-body").toggle("slow");

                });
                $('.app-container').addClass('closed-sidebar');

            });
        </script>
        <script type=text/javascript src="/CSS/oldJavaScript/home.js"></script>
        <script>
            $(document).ready(function() {
                var expanded = false;
                var menu = $('#menu').html();

                $('#search-input').on('input', function() {
                    var searchQuery = $(this).val();
                    searchMenu(searchQuery);
                    if (expanded == false) {
                        $('#menu li').each(function() {
                            if (!$(this).eq(0).hasClass('mm-active')) {
                                $(this).find('a').attr("aria-expanded", "true");
                                $(this).find('ul').addClass("mm-show");
                            }
                        });
                        expanded = true;
                    }
                    if (searchQuery == "" && expanded == true) {
                        $('#menu li').each(function() {
                            if (!$(this).eq(0).hasClass('mm-active')) {
                                $(this).find('a').attr("aria-expanded", "false");
                                $(this).find('ul').removeClass("mm-show");
                            }
                            expanded = false;
                        });
                    }
                });
            });

            function searchMenu(query) {

                // Loop through all menu items
                $('#menu li').each(function() {

                    // Get the text content of the menu item
                    var text = $(this).text().toLowerCase();

                    // If the text content contains the search query, show the menu item
                    if (text.indexOf(query.toLowerCase()) >= 0) {

                        $(this).show();
                    } else {

                        $(this).hide();
                    }
                });
            }
        </script>
        <script type="text/javascript" language="javascript">
            $(document).ready(function() {
                $(document).on("keydown", disableF5);
            });
            function disableF5(e) {
                if ((e.which || e.keyCode) == 116)
                    e.preventDefault();
            }
            ;</script>
    </body>
</html>
