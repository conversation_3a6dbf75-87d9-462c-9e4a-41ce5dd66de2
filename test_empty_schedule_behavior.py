#!/usr/bin/env python3
"""
Test script to verify the empty schedule behavior returns empty array.
"""

import sys
import os

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from api.schedule import is_schedule_empty

def test_user_example_schedule():
    """Test with the exact schedule structure the user provided"""
    user_schedule = {
        "Monday": {
            "Fifth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "First Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Fourth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Second Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Third Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            }
        },
        "Saturday": {
            "Fifth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "First Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Fourth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Second Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Third Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            }
        },
        "Sunday": {
            "Fifth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "First Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Fourth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Second Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Third Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            }
        },
        "Thursday": {
            "Fifth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "First Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Fourth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Second Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Third Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            }
        },
        "Tuesday": {
            "Fifth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "First Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Fourth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Second Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Third Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            }
        },
        "Wednesday": {
            "Fifth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "First Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Fourth Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Second Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            },
            "Third Period": {
                "Course_Name": "",
                "Location": "Unknown",
                "Type": "Lecture"
            }
        }
    }
    
    result = is_schedule_empty(user_schedule)
    print(f"User's example schedule (all empty course names): {result}")
    assert result == True, "User's schedule with all empty course names should return True"

if __name__ == "__main__":
    print("Testing empty schedule behavior...")
    
    try:
        test_user_example_schedule()
        print("\n✅ Test passed! The user's schedule will now return an empty array [] instead of the full structure.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
